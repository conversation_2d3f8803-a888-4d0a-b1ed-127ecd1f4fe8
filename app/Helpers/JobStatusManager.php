<?php

namespace App\Helpers;

use App\Models\JobBatch;
use Carbon\Carbon;
use Illuminate\Queue\Events\JobExceptionOccurred;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;
use Imtigger\LaravelJobStatus\EventManagers\EventManager;

class JobStatusManager extends EventManager
{
    private $batchModel;

    public function before(JobProcessing $event): void
    {
        // Job Batch before
        if ($batchId = $this->getBatchId($event)) {
            $this->batchModel = JobBatch::findOrFail($batchId);

            $this->batchModel->update([
            'status' => JobBatch::STATUS_EXECUTING,
            'cancelled_at' => null,
        ]);

            // Job Status before
            $this->getUpdater()->update($event, [
            'job_id' => $event->job->uuid(),
            'queue' => $event->job->getQueue(),
            'job_batch_id' => $this->batchModel->id,
            'status' => $this->getEntity()::STATUS_EXECUTING,
            'started_at' => Carbon::now(),
        ]);
        }
    }

    public function after(JobProcessed $event): void
    {
        if ($this->batchModel) {
            $this->getUpdater()->update($event, [
                'status' => $this->getEntity()::STATUS_SUCCEED,
                'finished_at' => Carbon::now(),
            ]);
        }
    }

    public function failing(JobFailed $event): void
    {
        if ($this->batchModel) {
            $this->getUpdater()->update($event, [
                'status' => $this->getEntity()::STATUS_FAILED,
                'finished_at' => Carbon::now(),
            ]);

            if($this->batchModel->status !== JobBatch::STATUS_SUCCEED) {
                $this->batchModel->update([
                    'status' => $this->getFailedStatus($event),
                    'cancelled_at' => now()->timestamp,
                ]);
            }
        }
    }

    public function exceptionOccurred(JobExceptionOccurred $event): void
    {
        if ($this->batchModel) {
            $this->getUpdater()->update($event, [
                'status' => $this->getEntity()::STATUS_FAILED,
                'finished_at' => Carbon::now(),
                'output' => [
                    'message' => $event->exception->getMessage()
                ],
            ]);

            if($this->batchModel->status !== JobBatch::STATUS_SUCCEED) {
                $this->batchModel->update([
                    'status' => $this->getFailedStatus($event),
                ]);
            }
        }
    }

    private function getFailedStatus($event)
    {
        return JobBatch::STATUS_FAILED;
    }

    private function getJob($event)
    {
        try {
            $payload = $event->job->payload();

            return unserialize($payload['data']['command']);
        } catch (\Throwable $e) {
            return null;
        }
    }

    private function getBatchId($event)
    {
        try {
            return $this->getJob($event)?->batchId;
        } catch (\Throwable $e) {
            return null;
        }
    }
}
