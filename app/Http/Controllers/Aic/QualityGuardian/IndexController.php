<?php

namespace App\Http\Controllers\Aic\QualityGuardian;


use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class IndexController extends Controller
{
  public function index(Request $request)
  {
    if(!auth()->check()) {
      return redirect()->route('aic.login');
    }
    $user = auth()->user();
    $requester_proxy_email = match(config('app.name')) {
      'Laravel' => '<EMAIL>',
      'AIC-STG' => '<EMAIL>',
      'AIC-DEV' => '<EMAIL>',
      'QG-MIRROR' => '<EMAIL>',
      'quality-guardiam' => '<EMAIL>',
    };

    $requester_proxy = User::select('last_name', 'first_name', 'email', 'department', 'user_type_id')->where('email', $requester_proxy_email)->first();
    $requester_proxy->department = 'コーポレートディビジョン　法務ブランド保証部　ブランド保証課';
    return Inertia::render('Aic/QualityGuardiam/Index', ['user' => $user ,'requester_proxy' => $requester_proxy]);
  }

  public function manual(Request $request)
  {
    if(!auth()->check()) {
      return redirect()->route('aic.login');
    }
    return Inertia::render('Aic/QualityGuardiam/Manual');
  }
}
