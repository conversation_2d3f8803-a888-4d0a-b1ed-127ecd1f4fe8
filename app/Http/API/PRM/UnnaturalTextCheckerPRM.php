<?php

namespace App\Http\API\PRM;

use Illuminate\Support\Facades\Storage;
use App\Helpers\EthicsConfirmAssistantHelper;
use App\Models\Proof;

/**
 * 不自然な文章チェッカーリコメンドAPI
 */
class UnnaturalTextCheckerPRM
{
    public function predicts(Proof $proof)
    {
        try {
            $key = EthicsConfirmAssistantHelper::unnaturalTextCheckCacheFilename($proof);

            return json_decode(Storage::disk('s3')->get($key), true);
        } catch (\Throwable $th) {
            return [];
        }
    }
}
