<?php

namespace App\Actions\Statistics;

use App\Models\Proof;
use Illuminate\Database\Query\Expression;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GetRequestedProofCountAction
{
    private string $startColumn = 'created_at';
    private string $endColumn   = 'external_deadline_at';
    private string $infixKey    = '_';

    const PerPage = 30;

    public function execute()
    {
        $paginator = $this->getPaginatorByDistinctDate();

        return tap($paginator, function (Paginator $paginator) {
            $data = $this
                    ->getProofsByDistinctDate($paginator->items())
                    ->pipe(fn (Collection $proofs) => $this->getGroupByRequestedProofs($proofs))
                    ->values();

            $paginator->setCollection($data);
        });
    }

    private function getPaginatorByDistinctDate(): Paginator
    {
        return Proof::query()
                ->select([
                    $this->queryCastToDate($this->startColumn),
                    $this->queryCastToDate($this->endColumn),
                ])
                ->distinct()
                ->latest($this->startColumn)
                ->simplePaginate(self::PerPage);
    }

    /**
     * get proofs by distinct date
     *
     * @param array $dates
     *
     * @return Collection
     */
    private function getProofsByDistinctDate(array $dates): Collection
    {
        if(empty($dates)) {
            return collect([]);
        }

        $whereInValues = collect($dates)
                        ->map(fn ($date) =>"('{$date[$this->startColumn]}', '{$date[$this->endColumn]}')")
                        ->join(',');

        return  Proof::without(['members', 'user', 'status', 'categories'])
                ->select([
                    'id', 'title',
                    $this->queryCastToDate($this->startColumn),
                    $this->queryCastToDate($this->endColumn),
                ])
                ->whereRaw("
                    (DATE({$this->startColumn}), DATE({$this->endColumn})) IN ({$whereInValues})
                ")
                ->latest($this->startColumn)
                ->get();
    }

    /**
     *
     * @param string $column
     *
     * @return Expression
     */
    private function queryCastToDate(string $column): Expression
    {
        return DB::raw("CAST({$column} AS DATE) as {$column}");
    }

    private function getGroupByRequestedProofs(Collection $proofs): Collection
    {
        return $proofs
                ->mapToGroups(fn (Proof $proof) => [
                    $this->getKey($proof) => $proof
                ])
                ->map(function (Collection $proofs, string $key) {
                    list($start, $end) = $this->explodeKey($key);

                    $details = $proofs->map(
                        fn (Proof $proof) => "{$proof->id}: {$proof->title}"
                    );

                    return [
                        'name' => "【倫理確認】{$details->count()}件",
                        'details' => $details,
                        'start' => $start,
                        'end' => $end,
                    ];
                })
                ->sortByDesc('start');
    }

    private function getKey(Proof $proof): string
    {
        return implode($this->infixKey, [
            $proof->getRawOriginal($this->startColumn),
            $proof->getRawOriginal($this->endColumn),
        ]);
    }

    /**
     * @param string $key
     * @return array
     */
    private function explodeKey(string $key): array
    {
        return explode($this->infixKey, $key);
    }
}
