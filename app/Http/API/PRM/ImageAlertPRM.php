<?php

namespace App\Http\API\PRM;

use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Storage;
use App\Http\API\PRM\Contracts\PRM;
use App\Enums\PRMEnum;
use App\Helpers\ImageAlertExamHelper;
use App\Models\ProofFile;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * 画像アラートリコメンドAPI
 * Replicateベース
 */
class ImageAlertPRM extends PRM
{
    public function __construct()
    {
    }
    
    public function predict(string $text): array
    {
        return [];
    }
    
    public function predicts(ProofFile $proofFile)
    {
        // APIのレスポンスファイルを探す
        $api_version_no = config('image-alert.version_no');
        $cache_base_dir = config('filesystems.disks.s3.image_alert_cache_dir');
        
        $cache_key = $this->getKey($api_version_no, PRMEnum::ImageAlert, $proofFile->proof_id);
        $path = "{$cache_base_dir}/{$cache_key}/response.json";

        if (Storage::disk('s3')->exists($path) == true) {
            $content = Storage::disk('s3')->get($path);
            $content_json = json_decode($content, true);
            $alerted_images = $content_json["alerted_images"];
        }
        else {
            $error_response["response file open error"] = "リコメンドファイルがありませんでした。";
            return $error_response;
        }
        
        $response = ["result" => "success", "alerted_images" => $alerted_images ];

        return $response;
    }

    public function annotationSamples(): array
    {
        return [];
    }

    public function annotations(string|int $class_no): array
    {
        return [];
    }

    public function files(string|int $class_no)
    {
    }

    public function version()
    {
        return "0.9";
    }

   /**
    * キャッシュ用のキーの取得（現状はディレクトリ名として利用）
    */
    private function getKey(string $version, string $suffix, string $proof_id): string
    {
        return sprintf(
            "%s.%s.%s",
            $version,
            $suffix,
            $proof_id
        );
    }
}
