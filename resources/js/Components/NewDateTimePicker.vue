<template>
  <v-dialog v-model="display" :width="dialogWidth">
    <template v-slot:activator="{ on }">
      <v-text-field
        v-bind="textFieldProps"
        :disabled="disabled"
        :loading="loading"
        :label="label"
        :value="formattedDatetime"
        v-on="on"
        readonly
        prepend-inner-icon="mdi-calendar-clock"
      >
        <template v-slot:progress>
          <slot name="progress">
            <v-progress-linear
              color="primary"
              indeterminate
              absolute
              height="2"
            ></v-progress-linear>
          </slot>
        </template>

        <template #label>
          <slot name="label" />
        </template>
      </v-text-field>
    </template>

    <v-card>
      <v-card-text class="px-0 py-0">
        <v-tabs fixed-tabs v-model="activeTab">
          <v-tab key="calendar">
            <slot name="dateIcon">
              <v-icon>mdi-calendar-month </v-icon>
            </slot>
          </v-tab>
          <v-tab key="timer" :disabled="dateSelected">
            <slot name="timeIcon">
              <v-icon>mdi-calendar-clock</v-icon>
            </slot>
          </v-tab>
          <v-tab-item key="calendar">
            <v-date-picker
              v-model="date"
              v-bind="datePickerProps"
              @input="showTimePicker"
              full-width
              locale="ja-jp"
              scrollable
              :dayFormat="date => new Date(date).getDate()"
            ></v-date-picker>
          </v-tab-item>
          <v-tab-item key="timer" class="ma-2 pa-1">
            <v-select
              ref="timer"
              v-model="time"
              :items="allowedHours"
              item-text="display"
              item-value="value"
              dense
              outlined
              hide-details
            />
          </v-tab-item>
        </v-tabs>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <slot name="actions" :parent="this">
          <v-btn color="grey lighten-1" text @click.native="clearHandler">
            終了
          </v-btn>
          <v-btn color="green darken-1" text @click="okHandler">
            確認
          </v-btn>
        </slot>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { format, parse } from 'date-fns';

const DEFAULT_DATE = '';
const DEFAULT_TIME = '00:00';
const DEFAULT_DATE_FORMAT = 'yyyy-MM-dd';
const DEFAULT_TIME_FORMAT = 'HH:mm';
const DEFAULT_DIALOG_WIDTH = 340;
const DEFAULT_CLEAR_TEXT = 'CLEAR';
const DEFAULT_OK_TEXT = 'OK';

export default {
  name: 'v-datetime-picker-new',
  model: {
    prop: 'datetime',
    event: 'input',
  },
  props: {
    datetime: {
      type: [Date, String],
      default: null,
    },
    disabled: {
      type: Boolean,
    },
    loading: {
      type: Boolean,
    },
    label: {
      type: String,
      default: '',
    },
    dialogWidth: {
      type: Number,
      default: DEFAULT_DIALOG_WIDTH,
    },
    dateFormat: {
      type: String,
      default: DEFAULT_DATE_FORMAT,
    },
    timeFormat: {
      type: String,
      default: 'HH:mm',
    },
    clearText: {
      type: String,
      default: DEFAULT_CLEAR_TEXT,
    },
    okText: {
      type: String,
      default: DEFAULT_OK_TEXT,
    },
    textFieldProps: {
      type: Object,
    },
    datePickerProps: {
      type: Object,
    },
    timePickerProps: {
      type: Object,
    },
    allowedTime: {
      type: Object,
      default() {
        return {
          from: DEFAULT_TIME,
          to: '23:30',
        };
      },
    },
    defaultDate: {
      type: String,
      default: DEFAULT_DATE,
    },
    defaultTime: {
      type: String,
      default: DEFAULT_TIME,
    },
  },
  data() {
    return {
      display: false,
      activeTab: 0,
      date: this.defaultDate,
      time: this.defaultTime,
      allowedHours: [],
    };
  },
  mounted() {
    this.init();
  },
  computed: {
    dateTimeFormat() {
      return this.dateFormat + ' ' + this.timeFormat;
    },
    defaultDateTimeFormat() {
      return DEFAULT_DATE_FORMAT + ' ' + DEFAULT_TIME_FORMAT;
    },
    formattedDatetime() {
      return this.selectedDatetime
        ? format(this.selectedDatetime, this.dateTimeFormat)
        : '';
    },
    selectedDatetime() {
      if (this.date && this.time) {
        let datetimeString = this.date + ' ' + this.time;
        if (this.time.length === 5) {
          // datetimeString += ':00';
        }

        return parse(datetimeString, this.defaultDateTimeFormat, new Date());
      } else {
        return null;
      }
    },
    dateSelected() {
      return !this.date;
    },
  },
  methods: {
    init() {
      if (!this.datetime) {
        this.date = this.defaultDate;
        this.time = this.defaultTime;
        return;
      }
      let initDateTime;
      if (this.datetime instanceof Date) {
        initDateTime = this.datetime;
      } else if (
        typeof this.datetime === 'string' ||
        this.datetime instanceof String
      ) {
        initDateTime = new Date(this.datetime);
      }

      this.date = format(initDateTime, DEFAULT_DATE_FORMAT);
      this.time = format(initDateTime, DEFAULT_TIME_FORMAT);
    },
    okHandler() {
      if (this.selectedDatetime) {
        const datetime = this.$moment(this.selectedDatetime).format(
          'YYYY-MM-DD HH:mm',
        );

        this.$emit('input', datetime);
      }

      this.resetPicker();
    },
    clearHandler() {
      this.resetPicker();
      this.date = DEFAULT_DATE;
      this.time = DEFAULT_TIME;
      this.$emit('input', null);
    },
    resetPicker() {
      this.display = false;
      this.activeTab = 0;
      if (this.$refs.timer) {
        this.$refs.timer.selectingHour = true;
      }
    },
    showTimePicker() {
      this.activeTab = 1;
    },
    initBusinessHours() {
      //adding hour gap
      var hours = [];
      let hourFrom = this.allowedTime.from;
      let hourTo = this.allowedTime.to;
      let minuteFrom = hourFrom.substring(3, 5);
      let minuteTo = hourTo.substring(3, 5);

      for (var i = parseInt(hourFrom); i <= parseInt(hourTo); i++) {
        //first hour
        if (i == parseInt(hourFrom)) {
          var displayhour = hourFrom.toString().substring(0, 5);
          var from = { display: displayhour, value: hourFrom };
          hours.push(from);

          if (minuteFrom == '00') {
            var displayhour =
              String(i)
                .padStart(2, '0')
                .toString() + ':30';
            var from = {
              display: displayhour,
              value: this.$moment({ hours: i, minute: 30 }).format(
                DEFAULT_TIME_FORMAT,
              ),
            };
            hours.push(from);
          }
        } else {
          //hours in the middle of from-to
          var displayhour =
            String(i)
              .padStart(2, '0')
              .toString() + ':00';
          var hour00 = {
            display: displayhour,
            value: this.$moment({ hours: i }).format(DEFAULT_TIME_FORMAT),
          };
          hours.push(hour00);

          //if last hour ends up in 30
          if (
            i < parseInt(hourTo) ||
            (i == parseInt(hourTo) && minuteTo == '30')
          ) {
            var displayhour30 =
              String(i)
                .padStart(2, '0')
                .toString() + ':30';
            var hour30 = {
              display: displayhour30,
              value: this.$moment({ hours: i, minute: 30 }).format(
                DEFAULT_TIME_FORMAT,
              ),
            };
            hours.push(hour30);
          }
        }
      }

      return hours;
    },
  },
  watch: {
    datetime: function() {
      this.init();
    },
  },
  created() {
    this.allowedHours = this.initBusinessHours();
  },
};
</script>
