<?php

namespace App\Http\Requests\Admin\ProofBusinessCategoryController;

use App\Data\Admin\Proof\ProofBusinessCategoryData;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                "required",
                "string",
                "max:255",
                Rule::unique('proof_business_categories')
            ],
            'description' => [
                "nullable",
                "string",
            ]
        ];
    }

    public function data(): ProofBusinessCategoryData
    {
        return ProofBusinessCategoryData::from($this->validated());
    }

    public function attributes()
    {
        return [
            'name' => '業務分類名',
            'description' => '業務分類説明',
        ];
    }
}
