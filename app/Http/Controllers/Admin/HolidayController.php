<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\HolidayController\StoreRequest;
use App\Models\Holiday;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class HolidayController extends Controller
{
    public function index()
    {
        $filters = array_merge([
            'year' => now()->year
        ], request()->query());

        $holidays = Holiday::filter($filters)
            ->orderBy('date')
            ->get();

        return Inertia::render('Admin/Holiday/AicIndex', [
            'year' => (int)$filters['year'],
            'holidays' => $holidays
        ]);
    }

    public function store(StoreRequest $request)
    {
        $validated = $request->validated();

        DB::beginTransaction();

        try {
            $holiday = Holiday::create(array_merge($validated, [
                'is_manual' => true
            ]));
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }

        DB::commit();

        return redirect()
            ->back(self::HTTP_STATUS_SEE_OTHER)
            ->with('snackbar', [
                'color' => 'primary',
                'icon' => 'mdi-check-circle',
                'title' => '成功',
                'message' => "「{$holiday->name}」を祝日で登録しました"
            ]);
    }

    public function delete(Holiday $holiday)
    {
        DB::beginTransaction();

        try {
            $holiday->delete();
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }

        DB::commit();

        return redirect()
            ->back(self::HTTP_STATUS_SEE_OTHER)
            ->with('snackbar', [
                'color' => 'primary',
                'icon' => 'mdi-check-circle',
                'title' => '成功',
                'message' => "「{$holiday->name}」を削除しました"
            ]);
    }
}
