<?php

namespace App\Http\Controllers\Admin;

use App\Actions\UpsertNoticeAction;
use App\Data\UpsertNoticeData;
use App\Http\Controllers\Controller;
use App\Models\Notice;
use App\Models\UserType;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class NoticeController extends Controller
{
    public function index()
    {
        $paginator = Notice::filter(request()->all())
                    ->with(['user_type'])
                    ->latest('visible')
                    ->latest('updated_at')
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

        $userTypes = UserType::all();

        return Inertia::render('Admin/Notice/Index', [
            'paginator' => $paginator,
            'userTypes' => $userTypes
        ]);
    }

    public function aicIndex()
    {
        $paginator = Notice::filter(request()->all())
            ->with(['user_type'])
            ->latest('id')
            ->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $userTypes = UserType::all();


        // userTypesのtextを変換
        // valueがadminならtextをメンバー、memberならサポーター、requesterなら申請者に変換
        $userTypes = $userTypes->map(function ($userType) {
            if ($userType->value === 'admin') {
                $userType->text = 'メンバー';
            } elseif ($userType->value === 'member') {
                $userType->text = 'サポーター';
            } elseif ($userType->value === 'requester') {
                $userType->text = '申請者';
            }
            return $userType;
        });

        // 同様にpaginatorのuser_typeのtextを変換
        $paginator->getCollection()->transform(function ($notice) {
            if ($notice->user_type->value === 'admin') {
                $notice->user_type->text = 'メンバー';
            } elseif ($notice->user_type->value === 'member') {
                $notice->user_type->text = 'サポーター';
            } elseif ($notice->user_type->value === 'requester') {
                $notice->user_type->text = '申請者';
            }
            return $notice;
        });

        //publish_atとclose_atを日付フォーマットに変換
        $paginator->getCollection()->transform(function ($notice) {
            $notice->publish_at = Carbon::create($notice->publish_at)->format('Y-m-d H:i');
            $notice->close_at = Carbon::create($notice->close_at)->format('Y-m-d H:i');
            Log::info("notice", ['notice' => $notice]);
            return $notice;
        });


        return Inertia::render('Admin/Notice/AicIndex', [
            'paginator' => $paginator,
            'userTypes' => $userTypes
        ]);
    }

    public function oldIndex()
    {
        $paginator = Notice::filter(request()->all())
            ->with(['user_type'])
            ->latest('visible')
            ->latest('updated_at')
            ->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $userTypes = UserType::all();

        return Inertia::render('Admin/Notice/Index', [
            'paginator' => $paginator,
            'userTypes' => $userTypes
        ]);
    }

    public function create()
    {
        $userTypes = UserType::all();

        $userTypes = $userTypes->map(function ($userType) {
            if ($userType->value === 'admin') {
                $userType->text = 'メンバー';
            } elseif ($userType->value === 'member') {
                $userType->text = 'サポーター';
            } elseif ($userType->value === 'requester') {
                $userType->text = '申請者';
            }
            return $userType;
        });

        return Inertia::render('Admin/Notice/AicCreate', [
            'userTypes' => $userTypes
        ]);
    }

    public function oldCreate()
    {
        $userTypes = UserType::all();

        return Inertia::render('Admin/Notice/Create', [
            'userTypes' => $userTypes
        ]);
    }

    public function show(Notice $notice)
    {
        $userTypes = UserType::all();

        $userTypes = $userTypes->map(function ($userType) {
            if ($userType->value === 'admin') {
                $userType->text = 'メンバー';
            } elseif ($userType->value === 'member') {
                $userType->text = 'サポーター';
            } elseif ($userType->value === 'requester') {
                $userType->text = '申請者';
            }
            return $userType;
        });

        return Inertia::render('Admin/Notice/AicShow', [
            'notice' => $notice,
            'userTypes' => $userTypes
        ]);
    }

    public function OldShow(Notice $notice)
    {
        $userTypes = UserType::all();

        return Inertia::render('Admin/Notice/Show', [
            'notice' => $notice,
            'userTypes' => $userTypes
        ]);
    }

    public function store(
        UpsertNoticeData $data,
        UpsertNoticeAction $action
    ) {
        try {
            $notice = DB::transaction(function () use ($data, $action) {
                return $action->execute($data);
            });
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }

        return redirect()->route('admin.notices.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $notice->title . 'を登録しました。'
                ]);
    }

    public function update(
        Notice $notice,
        UpsertNoticeData $data,
        UpsertNoticeAction $action
    ) {
        Log::info("notice", ['notice' => $notice]);
        Log::info("data", ['data' => $data]);

        try {
            $notice = DB::transaction(function () use ($data, $action) {
                return $action->execute($data);
            });
        } catch (\Throwable $th) {
            throw $th;

        }

        return redirect()->route('admin.notices.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $notice->title . 'を更新しました。'
                ]);
    }

    public function delete(Notice $notice)
    {
        try {
            DB::transaction(function () use ($notice) {
                return $notice->delete();
            });
        } catch (\Throwable $th) {
            throw $th;
        }

        return redirect()
            ->route('admin.notices.index', [], self::HTTP_STATUS_SEE_OTHER)
            ->with('snackbar', [
                'color' => 'primary',
                'icon' => 'mdi-check-circle',
                'title' => '成功',
                'message' => "「{$notice->title}」を削除しました"
            ]);
    }
}
