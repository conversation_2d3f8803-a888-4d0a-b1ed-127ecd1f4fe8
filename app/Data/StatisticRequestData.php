<?php

namespace App\Data;

use App\Enums\StatisticsUnitEnum;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Spatie\LaravelData\Data;

class StatisticRequestData extends Data
{
    public function __construct(
        public StatisticsUnitEnum $unit,
        public DateRangeData $range,
    ) {
    }

    public static function fromRequest(Request $request): static
    {
        $unit = $request->input('unit', StatisticsUnitEnum::Day);

        return self::from([
            'unit' => StatisticsUnitEnum::fromValue($unit),
            'range' => DateRangeData::from([
                'from' => $request->input('range.from'),
                'to' => $request->input('range.to'),
            ])
        ]);
    }

    public static function rules(): array
    {
        return [
            'unit' => [
                'sometimes',
                Rule::in(StatisticsUnitEnum::getValues())
            ],
            'range' => [
                'sometimes',
                'array'
            ],
            'range.from' => [
                'date'
            ],
            'range.to' => [
                'date'
            ]
        ];
    }
}
