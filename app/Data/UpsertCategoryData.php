<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;

class UpsertCategoryData extends Data
{
    public function __construct(
        public ?int $id,
        public string $name,
        public string $short_name,
    ) {
    }

    public static function rules(): array
    {
        $id = request()->input('id');

        return [
            'id' => [
                'nullable',
                'sometimes',
                'exists:categories,id'
            ],
            'name' => [
                'required',
                'string',
                $id ? 'unique:categories,name,' . $id : 'unique:categories,name',
            ],
            'short_name' => [
                'required',
                'string',
                $id ? 'unique:categories,short_name,' . $id : 'unique:categories,short_name',
            ],
        ];
    }

    public static function attributes(): array
    {
        return [
            'name' => '名前',
            'short_name' => '略称',
        ];
    }
}
