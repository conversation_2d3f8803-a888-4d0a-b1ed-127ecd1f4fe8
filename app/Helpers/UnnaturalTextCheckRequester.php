<?php

namespace App\Helpers;

use App\Models\Proof;
use App\Models\ProofFile;
use App\Enums\PRMEnum;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\Response;

// AI用サーバ(Replicate)へのリクエストヘルパークラス
class UnnaturalTextCheckRequester
{
    const SLEEP_SEC = 60;
    const RETRY_MAX = 10;

    private $text;
    private $recieve_id;

    public function __construct(Proof $proof, string $text, int $page_no)
    {
        $this->proof = $proof;
        $this->text = $text;
        $this->page_no = $page_no;
        $this->recieve_id = "";
    }

    public function requet_and_polling()
    {
        $result = [
            "status" => "",    // 処理結果
            "error" => ""    // エラー内容
        ];
        
        $proofFile = ProofFile::where('proof_id', $this->proof->id)->where('type', ProofFile::TYPE_SHARED)->first();
        if (!$proofFile) {
            Log::debug("ProofFile:error {$this->proof->id} is not found");
            $result["status"] = "failed";
            $result["error"] = "invalid proof id";
            return $result;
        }

        // 処理開始API
        for ($cnt = 0; $cnt < self::RETRY_MAX; $cnt++) {
            $response = Http::withHeaders([
                'Authorization' => 'Token ' . config('unnatural-text-check.auth_token')
            ])
            ->post(config('unnatural-text-check.url'). '/v1/predictions', [
                "version" => config('unnatural-text-check.version'),
                "input" => [
                    'text' => $this->text
                ]
            ]);
    
            $res_json = $response->json();
            if ($res_json["status"] === "starting") {
                $this->recieve_id = $res_json["id"];
                break;
            }
            else if ($res_json["status"] === "succeeded") {
                $this->success($response);
                $result["status"] = $res_json["status"];
                return $result;
            }
            
            sleep(self::SLEEP_SEC);
        }

        if ($cnt >= self::RETRY_MAX) {
            $result["status"] = "failed";
            $result["error"] = "retry max";
            return $result;
        }
        
        // 結果受け取りAPI

        for ($cnt = 0; $cnt < self::RETRY_MAX; $cnt++) {
            $response = Http::withHeaders(['Authorization' => 'Token ' . config('unnatural-text-check.auth_token')])
                ->get(config('unnatural-text-check.url') . '/v1/predictions/' . $this->recieve_id);
    
            $res_json = $response->json();

            if ($res_json !== null) {
                if ($res_json["status"] === "succeeded") {
                    $this->success($response);
                    $result["status"] = $res_json["status"];
                    return $result;
                }
                else if ($res_json["status"] === "failed") {
                    $result["status"] = $res_json["status"];
                    if ($res_json["error"]) {
                        $result["error"] = $res_json["error"];
                    }
                    return $result;
                }
                else if ($res_json["status"] === "canceled") {
                    $result["status"] = $res_json["status"];
                    return $result;
                }
            }

            sleep(self::SLEEP_SEC);
        }

        $result["status"] = "failed";
        $result["error"] = "retry max";
        return $result;
    }

    private function getKey(string $version, string $suffix, string $proof_id, int $page_no): string
    {
        return sprintf(
            "%s.%s.%s.%s",
            $version,
            $suffix,
            $proof_id,
            $page_no
        );
    }

    /*
     * API成功時処理
     */
    private function success(Response $response) {
        $api_version_no = config('unnatural-text-check.version_no');
        $cache_dir = config('filesystems.disks.s3.unnatural_text_check_response_dir');
        
        $content = $response->body();

        $cache_key = $this->getKey($api_version_no, PRMEnum::UnnaturalTextChecker, $this->proof->id, $this->page_no);

        $path = "{$cache_dir}/{$cache_key}.json";
        Storage::disk('s3')->put($path, $content);
    }
}
