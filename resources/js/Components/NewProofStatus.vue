<template>
  <v-progress-linear
    v-if="progressing"
    :value="batchData.progress"
    class="progress"
    color="primary"
    height="40"
    rounded
  >
    <template v-slot:default="{ value }">
      <v-progress-circular
        indeterminate
        color="white"
        class="progress-icon mr-3"
        size="15"
        width="2"
        style="height: 20px; width: 20px; margin-right: 12px;"
      />
      <span class="white--text" style="text-align: start; font-weight: bold; font-size: 12px;"> {{ Math.ceil(value) }}% <br> AIチェック中</span>
    </template>
  </v-progress-linear>

  <v-btn
    v-else-if="failed && isSysAdmin"
    class="chip-status px-0"
    color="#f1b500"
    block
    elevation="0"
  >
    <v-icon right dense color="white" style="margin-right: 0px;">
      mdi-alert-outline
    </v-icon>
    <span class="white--text" style="text-align: start; font-weight: bold; font-size: 12px; margin-right: 10px; width: auto;">リコメンド未了</span>
  </v-btn>

  <v-chip v-else class="chip-status" :color="proof.status.color" label>
    <v-icon left>
      {{ proof.status.icon }}
    </v-icon>
    <span style="font-size: 14px;">{{ convertProofStatusText(proof.status) }}</span>
  </v-chip>
</template>

<script>
export default {
  name: 'NewProofStatus',
  props: {
    proof: {
      type: Object,
      required: true,
    },
    intervalTime: {
      type: Number,
      required: false,
      default: 2 * 1000,
    },
  },
  watch: {
    proof(){
      console.log("proof watch");
      const url = this.$page.url;
      if (url.includes('admin/proofs/') || url.includes('member/proofs/')) {
        window.location.reload();
      }
      this.$emit('update:proof', this.proof);
    }
  },
  data() {
    return {
      interval: null,
      batchData: null,
      proofVal: this.proof,
    };
  },
  computed: {
    queued() {
      return this.batchData?.status === 'queued';
    },
    executing() {
      return this.batchData?.status === 'executing';
    },
    failed() {
      return this.batchData?.status === 'failed';
    },
    progressing() {
      return this.queued || this.executing;
    },
    requesting() {
      return this.proof.status.value === 'requesting';
    },
    proofProgress() {
      return this.proof.status.value === 'progress';
    },
    isAdmin() {
      console.log("isAdmins");
      console.log(this.$page.props.user.user_type_id);
      return this.$page.props.user.user_type_id === 3;
    },
    isSysAdmin() {
      return this.$page.props.user.user_type_id === 4;
    },
  },
  methods: {
    validateBatch() {
      if (this.requesting || this.proofProgress) {
        return true;
      }

      if (this.proof.batch === null) {
        return false;
      }

      if (!this.progressing) {
        return false;
      }

      return true;
    },

    registerBatchInterval() {
      if (this.validateBatch()) {
        const path = this.route('admin.logs.batch', this.proof.batch.id);

        this.interval = setInterval(() => {
          this.$axios.get(path).then(({ data: batch }) => {
            this.batchData = batch;
            console.log("this.batchData");
            console.log(this.batchData);

            if (!batch.pending) {
              console.log('clear interval');
              clearInterval(this.interval);
              this.proof = batch.proof;
              this.$page.$props.proof = batch.proof;
              console.log("this.$page.$props.proof");
              console.log(this.$page.$props.proof);
              this.interval = null;
            }
          });
        }, this.intervalTime);
      }
    },
    showLog(e) {
      const path = this.route('admin.logs.queue.index', {
        id: this.proof.job_batch_id,
      });

      this.$inertia.get(path);
    },

    convertProofStatusText(status) {
      switch (status.value) {
        case 'requesting':
          return 'PDF化待ち';
        default:
          return status.text;
      }
    },
  },

  mounted() {
    console.log("NewProofStatus mounted");
    console.log(this.proof);
    console.log(this.proof.batch);
    console.log(this.batchData);
    this.registerBatchInterval();
  },

  beforeDestroy() {
    clearInterval(this.interval);
  },

  created() {
    this.batchData = this.proof?.batch;
  },
};
</script>

<style lang="scss" scoped>
.progress {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
  min-width: 120px !important;
  width: 120px !important;

  .progress-icon {
    left: 5px;
    top: 1px;
  }
}

.chip-status {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
  color: white;
  width: 120px !important;
  min-width: 120px !important;
  justify-content: center;
  height: 40px !important;
  font-weight: bold;
}
</style>
