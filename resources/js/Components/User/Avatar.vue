<template>
  <span v-if="tooltip">
    <v-tooltip top>
      <template v-slot:activator="{ on, attrs }">
        <v-avatar
          v-bind="attrs"
          v-on="on"
          :color="color"
          :size="size"
          :style="{ border: avatarBorder }"
        >
          <span :class="textColor">{{ text }}</span>
        </v-avatar>
      </template>

      <slot name="tooltip"></slot>
    </v-tooltip>
  </span>

  <span v-else>
    <v-avatar :color="color" :size="size" :style="{ border: avatarBorder }">
      <span :class="textColor">{{ text }}</span>
    </v-avatar>
  </span>
</template>

<script>
export default {
  name: 'Avatar',
  props: {
    color: {
      type: String,
      default: 'primary',
    },
    textColor: {
      type: String,
      default: 'white--text',
    },
    size: {
      type: Number,
      default: 42,
    },
    tooltip: {
      type: Boolean,
      defualt: false,
    },
    border: {
      type: Boolean,
      defualt: false,
    },
    text: {
      type: String,
    },
    tooltipText: {
      type: String,
    },
  },
  computed: {
    avatarBorder() {
      if (this.border) {
        return '2px solid white !important';
      }
    },
  },
};
</script>

<style></style>
