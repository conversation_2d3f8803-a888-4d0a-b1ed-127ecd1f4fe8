<template>
  <div>
    <v-switch
      label="翌営業日の締切分"
      hide-details
      dense
      :input-value="active"
      @change="change"
    />
  </div>
</template>

<script>
import { filterSerialize as serialize } from '../lib/utils';

export default {
  name: 'NextBusinessDayDeadlineAtFilter',
  props: ['filter', 'target', 'holidays'],
  computed: {
    active() {
      const { startOfDay, endOfDay } = this.getStartEndOfToday();

      const { from, to } = this.filter[this.target];

      return startOfDay == from && endOfDay == to;
    },
    _filter: {
      get() {
        return this.filter;
      },
      set(value) {
        this.$emit('update:filter', value);
      },
    },
  },

  methods: {
    getStartEndOfToday() {
      // 翌営業日の日付を取得
      const format = 'YYYY-MM-DD HH:mm:ss';
      let nextBusinessDay = this.$moment().add(1, 'days').startOf('day');

      // 土曜日の場合、月曜日に設定
      if (nextBusinessDay.day() === 6) {
        nextBusinessDay.add(2, 'days');
      }
      // 日曜日の場合、月曜日に設定
      else if (nextBusinessDay.day() === 0) {
        nextBusinessDay.add(1, 'days');
      }
      // nextBusinessDayが祝日である場合、次の営業日を取得
      this.holidays.forEach((holiday) => {
        const holidayDate = this.$moment(holiday.date).startOf('day');
        if (nextBusinessDay.isSame(holidayDate, 'day')) {
          nextBusinessDay.add(1, 'days');
          // 土曜日の場合、月曜日に設定
          if (nextBusinessDay.day() === 6) {
            nextBusinessDay.add(2, 'days');
          }
          // 日曜日の場合、月曜日に設定
          else if (nextBusinessDay.day() === 0) {
            nextBusinessDay.add(1, 'days');
          }
        }
      });

      return {
        startOfDay: nextBusinessDay.startOf('day').format(format),
        endOfDay: nextBusinessDay.endOf('day').format(format),
      };
    },

    createTodayFilters(targets, from, to) {
      return targets.reduce(
        (acc, cur) => ({
          ...acc,
          [cur]: {
            from: from,
            to: to,
          },
        }),
        {},
      );
    },

    reset() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: {
            from: null,
            to: null,
          },
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },

    // フィルター送信
    submit() {
      const { startOfDay, endOfDay } = this.getStartEndOfToday();

      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: {
            from: startOfDay,
            to: endOfDay,
          },
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },

    change(active) {
      active ? this.submit() : this.reset();
    },
  },
};
</script>
