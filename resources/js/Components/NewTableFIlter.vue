<template>
  <v-navigation-drawer
    v-if="_drawer"
    v-model="_drawer"
    width="500px"
    absolute
    temporary
    right
  >
    <v-card height="inherit">
      <v-card-title class="mb-3">
        ソート
        <v-spacer> </v-spacer>
        <v-btn icon @click="_drawer = false">
          <v-icon>mdi-window-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <slot />
      </v-card-text>
      <v-card-actions class="pb-4 pr-4">
        <v-row>
          <v-col>
            <v-btn
              color="grey darken-1"
              @click="reset(_filter)"
              block
              class="ml-2"
            >
              <span style="color: white">初期化</span>
            </v-btn>
          </v-col>

          <v-col>
            <v-btn color="success" @click="submit(_filter)" block class="mr-2">
              検索
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-navigation-drawer>
</template>

<script>
import {
  filterDeserialize as deserialize,
  filterSerialize as serialize,
} from '../lib/utils';

export default {
  name: 'NewTableFilter',
  props: ['filter', 'drawer'],
  computed: {
    _filter: {
      get() {
        return this.filter;
      },
      set(value) {
        this.$emit('update:filter', value);
      },
    },
    _drawer: {
      get() {
        return this.drawer;
      },
      set(value) {
        this.$emit('update:drawer', value);
      },
    },
  },

  methods: {
    // フィルター初期化
    initFilter() {
      const filtered = deserialize();

      this._filter = Object.assign(this.filter, filtered);
    },

    reset() {
      const path = location.pathname;

      this.$inertia.get(path);
    },

    // フィルター送信
    submit(filter) {
      filter.page = 1;

      const path = location.pathname + serialize(filter);

      this.$inertia.get(path);
    },
  },
  created() {
    this.initFilter();
  },
};
</script>
