<template>
  <div>
    <v-switch
      label="管理者のみ表示"
      hide-details
      dense
      :input-value="this.filter.admin_role"
      @change="change"
    />
  </div>
</template>

<script>
import { filterSerialize as serialize } from '../../../../lib/utils';

export default {
  name: 'DeletedUserFilter',
  props: ['filter', 'target'],
  computed: {
    active() {
      console.log('active');
      console.log(this.filter);
      console.log(this.target);
      console.log(this.filter[this.target]);

      return this.filter[this.target];
    },
    _filter: {
      get() {
        return this.filter;
      },
      set(value) {
        this.$emit('update:filter', value);
      },
    },
  },

  methods: {
    getDeletedAt() {
      return this.filter[this.target] ? this.filter[this.target] : false;
    },
    reset() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: false,
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },

    // フィルター送信
    submit() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: true,
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },

    change(active) {
      active ? this.submit() : this.reset();
    },
  },
};
</script>
