<template>
  <v-card :class="{ 'red-border': errors.length }" outlined :tile="tile">
    <v-card-text>
      <v-row v-if="discription" class="pa-2 d-flex text-start">
        <span v-html="discription" class="text-ellipsis"></span>
      </v-row>
      <v-chip-group id="apptype" :active-class="`text--white ${color}`" color="white" :value="_value" @change="updateValue"
        :multiple="multiple" column >
        <v-chip v-for="item in items" :key="item.id" :value="item[itemValue]" :disabled="disabled" label class="text-ellipsis">
          {{ getText(item) }}
        </v-chip>
      </v-chip-group>

      <v-card-text v-if="errors.length">
        <span class="red--text">{{ errors.at(0) }}</span>
      </v-card-text>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  props: {
    value: {
      type: Number | Object | Array,
      required: true,
      default: () => {},
    },
    label: {
      type: String,
      required: false,
      default: '',
    },
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    errors: {
      type: Array,
      required: false,
      default: () => [],
    },
    itemValue: {
      type: String,
      required: false,
      default: 'id',
    },
    itemText: {
      type: String | Function,
      required: false,
      default: 'name',
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    color: {
      type: String,
      required: false,
      default: 'success',
    },
    tile: {
      type: Boolean,
      required: false,
      default: false,
    },
    multiple: {
      type: Boolean,
      required: false,
      default: false,
    },
    discription: {
      type: String,
      required: false,
      default: null,
    },
  },
  computed: {
    _value: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  methods: {
    getText(item) {
      if (typeof this.itemText === 'string') {
        return item[this.itemText];
      }

      return this.itemText(item);
    },
    updateValue(value) {
      this._value = value;
    },
  },
  updated: function() {
    // errorsがNULLの場合、エラーがないと判断し、エラー表示を消す
  },
};
</script>

<style lang="scss" scoped></style>

