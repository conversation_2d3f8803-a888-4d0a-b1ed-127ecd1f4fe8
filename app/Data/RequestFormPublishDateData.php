<?php

namespace App\Data;

use Carbon\CarbonImmutable;

class RequestFormPublishDateData
{
    public CarbonImmutable|null $start = null;
    public CarbonImmutable|null $end = null;

    public function __construct(
        CarbonImmutable|string|null $start,
        CarbonImmutable|string|null $end,
    ) {
        if (is_string($start)) {
            $start = $this->parseDate($start, true);
        }

        if (is_string($end)) {
            $end = $this->parseDate($end, false);
        }

        $this->start = $start;
        $this->end = $end;
    }

    private function parseDate(string $date, bool $isStart = true): CarbonImmutable
    {
        return match (true) {
            // YYYY
            CarbonImmutable::hasFormat($date, 'Y') => $isStart ?
                CarbonImmutable::createFromFormat('Y', $date)->startOfYear() :
                CarbonImmutable::createFromFormat('Y', $date)->endOfYear(),
            // YYYY-MM
            CarbonImmutable::hasFormat($date, 'Y-m') => $isStart ?
                CarbonImmutable::createFromFormat('Y-m', $date)->startOfMonth() :
                CarbonImmutable::createFromFormat('Y-m', $date)->endOfMonth(),
            // YYYY-MM-DD
            CarbonImmutable::hasFormat($date, 'Y-m-d') => $isStart ?
                CarbonImmutable::createFromFormat('Y-m-d', $date)->startOfDay() :
                CarbonImmutable::createFromFormat('Y-m-d', $date)->endOfDay(),
        };
    }

    public function hasStart(): bool
    {
        return $this->start !== null;
    }

    public function hasEnd(): bool
    {
        return $this->end !== null;
    }
}
