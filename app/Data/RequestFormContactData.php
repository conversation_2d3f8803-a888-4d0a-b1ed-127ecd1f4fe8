<?php

namespace App\Data;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;

class RequestFormContactData extends Data
{
    public User $writer;

    /** @var Collection<string> */
    public Collection $to;

    /** @var Collection<string> */
    public Collection $cc;

    /** @var Collection<string> */
    public Collection $bcc;

    public string $subject;

    public string $body;

    public function __construct(
        array $to,
        array $cc,
        array $bcc,
        string $subject,
        string $body
    ) {
        $this->writer = auth()->user();
        $this->to = collect($to);
        $this->cc = collect($cc);
        $this->bcc = collect($bcc);
        $this->subject = $subject;
        $this->body = $body;
    }

    public static function fromRequest(Request $request)
    {
        return new self(
            to: $request->to ?? [],
            cc: $request->cc ?? [],
            bcc: $request->bcc ?? [],
            subject: $request->input('subject'),
            body: $request->input('body'),
        );
    }
}
