<?php

namespace App\Data;

use App\Enums\VideoToImageIntervalEnum as IntervalEnum;
use App\Enums\VideoToImageUploadTypeEnum as UploadTypeEnum;

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;
use Illuminate\Support\Str;

class ExtractImagesFromVideoData extends Data
{
    public function __construct(
        public UploadVideoData $upload,
        public ExtractAndUploadImagesFromS3VideoData $extract,
    ) {
    }

    public static function fromRequest(Request $request): static
    {
        $interval = IntervalEnum::fromValue((float)$request->interval_type);
        $uploadVideoPath = self::resolveUploadVideoPath($request->video);
        $disk = 's3';

        return self::from([
            'upload' => UploadVideoData::from([
                'video' => $request->video,
                'upload_type' => UploadTypeEnum::fromValue($request->upload_type),
                'disk' => $disk,
                'toPath' => $uploadVideoPath,
            ]),
            'extract' => ExtractAndUploadImagesFromS3VideoData::from([
                'video' => $uploadVideoPath,
                'interval_type' => $interval,
                'disk' => $disk,
                'toDir' => self::resolveUploadImagesDirectory($request->video, $interval),
            ]),
        ]);
    }

    private static function resolveUploadVideoPath(string|UploadedFile $video): string
    {
        $dir = self::resolveUploadDir($video);
        $fileName = self::getFileName($video);

        return "{$dir}/{$fileName}";
    }

    private static function resolveUploadImagesDirectory(
        string|UploadedFile $video,
        IntervalEnum $interval
    ): string {
        $dir = self::resolveUploadDir($video);

        return "{$dir}/{$interval->key}";
    }

    private static function resolveUploadDir(string|UploadedFile $video): string
    {
        $key = self::createUniqueKey($video);

        return config('filesystems.disks.s3.video_to_images_path') . $key;
    }

    public static function getFileName(string|UploadedFile $video): string
    {
        if($video instanceof UploadedFile) {
            return $video->getClientOriginalName();
        }

        return Str::of($video)->afterLast('/');
    }

    private static function createUniqueKey(string|UploadedFile $video): string
    {
        $key = $video instanceof UploadedFile ?
            $video->getFileName() :
            Str::of($video)->match('/([\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12})/');

        $fileName = self::getFileName($video);

        return Str::of($key)->append('_')->append($fileName)->pipe('md5');
    }
}
