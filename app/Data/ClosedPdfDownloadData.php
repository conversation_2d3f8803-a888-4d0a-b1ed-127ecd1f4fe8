<?php

namespace App\Data;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\Data;

class ClosedPdfDownloadData extends Data
{
    public function __construct(
      //
      public string $proofId,
      public string $token,
    ) {}

    public static function fromRequest(Request $request): static
    {
        return new self(
            proofId: $request->proofId,
            token: $request->token,
        );
    }

    public function setValues(string $proofId, string $token): static
    {
        $this->proofId = $proofId;
        $this->token = $token;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'proofId' => $this->proofId,
            'token' => $this->token,
        ];
    }

    public function isMatchToken(string $token): bool
    {
        Log::info('ClosedPdfDownloadData::isMatchToken');
        return $this->token === $token;
    }

    public static function createToken(Request $request): static
    {
        Log::info('ClosedPdfDownloadData::createToken');
        Log::info($request->all());
        Log::info($request->proofId);
        Log::info($request->title_name);
        $token = hash('sha256', $request->proofId . $request->title_name);

        return new self(
            proofId: $request->proofId,
            token: $token,
        );
    }

}
