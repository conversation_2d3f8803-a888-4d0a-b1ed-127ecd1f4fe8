<?php

namespace App\Actions\User;

use App\Data\UpdateUserData;
use App\Mail\ProofAssignmentedToRequiredUserMail;
use App\Models\Proof;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Mail;

class UpdateUserAction
{
    public function execute(User $user, UpdateUserData $data): User
    {
        $user->update(
            $data->except(
                'business_categories',
                'ips',
                'category_id'
            )->toArray()
        );

        if ($this->isAllowedUserType($user)) {
            $this->syncCategories($user, $data);
        }

        return $user->refresh();
    }

    private function syncCategories(User $user, UpdateUserData $data)
    {
        if ($this->isNotAssignable($data->categories, $data->ips, $user)) {
            return;
        }

        $this->assignmentMemberToProofs($data->categories, $data->ips, $user);

        $this->syncCategory($user, $data->categories);
        $this->syncIntellectualProperties($user, $data->ips);
    }

    /**
     * 更新する前に
     * @param array $categoryIds
     * @param array $ips
     * @param User $user
     * 
     * @return void
     */
    private function assignmentMemberToProofs(array $categoryIds, array $ips, User $user)
    {
        /**
         * 「進行中」ステータスのProofと
         * ユーザーが所属するIPsまたはカテゴリを持つProofを取得
         */
        $proofs = Proof::query()
            ->whereProgressing()
            ->where(function (Builder $query) use ($ips, $categoryIds) {
                $query
                    ->orWhereHasIps($ips)
                    ->orWhereHasCategories($categoryIds);
            })
            ->get();

        $syncResult = $user->members()->sync($proofs);

        $isNewAssignedProof = fn(Proof $proof) => in_array($proof->id, $syncResult['attached']);
        $sendMail = fn(Proof $proof) => Mail::to($user)->send(new ProofAssignmentedToRequiredUserMail($proof, $user));

        $proofs
            ->filter($isNewAssignedProof)
            ->each($sendMail);
    }

    private function syncCategory(User $user, ?array $categoryIds)
    {
        $user->categories()->sync($categoryIds);
    }

    private function syncIntellectualProperties(User $user, ?array $ips)
    {
        return $user->ips()->sync($ips);
    }

    private function isAllowedUserType(User $user): bool
    {
        return $user->user_type_id == User::USER_TYPE_MEMBER || $user->user_type_id == User::USER_TYPE_ADMIN;
    }

    private function isNotAssignable(array $categoryIds, array $ips, User $user): bool
    {
        if ($user->user_type_id != User::USER_TYPE_MEMBER) {
            return true;
        }

        $isNotDifferent = value(function (array $categoryIds, array $ips, User $user) {
            $currentCategoryIds = $user->categories()->pluck('id')->toArray();
            $currentIps = $user->ips()->pluck('id')->toArray();

            return
                is_same_array($currentCategoryIds, $categoryIds) &&
                is_same_array($currentIps, $ips);
        }, $categoryIds, $ips, $user);

        if ($isNotDifferent) {
            return true;
        }

        return false;
    }
}
