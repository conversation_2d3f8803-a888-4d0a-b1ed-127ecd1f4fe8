<template>
  <v-card class="mx-auto">
    <!-- ツールバー -->
    <Toolbar
      :handler.sync="handler"
      :user="user"
      :readonly="readonly"
      :proof="proof"
    />
    <v-row no-gutters>
      <!-- ファイルツリー構造 -->
      <v-col cols="3" style="border-right: 2px solid #e0e0e0">
        <Tree :handler.sync="handler" :icons="icons" :readonly="readonly" />
      </v-col>

      <!-- ファイルリスト -->
      <v-col>
        <List
          :handler.sync="handler"
          :icons="icons"
          :readonly="readonly"
          :proof="proof"
          :user="user"
        />
      </v-col>
    </v-row>

    <!-- アップロード確認画面 -->
    <UploadReview :handler.sync="handler" :icons="icons" :readonly="readonly" />

    <v-card-actions
      class="red--text"
      style="border-top: 1px solid #e0e0e0"
      v-if="isCreatable"
    >
      申請ボタンを押さない限り実際にサーバーへアップロードしません。
      <v-spacer></v-spacer>

      <v-checkbox
        v-model="sendReady"
        @change="syncFiles"
        class="mr-3"
        label="確認"
        :error="!sendReady || files.length === 0"
      ></v-checkbox>
    </v-card-actions>
  </v-card>
</template>

<script>
const icons = {
  zip: 'mdi-folder-zip-outline',
  rar: 'mdi-folder-zip-outline',
  htm: 'mdi-language-html5',
  html: 'mdi-language-html5',
  js: 'mdi-nodejs',
  json: 'mdi-json',
  md: 'mdi-markdown',
  pdf: 'mdi-file-pdf',

  // 画像
  png: 'mdi-file-image',
  bmp: 'mdi-file-image',
  jpg: 'mdi-file-image',
  jpeg: 'mdi-file-image',
  gif: 'mdi-file-image',

  // 動画
  mp4: 'mdi-filmstrip',
  mkv: 'mdi-filmstrip',
  avi: 'mdi-filmstrip',
  wmv: 'mdi-filmstrip',
  mov: 'mdi-filmstrip',
  mgp: 'mdi-filmstrip',
  mpg: 'mdi-filmstrip',
  mpeg: 'mdi-filmstrip',
  mpg: 'mdi-filmstrip',
  flv: 'mdi-filmstrip',
  mkv: 'mdi-filmstrip',
  m4v: 'mdi-filmstrip',

  // MS
  xls: 'mdi-microsoft-excel',
  xlsx: 'mdi-microsoft-excel',
  doc: 'mdi-microsoft-word',
  docx: 'mdi-microsoft-word',
  ppt: 'mdi-microsoft-powerpoint',
  pptx: 'mdi-microsoft-powerpoint',

  // テキスト
  txt: 'mdi-file-document-outline',
  csv: 'mdi-file-delimited',

  // CAD
  dwf: 'mdi-file-outline',
  dxf: 'mdi-file-outline',
  dwg: 'mdi-file-outline',

  // Illustrator
  ai: 'mdi-file-outline',

  // その他
  other: 'mdi-file-outline',
};

import Toolbar from './Toolbar';
import Tree from './Tree';
import List from './List';
import UploadReview from './UploadReview';
import { FileHandler } from './util';

export default {
  name: 'FileBrowser',
  components: {
    Toolbar,
    Tree,
    List,
    UploadReview,
  },

  props: {
    languages: { type: Array, default: [] },
    maxUploadFilesCount: { type: Number, default: 0 },
    maxUploadFileSize: { type: Number, default: 0 },

    files: { type: Array, default: () => [], required: false },
    readonly: { type: Boolean, default: true },
    creatable: { type: Boolean, default: false },
    user: { type: Object, required: true },
    proof: { type: Object, required: false },

    icons: { type: Object, default: () => icons },
  },

  data() {
    return {
      handler: new FileHandler(this.languages),
      sendReady: false,
    };
  },

  methods: {
    syncFiles(isAgree) {
      if (isAgree) {
        let files = [];

        if (this.isAdminUpload) {
          const path =
            this.proof.status.value === this.handler.PROOF_STATUS_REQUESTING
              ? this.handler.SHARED_PATH
              : this.handler.CLOSED_PATH;

          files = this.handler.getFiles(path);
        } else {
          files = this.handler.getAllFiles();
        }

        this.$emit('update:files', files);
        this.$emit('update:readonly', true);
      } else {
        this.$emit('update:files', []);
        this.$emit('update:readonly', false);
      }
    },
  },
  computed: {
    isAdminUpload() {
      return (
        this.proof?.status.value === this.handler.PROOF_STATUS_ASSIGNMENT ||
        this.proof?.status.value === this.handler.PROOF_STATUS_PROGRESSING
      );
    },

    isRequest() {
      return this.proof === undefined;
    },

    isCreatable() {
      return this.creatable && (this.isRequest || this.isAdminUpload);
    },
  },
  created() {
    this.handler.init(this.user.type.value, this.proof);
  },
};
</script>
