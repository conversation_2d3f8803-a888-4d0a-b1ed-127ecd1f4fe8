<?php

namespace App\Http\Controllers\Member;

use App\Actions\User\UpdateUserAction;
use App\Models\UserType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Member\UserController\UpdateRequest;
use App\Models\Category;
use App\Models\IntellectualProperty;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class UserController extends Controller
{
    public function show()
    {
        /** @var User */
        $user = auth()->user();
        $user->load([
            'ips',
            'categories'
        ]);

        $userTypes = UserType::all();
        $categories = Category::all();
        $intellectualProperties = IntellectualProperty::with('parent')->children()->get();

        return Inertia::render('Member/User/Show/Index', [
            'user' => $user,
            'userTypes' => $userTypes,
            'categories' => $categories,
            'intellectualProperties' => $intellectualProperties
        ]);
    }

    public function update(UpdateRequest $request, UpdateUserAction $action)
    {
        try {
            DB::beginTransaction();

            $action->execute(
                auth()->user(),
                $request->data()
            );

            DB::commit();

            return redirect()->back(self::HTTP_STATUS_SEE_OTHER)->with('snackbar', [
                'color' => 'primary',
                'icon' => 'mdi-check-circle',
                'title' => '成功',
                'message' => '情報を更新しました。'
            ]);
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }
    }
}
