<?php

namespace App\Helpers;

use Exception;
use App\Models\Proof;
use App\Models\ProofFile;
use App\Enums\PRMEnum;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\Response;

// 不自然なテキストチェックAPI(GPT版）へのリクエストヘルパークラス
class UnnaturalTextCheckGptRequester extends UnnaturalTextCheckRequester
{
    const SLEEP_SEC = 120;
    const RETRY_MAX = 10;

    private $text;
    private $recieve_id;

    public function __construct(Proof $proof, string $text, int $page_no)
    {
        $this->proof = $proof;
        $this->text = $text;
        $this->page_no = $page_no;
        $this->recieve_id = "";
    }

    private function postChatGptAzure($messages)
    {
        $apikey = "********************************";
        $url = "https://tf-azure-openai-bapi.openai.azure.com/openai/deployments/tf-bapi-gpt-35-turbo-0613/chat/completions?api-version=2023-03-15-preview";

        // リクエストボディ
        $data = array(
            'messages' => $messages,
            'max_tokens' => 800,
            'temperature' => 0.7,
            'frequency_penalty' => 0,
            'presence_penalty' => 0,
            'top_p' => 0.95
        );

        try {
            $response = Http::withHeaders([
                'api-key' => $apikey
                ])
                ->accept('application/json')
                ->post($url, $data)
                ->throw();    // 外部APIで発生したエラーの場合に例外を発生させる
    
            $res_json = $response->json();
        } catch (Exception $e) {
            Log::debug("UnnaturalTextCheckGptRequester api" . $e->getMessage());
            return NULL;
        }

        return $res_json;
    }

    public function requet_and_polling()
    {
        $result = [
            "status" => "",    // 処理結果
            "error" => ""    // エラー内容
        ];
        
        $proofFile = ProofFile::where('proof_id', $this->proof->id)->where('type', ProofFile::TYPE_SHARED)->first();
        if (!$proofFile) {
            Log::debug("ProofFile:error {$this->proof->id} is not found");
            $result["status"] = "failed";
            $result["error"] = "invalid proof id";
            return $result;
        }

        $messages = [
            ['role' => 'user', 'content' => "次の文章に誤字脱字があればリスト形式で指摘してください。\n" . $this->text],
        ];
        
        // 誤字脱字のチェックAPI
        for ($cnt = 0; $cnt < self::RETRY_MAX; $cnt++) {
            $res_json = $this->postChatGptAzure($messages);

            $is_success = true;
            if (is_null($res_json)) {
                $is_success = false;
            }
            else if (array_key_exists("error", $res_json)) {
                Log::debug("UnnaturalTextCheckGptRequester api error");
                Log::debug($res_json["error"]["message"]);
                $is_success = false;
            }

            sleep(self::SLEEP_SEC);
            if ($is_success) {
                break;
            }
        }

        if ($cnt >= self::RETRY_MAX) {
            $result["status"] = "failed";
            $result["error"] = "retry max";
            return $result;
        }

        // 結果を会話の続きとして追加
        $result_messages = array($res_json["choices"][0]["message"]["content"]);
        
        array_push($messages, ['role' => 'system', 'content' => $result_messages[0]]);
        array_push($messages, ['role' => 'user', 'content' => '表現に違和感がある箇所があれば指摘してください。']);
        
        // 表現の違和感チェックAPI
        for ($cnt = 0; $cnt < self::RETRY_MAX; $cnt++) {
            $res_json = $this->postChatGptAzure($messages);

            if (!is_null($res_json) && !array_key_exists("error", $res_json)) {
                $result_message = $res_json["choices"][0]["message"]["content"];
                array_push($result_messages, $result_message);
                $this->success($result_messages);
                $result["status"] = "succeeded";
                return $result;
            }

            sleep(self::SLEEP_SEC);
        }

        $result["status"] = "failed";
        $result["error"] = "retry max";
        return $result;
    }

    private function getKey(string $version, string $suffix, string $proof_id, int $page_no): string
    {
        return sprintf(
            "%s.%s.%s.%s",
            $version,
            $suffix,
            $proof_id,
            $page_no
        );
    }

    /*
     * API成功時処理
     */
    private function success($result_messages) {
        $api_version_no = config('unnatural-text-check.version_no');
        $cache_dir = config('filesystems.disks.s3.unnatural_text_check_response_dir');
        
        $content = json_encode($result_messages, JSON_UNESCAPED_UNICODE);

        $cache_key = $this->getKey($api_version_no, PRMEnum::UnnaturalTextChecker, $this->proof->id, $this->page_no);

        $path = "{$cache_dir}/{$cache_key}.json";
        Storage::disk('s3')->put($path, $content);
    }
}
