<template>
  <v-card class="mb-4" flat>
    <v-card-title>
      <v-toolbar flat>
        <v-toolbar-title>
          申請種別内訳グラフ
        </v-toolbar-title>
      </v-toolbar>
    </v-card-title>

    <v-divider></v-divider>

    <v-card-text v-if="chartData.labels.length > 0">
      <Doughnut
        :chart-options="chartOptions"
        :chart-data="chartData"
        :chart-id="chartId"
        :dataset-id-key="datasetIdKey"
        :plugins="plugins"
        :css-classes="cssClasses"
        :styles="styles"
        :width="width"
        :height="height"
      />
    </v-card-text>

    <v-card-text v-else>
      No data available
    </v-card-text>
  </v-card>
</template>

<script>
import { chain } from 'lodash';
import dayjs from 'dayjs';

import { Doughnut } from 'vue-chartjs/legacy';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
} from 'chart.js';
ChartJS.register(Title, Toolt<PERSON>, Legend, ArcElement, CategoryScale);

export default {
  name: 'Proof<PERSON>ategoriesChart',
  props: {
    counts: {
      type: Array,
      required: false,
      default: () => [],
    },
    chartId: {
      type: String,
      default: 'doughnut-chart',
    },
    datasetIdKey: {
      type: String,
      default: 'label',
    },
    width: {
      type: Number,
      default: 400,
    },
    height: {
      type: Number,
      default: 400,
    },
    cssClasses: {
      default: '',
      type: String,
    },
    legend: {
      type: Object,
      default: () => ({
        position: 'right',
        align: 'start',
      }),
    },
    styles: {
      type: Object,
      default: () => {},
    },
    plugins: {
      type: Array,
      default: () => [],
    },
    filter: {
      type: Function,
      default: () => true,
    },
  },
  components: {
    Doughnut,
  },
  data() {
    return {
      chartData: {
        labels: [],
        datasets: [],
      },
      chartOptions: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: this.legend,
        },
      },
    };
  },
  methods: {
    getRandomColor(_, index) {
      const colors = [
        '#d5f74a',
        '#ace0a2',
        '#82c8fa',
        '#fcfab8',
        '#f6c1dc',
        '#ef87ff',
        '#ff5e84',
        '#f69270',
        '#edc65c',
      ];

      return colors[index % colors.length];
    },
    mergeCategoriesCountByName(categoriesCount) {
      const accumulateCount = (acc, value) => {
        if (value.label in acc) {
          acc[value.label] += value.count;
        } else {
          acc[value.label] = value.count;
        }

        return acc;
      };

      const toChartFormat = ([label, count]) => ({ label, count });

      return chain(categoriesCount)
        .reduce(accumulateCount, {})
        .toPairs()
        .map(toChartFormat)
        .orderBy('count', 'desc')
        .value();
    },
  },
  created() {
    const mergedCategoryCountsByName = this.mergeCategoriesCountByName(
      this.counts,
    );

    this.chartData = {
      labels: mergedCategoryCountsByName.map(bc => bc.label),
      datasets: [
        {
          backgroundColor: mergedCategoryCountsByName.map(this.getRandomColor),
          data: mergedCategoryCountsByName.map(bc => bc.count),
        },
      ],
    };
  },
};
</script>
<style lang="scss">
div.v-toolbar__title {
  color: rgba(64, 64, 64, 1) !important;
}

#doughnut-chart{
  color: rgba(64, 64, 64, 1) !important;
}

</style>
