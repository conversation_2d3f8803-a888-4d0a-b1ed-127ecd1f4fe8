<?php

namespace App\Actions;

use App\Data\ClosedPdfDownloadData;
use App\Models\Proof;
use App\Models\ProofFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class PutClosedPdfTemporaryAction
{
    public function execute(string $title_name, int $proofId): string
    {
        Log::info('PutClosedPdfTemporaryAction::execute');
        Log::info($title_name);
        Log::info($proofId);
        $request = request();
        $request->merge(['title_name' => $title_name, 'proofId' => $proofId]);
        Log::info($request->all());
        $data = ClosedPdfDownloadData::createToken($request);
        return $this->generateUrl($data);
    }

    private function generateUrl(ClosedPdfDownloadData $data): string
    {
        return config('app.url') . '/closed-pdf?proofId=' . $data->proofId . '&token=' . $data->token;

        // if(!$fileName) {
        //     return '';
        // }
        // dirの最初の文字が/なら削除
        // $fileDir = substr($fileName->dir, 0, 1) === '/' ? substr($fileName->dir, 1) : $fileName->dir;
        // return Storage::disk('s3')->temporaryUrl(
        //     "{$fileDir}/{$fileName->name}",
        //     now()->addMinutes(30)
        // );

        // if( config('app.name') == 'Laravel' || config('app.name') == 'AIC-DEV'|| config('app.name') == 'AIC-STG') {
        //     return Storage::disk('s3')->temporaryUrl(
        //         "{$fileDir}/{$fileName->name}",
        //         now()->addMinutes(30)
        //     );
        // } else {
        //     return Storage::disk('s3')->temporaryUrl(
        //         "{$fileDir}/{$fileName->name}",
        //         now()->addDays(7)
        //     );
        // }
    }
}