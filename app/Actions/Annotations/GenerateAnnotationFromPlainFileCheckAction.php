<?php

namespace App\Actions\Annotations;

use App\Actions\PredictClassificationsAction;
use App\Data\ImageAlertAlertedImageData;
use App\Data\UnnaturalTextItemData;
use App\Enums\PRMEnum;
use App\Models\ProofFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class GenerateAnnotationFromPlainFileCheckAction
{
    private ProofFile $proofFile;

    public function __construct(
        private PredictClassificationsAction $action,
        private GenerateUnnaturalTextAnnotationsAction $generateUnnaturalTextAnnotations,
        private GenerateImageAlertAnnotationsAction $generateImageAlertAnnotations
    ) {
    }

    public function execute(ProofFile $proofFile, PRMEnum $prmEnum, array $predicts): array
    {
        // ログ用でプロパティに保持
        $this->proofFile = $proofFile;

        $generateAnnots = function (array $predict) use ($prmEnum): array {
            return match($prmEnum->value) {
                PRMEnum::UnnaturalTextChecker => $this->getUnnaturalTextAnnots($predict),
                PRMEnum::ImageAlert => $this->getImageAlertAnnots($predict),
                default => []
            };
        };

        return collect($predicts)
            ->flatMap($generateAnnots)
            ->toArray();
    }

    private function getUnnaturalTextAnnots(array $predict): array
    {
        $class = UnnaturalTextItemData::class;

        $predicts = collect([$predict])
                    // 「ocr」と「annotation」は同じ値ですが、統一するために「ocr」に変更
                    ->map(fn (array $predict) => Arr::set($predict, 'ocr', Arr::get($predict, 'annotation', [])))
                    ->map(fn (array $predict) => $this->toDto($predict, $class))
                    ->filter(fn (mixed $data) => $this->isDtoInstance($data, $class))
                    ->pipe(fn (Collection $data) => $class::collection($data));

        return $this->generateUnnaturalTextAnnotations->execute($predicts);
    }

    private function getImageAlertAnnots(array $alertedImage): array
    {
        $class = ImageAlertAlertedImageData::class;

        $predicts = collect([$alertedImage])
                    ->map(fn (array $alertedImage) =>  $this->toDto($alertedImage, $class))
                    ->filter(fn (mixed $data) => $this->isDtoInstance($data, $class))
                    ->pipe(fn (Collection $data) => $class::collection($data));

        return $this->generateImageAlertAnnotations->execute($predicts);
    }

    private function toDto(array $data, string $class): mixed
    {
        try {
            return $class::fromArray($data);
        } catch (\Throwable $th) {
            Log::warning(
                message: $th->getMessage(),
                context: [
                    'input' => [
                        'proofFile' => [
                            'id' => $this->proofFile->id,
                            'path' => $this->proofFile->path,
                        ]
                    ],
                    'trace' => [
                        'file' => $th->getFile(),
                        'line' => $th->getLine(),
                    ]
                ]
            );

            return [];
        }
    }

    private function isDtoInstance(mixed $data, string $dtoClass): bool
    {
        return $data instanceof $dtoClass;
    }
}
