<?php

namespace App\Actions\Proof;

use App\Models\Proof;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GetProofReservedAction
{
    public function execute(array|Collection $proofs, Carbon $date): int
    {
        // @TODO: Implement this
        return $proofs
                ->filter(
                    fn (Proof $proof) =>
                    $this->isProofWithinDateRange($proof, $date)
                )
                ->count();
    }

    private function isProofWithinDateRange(Proof $proof, Carbon $date): bool
    {
        return $date->between(
            $proof->created_at->startOfDay(),
            $proof->external_deadline_at->endOfDay()
        );
    }
}
