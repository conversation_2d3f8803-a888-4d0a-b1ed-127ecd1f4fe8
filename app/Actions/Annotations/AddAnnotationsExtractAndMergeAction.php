<?php

namespace App\Actions\Annotations;

use App\Helpers\XmlHelper as XML;
use Exception;
use SimpleXMLElement;

class AddAnnotationsExtractAndMergeAction
{
    public function execute(SimpleXMLElement $xml, array $annotations): array
    {
        $extractAnnotations = XML::map(
            $xml->add->children(),
            function (SimpleXMLElement $child) use ($annotations) {
                $this->throw($annotations, $child);

                return $child->asXML();
            }
        );

        return collect($annotations)
                ->merge($extractAnnotations)
                ->toArray();
    }

    private function throw(array $annotations, SimpleXMLElement $child)
    {
        if ($this->findAnnotation($annotations, $child)) {
            throw new Exception(
                sprintf("Invalid value of Annotations. '%s' is duplicate.", $child['name']),
                400
            );
        }
    }

    private function findAnnotation(array $annotations, SimpleXMLElement $child)
    {
        return XML::find($annotations, function (SimpleXMLElement $annotation) use ($child) {
            return XML::getName($annotation) == XML::getName($child);
        });
    }
}
