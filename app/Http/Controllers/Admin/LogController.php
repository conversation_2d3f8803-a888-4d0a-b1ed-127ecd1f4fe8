<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Proof\CreateRecommendBatchAction;
use App\Helpers\ProofFileManager;
use App\Http\Controllers\Controller;
use App\Models\AdobeApiLog;
use App\Models\ActivityLog;
use App\Models\JobBatch;
use App\Models\JobStatus;
use App\Models\ProofFile;
use App\Models\ProofLanguage;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use ZipArchive;

class LogController extends Controller
{
    public function database()
    {
        $paginator = ActivityLog::filter(request()->all())
                                ->orderByDesc('id')
                                ->orderByDesc('created_at')
                                ->paginate(self::PER_PAGE)
                                ->appends(request()->except(['page', '_token']));

        // NOTE クラス名リスト(App\Models\XXXX)
        $models = ActivityLog::select('subject_type')
                            ->distinct()
                            ->pluck('subject_type');

        $casures = User::has('database_logs')->get();

        return Inertia::render('Admin/Log/Database', [
            'paginator' => $paginator,
            'models'    => $models,
            'casures'   => $casures
        ]);
    }

    public function queue()
    {
        $paginator = JobBatch::filter(request()->all())
                    ->with(['proof', 'jobs'])
                    ->orderByDesc('created_at')
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

        return Inertia::render("Admin/Log/Queue/Index", [
            'paginator' => $paginator,
        ]);
    }

    public function batch(JobBatch $jobBatch)
    {
        $jobBatch->load(['proof', 'jobs']);

        return $jobBatch;
    }

    public function retryBatch(JobBatch $jobBatch)
    {
        $exitCode = Artisan::call('queue:retry-batch', ['id' => $jobBatch->id]);

        // Shell Script exit code(0: Success, etc: Fail)
        if ($exitCode) {
            // 失敗
            $output = Artisan::output();

            return redirect()->route('admin.logs.queue.index', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'error',
                                'icon' => 'mdi-alert-circle',
                                'title' => '失敗',
                                'message' => $output
                            ]);
        } else {
            // 成功
            sleep(1);

            return redirect()->route('admin.logs.queue.index', ['id' => $jobBatch->id], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'success',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => $jobBatch->id . 'バッチを再試行しました。'
                            ]);
        }
    }

    public function download(JobBatch $jobBatch)
    {
        $proof = $jobBatch->proof;

        $zip = new ZipArchive();
        $zipFilePath = Storage::disk('temp')->path($proof->uuid);

        if ($zip->open($zipFilePath, ZipArchive::CREATE) === true) {
            $proof->origin->each(function ($proofFile) use ($zip) {
                $content = ProofFileManager::getContentFromS3($proofFile->path);

                $zip->addFromString($proofFile->name, $content);
            });
        }

        $zip->close();

        return response()
            ->download($zipFilePath, "{$proof->id}.zip")
            ->deleteFileAfterSend(true);
    }


    public function forceUpload(
        JobBatch $jobBatch,
        // CreateRecommendBatchAction $createRecommendBatchAction
    ) {
        $validator = Validator::make(request()->all(), [
            'file' => 'required|file|mimetypes:application/octet-stream,application/pdf'
        ]);

        if ($validator->fails()) {
            return redirect()
                ->back()
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => $validator->errors()->first()
                ]);
        }

        $proof = $jobBatch->proof;
        $uploadedFile = request()->file('file');

        try {
            DB::beginTransaction();

            $path = ProofFileManager::mergePaths(
                config('filesystems.disks.s3.proof_path'),
                $proof->uuid,
                config('filesystems.disks.s3.shared_dir'),
                $uploadedFile->getClientOriginalName()
            );

            $result = ProofFileManager::uploadS3($path, file_get_contents($uploadedFile));
            if (!$result) {
                throw new Exception("File Upload to S3 Failed");
            }

            ProofFile::create([
                'name' => $uploadedFile->getClientOriginalName(),
                'extension' => $uploadedFile->guessClientExtension(),
                'size' => $uploadedFile->getSize(),
                'dir' => dirname($path),
                'type' => ProofFile::TYPE_SHARED,
                'last_modified' => now(),
                'proof_id' => $proof->id,
                'proof_language_id' => null,
            ]);

            $jobBatch->failed_models_remove();
            $jobBatch->failed_jobs = 0;
            $jobBatch->pending_jobs = 0;
            $jobBatch->status = 'finished';
            $jobBatch->save();

            // リコメンドバッチ生成
            // $createRecommendBatchAction->execute($proof);

            DB::commit();

            return redirect()
                ->route('admin.proofs.index', ['id' => $proof->id])
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $uploadedFile->getClientOriginalName() . 'ファイルをアップロードしました。'
                ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            return redirect()->route('admin.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'error',
                                'icon' => 'mdi-alert',
                                'title' => '失敗',
                                'message' => $th->getMessage()
                            ]);
        }
    }
}
