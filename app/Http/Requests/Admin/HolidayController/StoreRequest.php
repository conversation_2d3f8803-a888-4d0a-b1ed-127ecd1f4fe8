<?php

namespace App\Http\Requests\Admin\HolidayController;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'nullable',
                'string',
                'max:50'
            ],
            'date' => [
                'required',
                'date_format:Y-m-d',
                'unique:holidays,date'
            ],
        ];
    }

    public function attributes()
    {
        return [
            'name' => '祝日名',
            'date' => '日付',
        ];
    }

    public function messages()
    {
        return [
            'date.required' => ':attribute項目は必須です',
            'date.date_format' => '正しい:attributeではありません',
            'date.unique' => '既に登録されている:attributeです'
        ];
    }
}
