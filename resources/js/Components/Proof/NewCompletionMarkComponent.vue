<template>
  <section class=" d-flex">
    <section class="flex-grow-1 d-flex flex-column ma-2" style="gap: 8px; padding-left: 10px;">
      <section class="d-flex">
        <v-col cols="3" class="pa-0" style="margin-right: -40px;">
          <span class="font-weight-bold red--text" style="font-size: 14px; word-break: normal; color: rgba(64, 64, 64, 1);">
            スペシャルサポーター
          </span>
        </v-col>

        <v-row class="mb-2">
          <v-col
            v-if="nothingSpSup"
            sm="4"
            md="3"
            lg="2"
            class="py-1 d-flex align-center mr-3 mt-2 px-0"
          >
            <span style="font-size: 14px;  color: rgba(64, 64, 64, 1);">なし</span>
          </v-col>
          <v-col
            v-for="user in specialSupporters"
            :key="user.id"
            sm="4"
            md="3"
            lg="2"
            class="py-1 d-flex align-center mr-3 px-0"
          >
            <v-checkbox
              class="mt-0"
              :label="user.name"
              :input-value="includeCompletionMark(user)"
              :disabled="disabled"
              hide-details
              @change="completionMarking(user)"
            >
              <template v-slot:label>
                <span class="font-weight-bold red--text" style="font-size: 14px; word-break: normal; color: rgba(64, 64, 64, 1);">
                  {{ user.name }}
                </span>
              </template>
            </v-checkbox>
          </v-col>
        </v-row>
      </section>

      <v-divider></v-divider>

      <section class="d-flex mt-2">
        <v-col cols="3" class="pa-0" style="margin-right: -40px;">
          <span style="font-size: 14px;">
            サポーター
          </span>
        </v-col>

        <v-row class="mb-2">
          <v-col
            v-for="user in supporters"
            :key="user.id"
            sm="4"
            md="3"
            lg="2"
            class="py-1 d-flex align-center mr-3 mt-2 px-0"
          >
            <v-checkbox
              class="mt-0"
              :input-value="includeCompletionMark(user)"
              :disabled="disabled"
              hide-details
              @change="completionMarking(user)"
            >
              <template v-slot:label>
                <span style="font-size: 14px; word-break: normal; color: rgba(64, 64, 64, 1)">
                  {{ user.name }}
                </span>
              </template>
            </v-checkbox>
          </v-col>
        </v-row>
      </section>

      <v-divider></v-divider>
      <section class="d-flex mt-2">
        <v-col cols="3" class="pa-0 mt-2" style="margin-right: -40px;">
          <span class="font-weight-bold" style="font-size: 14px; color: rgba(64, 64, 64, 1)">
            メンバー
          </span>
        </v-col>

        <v-row>
          <v-col
            v-for="user in admins"
            :key="user.id"
            sm="4"
            md="3"
            lg="2"
            class="py-1 d-flex align-center mr-3 mt-2 px-0"
          >
            <v-checkbox
              class="mt-0"
              :input-value="includeCompletionMark(user)"
              :disabled="disabled"
              hide-details
              @change="completionMarking(user)"
            >
              <template v-slot:label>
                <span class="font-weight-bold" style="font-size: 14px; word-break: normal; color: rgba(64, 64, 64, 1)">
                  {{ user.name }}
                </span>
              </template>
            </v-checkbox>
          </v-col>
        </v-row>
      </section>
    </section>
  </section>
</template>

<script>
import { chain } from 'lodash';
import Vue from 'vue';

const SPECIAL_USER_MAIL_LIST = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export default {
  props: {
    currentCompletionMarks: {
      type: Array,
      default: () => [],
    },
    proof: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    completionMarks(){
      console.log("completionMarks watch");
      this.$emit('update:currentCompletionMarks', this.completionMarks);
    }
  },
  computed: {
    specialSupporters() {
      const specialSupporterIds = this.proof.members.map(user => user.id);
      const isSpecialSupporter = user => specialSupporterIds.includes(user.id);
      // isAdminの結果がfalseのuserを返す
      const isNotAdminAndSpecialUser = user =>
        !this.isAdmin(user);

      const isNotViewer = user =>
        !this.isViewer(user);

      return this.proof.capable_completion_mark_users
        .filter(isSpecialSupporter)
        .filter(isNotAdminAndSpecialUser)
        .filter(isNotViewer)
        .toSorted(this.userOrderDesc('special_supporter'));
    },

    supporters() {
      const hasAssignmentedMember = user =>
        this.specialSupporters.some(m => m.id === user.id);

      const hasAdmin = user => this.admins.some(a => a.id === user.id);

      const isNotAdminAndAssignmentedMember = user =>
        !hasAssignmentedMember(user) && !hasAdmin(user);

      const isNotViewer = user =>
        !this.isViewer(user);

      return this.proof.capable_completion_mark_users
        .filter(isNotAdminAndAssignmentedMember)
        .filter(isNotViewer)
        .toSorted(this.userOrderDesc('supporter'));
    },
    admins() {
      const isAdminOrSpecialUser = user =>
        this.isAdmin(user);

      return this.proof.capable_completion_mark_users
        .filter(isAdminOrSpecialUser)
        .toSorted(this.userOrderDesc('admin'));
    },
  },
  data() {
    return {
      nothingSpSup : false,
      completionMarks: this.currentCompletionMarks,
    };
  },
  methods: {
    isAdmin(user) {
      return user.type.value == 'admin';
    },
    isSpecialUser(user) {
      return SPECIAL_USER_MAIL_LIST.includes(user.email);
    },
    isNotSpecialUser(user) {
      return !this.isSpecialUser(user);
    },
    isViewer(user) {
      console.log("isViewer");
      console.log(user);
      console.log(user.type_value);
      return user.type_value == 'viewer';
    },
    includeCompletionMark(user) {
      return this.currentCompletionMarks.some(
        completion_mark => completion_mark.user == null ? false : completion_mark.user.id === user.id,
      );
    },
    userOrderDesc(sortType) {
      const getPriority = user => {
        const DEFAULT_PRIORITY = 100000000;
        const equalSortType = sort => sort.sort_type === sortType;

        return chain(user.completion_mark_sorts)
          .find(equalSortType)
          .get('priority', DEFAULT_PRIORITY)
          .value();
      };

      return function(x, y) {
        return getPriority(x) - getPriority(y);
      };
    },
    completionMarking(user) {
      console.log("completionMarking");
      const path = this.route(
        `${this.$page.props.user.type.value}.proofs.completion-mark`,
        {
          proof: this.proof.id,
          user: user.id,
        },
      );

      this.$axios.post(path).then(({ data }) => {
        this.$page.props.snackbar = data.snackbar;
        this.completionMarks = data.completion_marks;
      });

    },
  },
};
</script>

<style></style>
