<?php

namespace App\Events;

use App\Data\RequestFormPublishDateData;
use App\Models\Proof;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProofRequested
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public $proof;
    public $publishDate;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(
        Proof $proof,
        RequestFormPublishDateData $publishDate
    ) {
        $this->proof = $proof;
        $this->publishDate = $publishDate;
    }
}
