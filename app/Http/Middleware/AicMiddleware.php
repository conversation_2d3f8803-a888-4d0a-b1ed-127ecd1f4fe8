<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AicMiddleware
{
  /**
   * Handle an incoming request.
   * 
   * @param  \Illuminate\Http\Request  $request
   * @param  \Closure  $next
   * 
   * @return mixed
   */
  public function handle(Request $request, Closure $next)
  {
    // 許可するユーザー名とパスワード
    $username = env('BASIC_AUTH_USER', 'tf-bapi-aic');
    $password = env('BASIC_AUTH_PASS', 'kvrbX2ygxA_Q');

    if ($request->getUser() !== $username || $request->getPassword() !== $password) {
      return response('Unauthorized', 401, ['WWW-Authenticate' => 'Basic']);
    }

    return $next($request);
  }
}
