<?php

namespace App\Http\Requests\Admin\ProofController;

use App\Helpers\ProofFileManager;
use Illuminate\Foundation\Http\FormRequest;

class CloseRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'comments' => "nullable|array",
            'comments.*.value' => "required|string",
            'comments.*.created_at' => "required|date_format:Y-m-d H:i:s",
            'comments.*.user.id' => 'required|exists:users,id',
            'comments.*.replies' => 'nullable',

            'files' => [
                "required",
                // File Count
                function ($attribute, $value, $fail) {
                    if (count($value) > 20) {
                        return $fail($attribute . ' should be less than or equal to 3.');
                    }
                }
            ],
            'files.*.name' => "required|string",
            'files.*.size' => "required|numeric",
            'files.*.path' => "required|string",
            'files.*.extension' => "required|string",
            'files.*.last_modified' => "required|date",
            'files.*.proof_language_id' => "required|exists:proof_languages,id",
            'files.*.file' => "required|file|mimetypes:{$this->getAcceptFileMimes()}",
        ];
    }

    private function getAcceptFileMimes()
    {
        $extensions = [
            // MS
            // 'doc',
            // 'docx',
            // 'xls',
            // 'xlsx',
            // 'ppt',
            // 'pptx',

            // 画像
            // 'jpg',
            // 'jpeg',
            // 'png',
            // 'bmp',
            // 'gif',

            // テキスト
            // 'txt',
            // 'csv',

            // pdf
            'pdf',

            // 動画
            // 'mp4',
            // 'wmv',
            // 'mov',
            // 'avi',
            // 'mgp',
            // 'mpg',
            // 'mpeg',
            // 'flv',
            // 'mkv',

            // CAD
            // 'dwf',
            // 'dxf',
            // 'dwg',

            // Illustrator
            // 'ai',
        ];

        $mimes = collect($extensions)->map(function ($extension) {
            return ProofFileManager::getMIME($extension);
        })
            ->push('application/octet-stream')
            ->implode(',');

        return $mimes;
    }
}
