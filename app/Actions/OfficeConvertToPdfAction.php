<?php

namespace App\Actions;

use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

/**
 * OfficeファイルをPDFに変換
 */
class OfficeConvertToPdfAction
{
    private const EXTENSIONS = [
        'excel' => ['xlsx', 'xls'],
        // 'word' => ['docx', 'doc'],
        // 'powerpoint' => ['pptx', 'ppt'],
    ];

    public function __construct(
        private BinaryToUploadedFileAction $binaryToUploadedFileAction
    ) {}

    public function execute(UploadedFile $file): UploadedFile
    {
        $inputFile = $this->rename($file);

        if ($this->isExcelFile($inputFile)) {
            $this->setExcelPageFitToWidth($inputFile);
        }

        $path = $this->convert($inputFile);

        return new UploadedFile(
            path: $path,
            originalName: pathinfo($path, PATHINFO_BASENAME),
            mimeType: 'application/pdf'
        );
    }

    /**
     * Excelファイルのページ設定を変更
     *
     * @param UploadedFile $file
     * @return void
     */
    private function setExcelPageFitToWidth(UploadedFile $file): void
    {
        $spreadsheet = IOFactory::load($file->getPathname());

        // ワークシートのページ設定を変更
        collect($spreadsheet->getAllSheets())->each(function (Worksheet $sheet) {
            $sheet
                ->getPageSetup()
                ->setFitToWidth(1)   // 横1ページ
                ->setFitToHeight(0); // 高さ制限なし
        });

        $writer = IOFactory::createWriter($spreadsheet, IOFactory::WRITER_XLSX);
        $writer->setPreCalculateFormulas(false);
        $writer->save($file->getPathname());
    }

    private function isExcelFile(UploadedFile $file): bool
    {
        return in_array($file->extension(), self::EXTENSIONS['excel']);
    }

    /**
     * ファイル名を変更
     *
     * libreofficeでPDFに変換する際に、ファイル名の設定ができないため、
     * ファイル名を変更してからPDF変換をする
     *
     * 例えば、sample.xlsxをPDFに変換すると、sample.pdfというファイル名で保存される
     * 他にsample.xlsがある場合、sample.pdfというファイル名で保存されるため、
     * sample.pdfが上書きされてしまう
     *
     * sample.xlsx => sample.pdf
     * sample.xls => sample.pdf
     *
     * @param UploadedFile $file
     * @return UploadedFile
     */
    private function rename(UploadedFile $file): UploadedFile
    {
        $newName = Str::uuid() . ".{$file->getClientOriginalExtension()}";

        return $this->binaryToUploadedFileAction->execute(
            fileBaseName: $newName,
            binary: file_get_contents($file->getPathname())
        );
    }

    /**
     * PDFに変換
     *
     * @param UploadedFile $file
     * @return string PDFファイルのパス
     */
    private function convert(UploadedFile $file): string
    {
        $outdir = sys_get_temp_dir();

        $this->exec($this->makeCommand(
            filePath: $file->getPathname(),
            outdir: $outdir
        ));

        $pdfFileName = pathinfo($file->getPathname(), PATHINFO_FILENAME);

        return "{$outdir}/{$pdfFileName}.pdf";
    }

    /**
     * コマンドを生成
     *
     * NOTE: 高速化(?) https://zenn.dev/yksn/articles/d3eb91a88e1b04
     *
     * @param string $filePath inputファイルのパス
     * @param string $outdir 出力先ディレクトリ
     *
     * @return string コマンド
     */
    private function makeCommand(string $filePath, string $outdir): string
    {
        /**
         * libreoffice -h
         *
         * -env:<VAR>[=<VALUE>] Set a bootstrap variable. For example: to set a non-default user profile path:
         * -env:UserInstallation=file:///tmp/test
         */
        $userInstallationDirectoryOption = "-env:UserInstallation=file:///tmp/test";

        return "libreoffice --headless {$userInstallationDirectoryOption} --convert-to pdf --outdir {$outdir} {$filePath}";
    }

    /**
     * コマンドを実行
     *
     * @param string $cmd コマンド
     */
    private function exec(string $cmd): void
    {
        $exec = exec($cmd . ' 2>&1', $output, $resultCode);

        if (false === $exec || 0 !== $resultCode) {
            $croppedStderr = $this->trimString(implode("\n", $output), 1000);

            throw new Exception('Convertion Failure! Contact Server Admin:' . "\nError: $croppedStderr ({$resultCode})");
        }
    }

    private function trimString($value, $limit = 200, $end = '...')
    {
        return (mb_strwidth($value, 'UTF-8') <= $limit)
            ? $value
            : rtrim(mb_strimwidth($value, 0, $limit, '', 'UTF-8')) . $end;
    }
}
