<?php

namespace App\Actions\Proof;

use App\Data\RequestFormPublishDateData;
use App\Jobs\JobCachePredictGeneralAndLegalPRM;
use App\Jobs\JobRequestImageAlert;
use App\Jobs\JobRequestUnnaturalTextCheck;
use App\Models\JobBatch;
use App\Models\Proof;
use Illuminate\Support\Facades\Bus;
use Illuminate\Bus\Batch;

class CreateRecommendBatchAction
{
    public function execute(
        Proof $proof,
        RequestFormPublishDateData $publishDate
    ): Batch {
        return Bus::batch([
                new JobCachePredictGeneralAndLegalPRM($proof),
                new JobRequestUnnaturalTextCheck($proof, $publishDate),
                new JobRequestImageAlert($proof)
            ])
            ->then(function (Batch $batch) use ($proof) {
                JobBatch::whereId($batch->id)->update([
                    'status' => JobBatch::STATUS_SUCCEED
                ]);
            })
            ->name("{$proof->title} ({$proof->id}) - リコメンド")
            ->onQueue('recommend')
            ->dispatch();
    }
}
