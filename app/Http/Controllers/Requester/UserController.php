<?php

namespace App\Http\Controllers\Requester;

use App\Models\UserType;
use App\Http\Controllers\Controller;
use Inertia\Inertia;

class UserController extends Controller
{
    public function show()
    {
        $user = auth()->user();
        $userTypes = UserType::all();
        
        return Inertia::render('Requester/User/Show/Index', [
            'user' => $user,
            'userTypes' => $userTypes
        ]);
    }
}
