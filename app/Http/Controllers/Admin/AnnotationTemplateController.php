<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Proof\UpsertAnnotationTemplateAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AnnotaionTemplateController\UpsertRequest;
use App\Models\AnnotationTemplate;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class AnnotationTemplateController extends Controller
{
    /**
     * 倫理確認カテゴリーリスト
     */
    public function index()
    {
        $paginator = AnnotationTemplate::query()
                    ->self()
                    ->orderByDesc("created_at")
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

        return Inertia::render('Admin/Util/AnnotationTemplate/Index', [
            'paginator' => $paginator,
        ]);
    }

    public function newIndex()
    {
        $paginator = AnnotationTemplate::query()
            ->orderByDesc("id")
            ->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        // 作成者の情報を取得
        $paginator->getCollection()->transform(function ($item) {
            $item->user = $item->user()->first()->last_name . ' ' . $item->user()->first()->first_name;
            return $item;
        });

        return Inertia::render('Admin/Util/AnnotationTemplate/NewIndex', [
            'paginator' => $paginator,
        ]);
    }

    public function show(AnnotationTemplate $annotationTemplate)
    {
        return Inertia::render('Admin/Util/AnnotationTemplate/Show', [
            'annotationTemplate' => $annotationTemplate
        ]);
    }

    public function newShow(AnnotationTemplate $annotationTemplate)
    {
        return Inertia::render('Admin/Util/AnnotationTemplate/NewShow', [
            'annotationTemplate' => $annotationTemplate
        ]);
    }

    public function create()
    {
        return Inertia::render('Admin/Util/AnnotationTemplate/Create', []);
    }

    public function newCreate()
    {
        return Inertia::render('Admin/Util/AnnotationTemplate/NewCreate', []);
    }

    public function upsert(UpsertRequest $request, UpsertAnnotationTemplateAction $action)
    {
        try {
            DB::transaction(
                fn () =>
                $action->execute($request->data(), auth()->user())
            );

            return redirect()
                    ->route('admin.utils.annotation-templates.index', [], self::HTTP_STATUS_SEE_OTHER)
                    ->with('snackbar', [
                        'color' => 'primary',
                        'icon' => 'mdi-check-circle',
                        'title' => '成功',
                        'message' => '注釈テンプレートを保存しました。'
                    ]);
        } catch (\Throwable $th) {
            return redirect()
                    ->back()
                    ->with('snackbar', [
                        'color' => 'error',
                        'icon' => 'mdi-alert',
                        'title' => '失敗',
                        'message' => $th->getMessage()
                    ]);
        }
    }

    public function destroy(AnnotationTemplate $annotationTemplate)
    {
        try {
            DB::beginTransaction();

            $annotationTemplate->delete();

            DB::commit();

            return redirect()
                    ->route('admin.utils.annotation-templates.index', [], self::HTTP_STATUS_SEE_OTHER)
                    ->with('snackbar', [
                        'color' => 'primary',
                        'icon' => 'mdi-check-circle',
                        'title' => '成功',
                        'message' => "注釈テンプレートを削除しました。"
                    ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
