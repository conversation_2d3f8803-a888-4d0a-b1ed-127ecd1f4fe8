<?php

namespace App\Actions\EthicsConfirmAssistant;

use App\Actions\BinaryToUploadedFileAction;
use App\Actions\Proof\CreateProofFileAndUploadAction;
use App\Data\RequestFormImageData;
use App\Data\RequestFormPdfData;
use App\Data\RequestFormPublishDateData;
use App\Data\RequestFormTextData;
use App\Http\API\EthicsConfirmAssistant;
use App\Models\Proof;
use App\Models\ProofFile;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;

/**
 * 「PDFに対する説明」、「公開開始日」、「公開終了日」のいずれか一つでも値がある場合、
 * 1ページ（メタ情報が記入）を追加したPDFを追加する。
 * その後、PDFをS3にアップロードしてProofFileモデルを生成する。
 *
 * ※旧申請入力フォームの処理("/unnatural-text-check", "/image-alert")に入れる前に処理を行う。
 */
class MakeCommentPageAction
{
    public function __construct(
        private EthicsConfirmAssistant $api,
        private CreateProofFileAndUploadAction $createProofFileAndUploadAction,
        private BinaryToUploadedFileAction $binaryToUploadedFileAction
    ) {
    }

    /**
     * @param Proof $proof
     * @param RequestFormPdfData $pdf
     * @param RequestFormPublishDateData $publishDate
     */
    public function execute(
        Proof $proof,
        RequestFormPdfData $pdf,
        RequestFormPublishDateData $publishDate
    ): ProofFile {
        $this->validate($pdf);

        $file = $this->shouldAddCommentPage($pdf, $publishDate) ?
            $this->getPdfWithAddCommentPage($pdf, $publishDate) :
            $pdf->value ;

        return $this->createProofFileAndUploadAction->execute(
            $proof,
            $file,
            config('filesystems.disks.s3.converted_dir'),
            ProofFile::TYPE_CONVERTED
        );
    }

    private function getPdfWithAddCommentPage(
        RequestFormPdfData $pdf,
        RequestFormPublishDateData $publishDate
    ): UploadedFile {
        $response = $this->api->makeCommentPage($pdf, $publishDate);

        return $this->binaryToUploadedFileAction->execute(
            $pdf->value->getClientOriginalName(),
            $response->body()
        );
    }

    /**
     * @param RequestFormPdfData $pdf
     * @param RequestFormPublishDateData $publishDate
     */
    private function shouldAddCommentPage(
        RequestFormPdfData $pdf,
        RequestFormPublishDateData $publishDate
    ): bool {
        return $pdf->hasComment() || $publishDate->hasStart() || $publishDate->hasEnd();
    }

    private function validate(RequestFormPdfData $pdf): void
    {
        if (!$pdf->hasPdf()) {
            throw new \InvalidArgumentException("PDFファイルが存在しません。");
        }
    }
}