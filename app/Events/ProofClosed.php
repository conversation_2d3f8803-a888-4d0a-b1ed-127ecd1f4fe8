<?php

namespace App\Events;

use App\Models\Proof;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProofClosed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $proof;
    public $temporaryUrl;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Proof $proof, $temporaryUrl = "")
    {
        $this->proof = $proof;
        $this->temporaryUrl = $temporaryUrl;
    }
}
