<?php

namespace App\Actions\Annotations;

use App\Actions\PredictClassificationsAction;
use App\Data\ImageAlertAlertedImageData;
use App\Data\UnnaturalTextItemData;
use App\Enums\PRMEnum;
use App\Models\ProofFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class GenerateAnnotationFromRecommendAction
{
    private ProofFile $proofFile;

    public function __construct(
        private PredictClassificationsAction $action,
        private GenerateUnnaturalTextAnnotationsAction $generateUnnaturalTextAnnotations,
        private GenerateImageAlertAnnotationsAction $generateImageAlertAnnotations
    ) {
    }

    public function execute(ProofFile $proofFile, PRMEnum $prmEnum): array
    {
        // ログ用でプロパティに保持
        $this->proofFile = $proofFile;

        $generateAnnots = function (array $predictsResult): array {
            $prmType = data_get($predictsResult, 'value');

            return match($prmType) {
                PRMEnum::UnnaturalTextChecker => $this->getUnnaturalTextAnnots(
                    data_get($predictsResult, 'predicts')
                ),
                PRMEnum::ImageAlert => $this->getImageAlertAnnots(
                    data_get($predictsResult, 'predicts.alerted_images')
                ),
                default => []
            };
        };

        $predictsResults = $this->action->execute($proofFile, [
            $prmEnum,
        ]);

        return collect($predictsResults)
            ->flatMap($generateAnnots)
            ->toArray();
    }

    private function getUnnaturalTextAnnots(array $predicts): array
    {
        $class = UnnaturalTextItemData::class;

        $predicts = collect($predicts)
                    ->map(fn (array $predict) => $this->toDto($predict, $class))
                    ->filter(fn (mixed $data) => $this->isDtoInstance($data, $class))
                    ->pipe(fn (Collection $data) => $class::collection($data));

        return $this->generateUnnaturalTextAnnotations->execute($predicts);
    }

    private function getImageAlertAnnots(array $alertedImages): array
    {
        $class = ImageAlertAlertedImageData::class;

        $predicts = collect($alertedImages)
                    ->map(fn (array $alertedImage) =>  $this->toDto($alertedImage, $class))
                    ->filter(fn (mixed $data) => $this->isDtoInstance($data, $class))
                    ->pipe(fn (Collection $data) => $class::collection($data));

        return $this->generateImageAlertAnnotations->execute($predicts);
    }

    private function toDto(array $data, string $class): mixed
    {
        try {
            return $class::fromArray($data);
        } catch (\Throwable $th) {
            Log::warning(
                message: $th->getMessage(),
                context: [
                    'input' => [
                        'proofFile' => [
                            'id' => $this->proofFile->id,
                            'path' => $this->proofFile->path,
                        ]
                    ],
                    'trace' => [
                        'file' => $th->getFile(),
                        'line' => $th->getLine(),
                    ]
                ]
            );

            return [];
        }
    }

    private function isDtoInstance(mixed $data, string $dtoClass): bool
    {
        return $data instanceof $dtoClass;
    }
}
