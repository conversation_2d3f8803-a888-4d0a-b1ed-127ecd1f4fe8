<?php

namespace App\Http\API;

use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\Response;
use DateTime;
use Exception;
use Illuminate\Support\Arr;

/**
 * BAPIセット事例リコメンドAPI
 * Dockerベース
 */
class PRM
{
    public $client = null;
    public $code = 200;
    public $message = '';

    // Errors
    public $errors = [
        'fail' => '認証トークンのエラー又正しい分類項目ではありません。',
        'busy' => '実行中です。今しばらくお待ちください。',
        'time out' => 'タイムアウトが発生しました。',
    ];

    public function __construct()
    {
        $this->client = Http::asForm()
                            ->baseUrl(config('prm.url'));
    }

    public function version()
    {
        $response = $this->client->post('/file-class/version/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "APIバージョンの取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['version'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function predict(string $text)
    {
        $response = $this->client->post('/file-class/predict/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
            'text' => $text,
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "ファイル分類予測API: {$this->message}",
                    $this->code
                );
            }

            return $response->json();
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function predicts(array $text)
    {
        $response = $this->client->post('/file-class/predict-list/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
            'text' => $text,
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "ファイル分類予測API (json): {$this->message}",
                    $this->code
                );
            }

            return  $response->json();
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function annotationsList()
    {
        $response = $this->client->post('/file-class/annotation-sample/', [
                            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
                        ]);
        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "注釈の代表例の取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['annotations'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function annotations(string|int $class_no)
    {
        $response = $this->client->post('/file-class/annotations/', [
                            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
                            'class_no' => $class_no,
                        ]);
        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "おすすめの注釈の取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['annotations'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function files(string|int $class_no)
    {
        $response = $this->client->post('/file-class/file-info-list/', [
                            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
                            'class_no' => $class_no,
                        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "おすすめの注釈の取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['file_list'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    private function getAuthToken(string $prefix)
    {
        $date = (new DateTime())->format('Ymd');

        $text = $prefix . $date;

        $hash_str = hash('sha256', $text);

        return substr($hash_str, 0, 16);
    }

    private function fails(Response $response)
    {
        // Http Status Code 200以外エラー
        $response->throw(function ($res, $e) {
            $this->code = $e->getCode();
            $this->message= $e->getMessage();

            return true;
        });

        $result = $response->json()['result'];

        // public function predictのstring $textが空白('')の場合
        if ($result == 'no text') {
            $this->code = $response->status();
            $this->message = "ファイルにテキストデータがない為、予測することができませんでした";

            return true;
        }

        // Http Status Code 200でもエラーが発生した場合
        if ($result !== 'success') {
            $this->code = $response->status();
            $this->message = Arr::get($this->errors, $result, '');

            return true;
        }

        return false;
    }
}
