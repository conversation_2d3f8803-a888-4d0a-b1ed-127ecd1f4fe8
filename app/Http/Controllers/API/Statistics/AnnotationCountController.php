<?php

namespace App\Http\Controllers\API\Statistics;

use App\Actions\Statistics\GetAnnotationCountGroupByDateAction;
use App\Data\StatisticRequestData;
use App\Http\Controllers\Controller;
use App\Models\Proof;
use App\Models\ProofFile;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

class AnnotationCountController extends Controller
{
    public function __invoke(
        StatisticRequestData $data,
        GetAnnotationCountGroupByDateAction $action
    ) {
        $dateformat = $data->unit->dateformat();

        $range = CarbonPeriod::create(
            $data->range->from ?? Proof::min('created_at'),
            $data->range->to ?? now()->endOfDay()
        );

        return collect($range)->pipeThrough([
            // Initialize Return Data
            fn (Collection $range) => $range->mapWithKeys(fn (Carbon $date) => [
                $date->format($dateformat) => [
                    $action->drawingCount => 0,
                    $action->textCount => 0,
                    $action->totalCount => 0
                ]
            ]),
            // Replace with the data retrieved from DB
            fn (Collection $initData) => $initData->replace(
                $action->execute(
                    model: ProofFile::class,
                    range: $range,
                    dateformat: $dateformat,
                )
            )
        ]);
    }
}
