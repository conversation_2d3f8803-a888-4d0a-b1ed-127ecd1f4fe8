<?php

namespace App\Services;

class InputSanitizer
{
    /**
     * Sanitize context data to prevent prompt injection attacks
     */
    public static function sanitizeContext(array $context): array
    {
        $sanitized = [];

        foreach ($context as $key => $value) {
            $sanitizedKey = self::sanitizeKey($key);
            $sanitizedValue = self::sanitizeValue($value);

            if ($sanitizedKey && $sanitizedValue !== null) {
                $sanitized[$sanitizedKey] = $sanitizedValue;
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize user query to prevent prompt injection
     */
    public static function sanitizeQuery(string $query): string
    {
        // Remove potential prompt injection patterns
        $patterns = [
            '/\bignore\s+previous\s+instructions?\b/i',
            '/\bignore\s+above\b/i',
            '/\bforget\s+everything\b/i',
            '/\bact\s+as\s+(?:a\s+)?(?:different|new)\b/i',
            '/\bpretend\s+(?:to\s+be|you\s+are)\b/i',
            '/\brole\s*:\s*(?:system|assistant|user)\b/i',
            '/\b(?:system|assistant|user)\s*:\b/i',
            '/\[(?:system|assistant|user)\]/i',
            '/\{(?:system|assistant|user)\}/i',
        ];

        $sanitized = preg_replace($patterns, '[FILTERED]', $query);

        // Limit length to prevent extremely long inputs
        $sanitized = mb_substr($sanitized, 0, 1000);

        // Remove excessive whitespace
        $sanitized = preg_replace('/\s+/', ' ', trim($sanitized));

        return $sanitized;
    }

    /**
     * Sanitize context key
     */
    private static function sanitizeKey(string $key): ?string
    {
        // Only allow alphanumeric characters and underscores
        $sanitized = preg_replace('/[^a-zA-Z0-9_]/', '', $key);

        // Reject keys that are too long or empty
        if (empty($sanitized) || strlen($sanitized) > 50) {
            return null;
        }

        // Reject potentially dangerous keys
        $dangerousKeys = ['role', 'system', 'assistant', 'user', 'content', 'message'];
        if (in_array(strtolower($sanitized), $dangerousKeys)) {
            return null;
        }

        return $sanitized;
    }

    /**
     * Sanitize context value
     */
    private static function sanitizeValue($value)
    {
        if (is_string($value)) {
            return self::sanitizeString($value);
        } elseif (is_array($value)) {
            return self::sanitizeArray($value);
        } elseif (is_numeric($value)) {
            return $value;
        } elseif (is_bool($value)) {
            return $value;
        } else {
            // Reject other types
            return null;
        }
    }

    /**
     * Sanitize string value
     */
    private static function sanitizeString(string $value): ?string
    {
        // Limit length
        if (strlen($value) > 500) {
            return null;
        }

        // Remove potential prompt injection patterns
        $patterns = [
            '/\bignore\s+previous\s+instructions?\b/i',
            '/\bignore\s+above\b/i',
            '/\bforget\s+everything\b/i',
            '/\bact\s+as\s+(?:a\s+)?(?:different|new)\b/i',
            '/\brole\s*:\s*(?:system|assistant|user)\b/i',
            '/\b(?:system|assistant|user)\s*:\b/i',
        ];

        $sanitized = preg_replace($patterns, '[FILTERED]', $value);

        // Remove excessive whitespace
        $sanitized = preg_replace('/\s+/', ' ', trim($sanitized));

        return $sanitized;
    }

    /**
     * Sanitize array value (recursive)
     */
    private static function sanitizeArray(array $value): ?array
    {
        // Limit array size
        if (count($value) > 20) {
            return null;
        }

        $sanitized = [];
        foreach ($value as $k => $v) {
            $sanitizedKey = is_string($k) ? self::sanitizeKey($k) : $k;
            $sanitizedValue = self::sanitizeValue($v);

            if ($sanitizedKey !== null && $sanitizedValue !== null) {
                $sanitized[$sanitizedKey] = $sanitizedValue;
            }
        }

        return $sanitized;
    }
}
