<?php

namespace App\Http\Controllers\ViewerUser;

use App\Helpers\ProofFileManager;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\IntellectualProperty;
use App\Models\Proof;
use App\Models\ProofFile;
use App\Models\ProofNumber;
use App\Models\ProofStatus;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use ZipArchive;

class ProofController extends Controller
{
    public function index(Request $request)
    {
        if (request()->has('proof_number') && request()->proof_number != null) {
          // proof_numberが5桁未満なら先頭から0を埋める
          $proof_number = request()->proof_number;
          if (strlen($proof_number) < 5) {
            $proof_number = str_pad($proof_number, 5, '0', STR_PAD_LEFT);
          }
          // company_codeを付ける
          $proof_number = request()->company_code . '-' . $proof_number;
          request()->merge(['proof_number' => $proof_number]);
        }

        $query = Proof::filter(request()->all())
          ->where('proof_status_id', ProofStatus::ofValue(ProofStatus::STATUS_CLOSED)->first()->id)
          ->orderByDesc('created_at');

        if (request()->has('checkedOnly') && request()->checkedOnly) {
          $userId = auth()->id();
          $query->whereHas('completionMarks', function ($q) use ($userId) {
            $q->where('user_id', $userId);
          });
        }

        if (request()->has('xp_only') && request()->xp_only == "true") {
          // proofs.proof_numberがBNXP-から始まるものを取得
          $query->where('proof_number', 'like', 'BNXP-%');
        }

        if (request()->has('am_only') && request()->am_only == "true") {
          // proofs.proof_numberがBNAM-から始まるものを取得
          $query->where('proof_number', 'like', 'BNAM-%');
        }

        if (request()->has('al_only') && request()->al_only == "true") {
          // proofs.proof_numberがBNAL-から始まるものを取得
          $query->where('proof_number', 'like', 'BNAL-%');
        }

        $paginator = $query->paginate(self::PER_PAGE)
          ->appends(request()->except(['page', '_token']));

        $paginator
          ->each(fn(Proof $proof) => $proof->append('capable_completion_mark_users'));


        $proofStatuses = ProofStatus::where('value', '!=', ProofStatus::STATUS_REJECT)
          ->where('value', '!=', ProofStatus::STATUS_ASSIGNMENT)
          ->get();
        $requesters = User::has('proofs')->get();

        $closedProofFiles = ProofFile::whereIn('type', [ProofFile::TYPE_CLOSED, ProofFile::TYPE_CONVERTED])->get();
        $paginator->each(function ($proof) use ($closedProofFiles) {
          // $proofのidをもつファイルでnameに_gendocがはいっていないものを取得
          $proof->convertFile = $closedProofFiles->filter(function ($closedProofFile) use ($proof) {
            return $closedProofFile->proof_id === $proof->id && strpos($closedProofFile->name, '_gendoc') === false && $closedProofFile->type === 'converted';
          })->values();

          $proof->closedFile = $closedProofFiles->filter(function ($closedProofFile) use ($proof) {
            return $closedProofFile->proof_id === $proof->id && $closedProofFile->type === 'closed';
          })->values();
        });

        return Inertia::render('ViewerUser/Proof/Archive', [
          'paginator' => $paginator,
          'ips' => IntellectualProperty::with('parent')->children()->get(),
          'categories' => Category::all(),
          'proofStatuses' => $proofStatuses,
          'requesters' => $requesters,
          'user' => auth()->user(),
          'companyCode' => ProofNumber::select('company_code')->get()->pluck('company_code')->toArray(),
        ]);
    }

  public function closedOriginFileZipDownload(Proof $proof)
  {
    Log::info('closedOriginFileZipDownload');
    Log::info($proof->id);

    $proofFiles = ProofFile::where('proof_id', $proof->id)
      ->where('type', ['origin'])
      ->get();

    if ($proofFiles->isEmpty()) {
      return redirect()
        ->route('viewer.archive', ['id' => $proof->id])
        ->with('snackbar', [
          'color' => 'error',
          'icon' => 'mdi-alert',
          'title' => '失敗',
          'message' => '対象ファイルが見つかりませんでした。'
        ]);
    }

    Log::info($proofFiles);

    // 一時ディレクトリのパス
    $tempDir = storage_path('app/public/tmp/' . $proof->id);
    // $proofFiles->first()->nameから拡張子を取り除いたファイル名を取得
    $fileNameWithoutExtension = pathinfo($proof->proof_number, PATHINFO_FILENAME);
    // ZIPファイル名
    $zipFileName = $fileNameWithoutExtension . '.zip';
    $zipFilePath = storage_path('app/public/tmp/' . $zipFileName);

    Log::info('ZIPファイル名: ' . $zipFileName);
    Log::info('ZIPファイルパス: ' . $zipFilePath);
    Log::info('一時ディレクトリ: ' . $tempDir);

    // 一時ディレクトリの作成
    if (!file_exists($tempDir)) {
      mkdir($tempDir, 0755, true);
    }

    $zip = new ZipArchive();
    if ($zip->open($zipFilePath, ZipArchive::CREATE) !== true) {
      return redirect()
        ->route('viewer.archive', ['id' => $proof->id])
        ->with('snackbar', [
          'color' => 'error',
          'icon' => 'mdi-alert',
          'title' => '失敗',
          'message' => 'ZIPファイルの作成に失敗しました。'
        ]);
    }

    $filesToDelete = [];

    try {
      foreach ($proofFiles as $proofFile) {
        $file = ProofFileManager::getContentFromS3($proofFile->path);
        if (!$file) {
          throw new \Exception('S3からのファイル取得に失敗しました: ' . $proofFile->name);
        }

        $tempFilePath = $tempDir . '/' . $proofFile->name;
        $filesToDelete[] = $tempFilePath;

        // ファイルを一時ディレクトリに保存
        if (file_put_contents($tempFilePath, $file) === false) {
          throw new \Exception('ファイルの書き込みに失敗しました: ' . $tempFilePath);
        }

        Log::info('ファイル保存: ' . $tempFilePath);

        // ZIPに追加
        if (!$zip->addFile($tempFilePath, $proofFile->name)) {
          throw new \Exception('ZIPへのファイル追加に失敗しました: ' . $proofFile->name);
        }
      }

      $zip->close();

      $headers = [
        'Content-Type' => 'application/zip',
        'Content-Disposition' => 'attachment; filename="' . $zipFileName . '"',
        'Cache-Control' => 'no-cache, no-store, must-revalidate',
        'Pragma' => 'no-cache',
        'Expires' => '0'
      ];
      // ダウンロードレスポンスを返す
      return response()->download($zipFilePath, $zipFileName, $headers)->deleteFileAfterSend(true);
    } catch (\Exception $e) {
      // エラー発生時はZIPファイルを閉じて削除
      Log::error('ZIPファイル作成エラー: ', [$e->getMessage()]);
      return redirect()
        ->route('viewer.archive')
        ->with('snackbar', [
          'color' => 'error',
          'icon' => 'mdi-alert',
          'title' => '失敗',
          'message' => 'ZIPファイルの作成に失敗しました。'
        ]);
    }
  }
}