<?php

namespace App\Helpers;

use App\Data\RequestFormPublishDateData;
use App\Events\ProofRequested;
use App\Jobs\JobCreatePDFAndStore;
use App\Jobs\JobCombinePDFAndStore;
use App\Mail\ProofFailedMail;
use App\Models\Proof;
use App\Models\ProofFile;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Storage;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use \arrayObject;

class ProofFileManager extends FilesystemAdapter
{
    /**
     * S3にファイル保存
     * @param path ファイルプルパス
     * @param content ファイルリソース
     * @param options オプション
     */
    public static function uploadS3(string $path, $content, $options = []): bool
    {
        return Storage::disk('s3')->put($path, $content, $options);
    }

    /**
     * ローカルにファイル保存
     * @param path ファイルプルパス
     * @param content ファイルリソース
     * @param options オプション
     */
    protected static function uploadLocalTemp(string $path, $content, $options = []): bool
    {
        return Storage::disk('temp')->put($path, $content, $options);
    }

    /**
     * ローカルにあるファイル削除
     * @param path ファイルプルパス
     */
    protected static function removeLocalTemp(string $path)
    {
        return Storage::disk('temp')->delete($path);
    }

    /**
     * S3にあるファイル削除
     * @param path ファイルプルパス
     */
    public static function removeS3(string $path)
    {
        return Storage::disk('s3')->delete($path);
    }

    /**
     * S3からリソース取得
     * @param path ファイルプルパス
     */
    public static function getContentFromS3(string $path)
    {
        return Storage::disk('s3')->get($path);
    }

    public static function uploadFilesStore($uploadedFiles, Proof $proof, $type)
    {
        return collect($uploadedFiles)->map(function ($file) use ($proof, $type) {
            $path = self::mergePaths(
                config('filesystems.disks.s3.proof_path'),
                $proof->uuid,
                $file['path'],
                $file['name']
            );

            self::uploadS3($path, $file['file']->get());

            return ProofFile::create([
                'name' => $file['name'],
                'extension' => $file['extension'],
                'size' => $file['size'],
                'dir' => dirname($path),
                'type' => $type,
                'last_modified' => Carbon::create($file['last_modified'])->timezone('Asia/Tokyo'),
                'proof_id' => $proof->id,
                'proof_language_id' => $file['proof_language_id'],
            ]);
        });
    }

    public static function request($uploadedFiles, Proof $proof)
    {
        // 受付ファイルS3へ保存
        $originProofFiles = self::uploadFilesStore($uploadedFiles, $proof, ProofFile::TYPE_ORIGIN);

        // PDF変換後、S3へ保存するJob生成
        $jobs = collect($originProofFiles)
            ->map(fn(ProofFile $proofFile) => new JobCreatePDFAndStore($proofFile))
            ->push(new JobCombinePDFAndStore($proof));

        // Batch生成
        $batch = Bus::batch($jobs)
            ->then(function (Batch $batch) use ($proof) {
                $publishDate = new RequestFormPublishDateData(
                    start: null,
                    end: null
                );

                event(new ProofRequested($proof, $publishDate));
            })
            ->catch(function (Batch $batch, \Throwable $e) use ($proof) {
                $admins = User::admin()->get();

                Mail::to($admins)->send(new ProofFailedMail(
                    $proof,
                    $e->getMessage()
                ));
            })
            ->name("{$proof->title} ({$proof->id}) - 依頼")
            ->allowFailures(false)
            ->dispatch();

        return $proof->update(['job_batch_id' => $batch->id]);
    }

    public static function getMIME(string $extension)
    {
        $extension = strtolower($extension);

        switch ($extension) {
            case 'doc':
            case 'dot':
                return 'application/msword';
            case 'docx':
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            case 'xls':
            case 'xlt':
            case 'xla':
                return 'application/vnd.ms-excel';
            case 'xlsx':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            case 'ppt':
            case 'pot':
            case 'pps':
            case 'ppa':
                return 'application/vnd.ms-powerpoint';
            case 'pptx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
            case 'pdf':
                return 'application/pdf';
            case 'txt':
            case 'csv':
                return 'text/plain';
            case 'html':
                return 'text/html';
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'png':
                return 'image/png';
            case 'bmp':
                return 'image/bmp';
            case 'gif':
                return 'image/gif';
            case 'mp4':
                return 'video/mp4';
            case 'm4v':
                return 'video/x-m4v';
            case 'wmv':
                return 'video/x-ms-wmv';
            case 'mov':
                return 'video/quicktime';
            case 'avi':
                return 'video/x-msvideo';
            case 'mgp':
                return 'audio/mgp';
            case 'mpg':
            case 'mpeg':
            case 'mpe':
                return 'video/mpeg';
            case 'flv':
                return 'video/x-flv';
            case 'mkv':
                return 'video/x-matroska';
            case 'dwf':
                return 'drawing/x-dwf';
            case 'dxf':
                return 'application/dxf';
            case 'dwg':
                return 'application/acad';
            case 'ai':
            case 'eps':
                return 'application/postscript';
            default:
                return 'application/octet-stream';
        }
    }

    public static function mergePaths($path1, $path2)
    {
        $paths = func_get_args();
        $last_key = func_num_args() - 1;
        array_walk($paths, function (&$val, $key) use ($last_key) {
            switch ($key) {
                case 0:
                    $val = rtrim($val, '/ ');
                    break;
                case $last_key:
                    $val = ltrim($val, '/ ');
                    break;
                default:
                    $val = trim($val, '/ ');
                    break;
            }
        });

        $first = array_shift($paths);
        $last = array_pop($paths);
        $paths = array_filter($paths); // clean empty elements to prevent double slashes
        array_unshift($paths, $first);
        $paths[] = $last;
        return implode('/', $paths);
    }

    public static function ViewerComponent(ProofFile $proofFile): string 
    {
        #Log::info("viewer立ち上げ");
        #Log::info("入力配列", [$proofFile['annotations']]);
#
        #$i = 0;
        #$jsonResults = [];
        #if (!empty($proofFile['annotations'])) {
        #    foreach ($proofFile['annotations'] as $index => $annotation) {
        #        Log::info("関数入力", [$annotation]);
        #        $jsonResult = self::convertXmlToJson($annotation); #, $i);
        #        //$encoded = json_decode($jsonResult, true)[0];
        #        Log::info("関数出力", [$jsonResult]);
        #        $jsonResults[$index] = $jsonResult;
        #        $i = $i + 1;
        #    }
        #    $proofFile['annotations'] = $jsonResults;
        #    Log::info("出力配列", [$proofFile['annotations']]);
        #} else {
        #    $proofFile['annotations'] = null;
        #    Log::info("proof_file", [$proofFile['annotations']]);
        #}

        return  $proofFile->is_fabric_viewer
        ? "Viewer/FabricWebViewer"
        : "Viewer/ExpressWebViewer";
    }

    static function convertXmlToJson($xml_str) {
        // XMLデータの前処理
        #$xml_str = trim($xml_str, '"');
        #$xml_str = stripslashes($xml_str);
        #
        #// 正規表現を使用して属性を抽出
        #preg_match('/page=["\'](\\d+)["\']/', $xml_str, $page_match);
        #preg_match('/rect=["\']([\d.,]+)["\']/', $xml_str, $rect_match);
        #preg_match('/color=["\'](#?[A-Za-z0-9]+)["\']/', $xml_str, $color_match);
        #preg_match('/title=["\'](.*?)["\']/s', $xml_str, $title_match);
        #preg_match('/subject=["\'](.*?)["\']/s', $xml_str, $subject_match);
        #
        #// 抽出した値を取得
        #$page = isset($page_match[1]) ? intval($page_match[1])+1 : 1; // ページ番号を0から1に変換
        #$rect = isset($rect_match[1]) ? $rect_match[1] : "0,0,100,100";
        #$color_value = isset($color_match[1]) ? $color_match[1] : "#E44234";
        #$title = isset($title_match[1]) ? $title_match[1] : "Admin01 System";
        #$subject = isset($subject_match[1]) ? strtolower($subject_match[1]) : "rectangle";
        #
        #// rect属性を解析して位置とサイズに変換
        #$rect_parts = explode(',', $rect);
        #Log:info("rect_parts", [$rect_parts]);
        #$x = isset($rect_parts[0]) ? floatval($rect_parts[0]) : 0;
        #$y = isset($rect_parts[1]) ? floatval($rect_parts[1]) : 0;
        #$width = isset($rect_parts[2]) && isset($rect_parts[0]) ? floatval($rect_parts[2]) - floatval($rect_parts[0]) : 100;
        #$height = isset($rect_parts[3]) && isset($rect_parts[1]) ? floatval($rect_parts[3]) - floatval($rect_parts[1]) : 100;
        #
        #// 色をカラーマップで逆変換
        #$reverse_color_map = [
        #    '#E44234' => 'red',
        #    '#1A4FFF' => 'blue',
        #    '#00B050' => 'green',
        #    '#FFFF00' => 'yellow',
        #    '#FF9C00' => 'orange',
        #    '#7030A0' => 'purple',
        #    '#FF00FF' => 'pink',
        #    '#00FFFF' => 'cyan',
        #    '#A52A2A' => 'brown',
        #    '#808080' => 'gray',
        #    '#000000' => 'black',
        #    '#FFFFFF' => 'white',
        #    // ... existing code ...
        #];
        #$color = isset($reverse_color_map[$color_value]) ? $reverse_color_map[$color_value] : 'red';
        #
        #// IDをランダムに生成
        #$annotation_id = mt_rand(1000000000000, 9999999999999);
        #$comment_id = mt_rand(1000000000000, 9999999999999);
        #
        #// 出力データの作成
        #$output = [
        #    [
        #        'annotation' => [
        #            'id' => $annotation_id,
        #            'type' => $subject,
        #            'position' => [
        #                'x' => $x,
        #                'y' => $y
        #            ],
        #            'size' => [
        #                'width' => $width,
        #                'height' => $height
        #            ],
        #            'number' => 1,
        #            'color' => $color,
        #            'page' => $page,
        #            'comment_text' => null,
        #            'comments' => [
        #                [
        #                    'id' => $comment_id,
        #                    'author' => $title,
        #                    'timestamp' => '2025-03-18T18:24:50.918Z',
        #                    'text' => null,
        #                    'annotation_id' => $annotation_id,
        #                    'parent_id' => $comment_id
        #                ]
        #            ]
        #        ]
        #    ]
        #];
        #
        #$encoded_json = json_encode($output, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        #
        #// JSON文字列内のエスケープされたダブルクォーテーションを修正
        #// 注意: 通常のJSONでは使用しないでください。このケースの特殊な要件に対応するものです
        #$encoded_json = str_replace('\"', '"', $encoded_json);
#
        #return $encoded_json;
        
        #一つの場合
        $json_str = <<<LONGTEXT
                [{"annotation":{"id":1742320436565,"type":"rectangle","position":{"x":281.5,"y":160},"size":{"width":300,"height":208},"number":1,"color":"red","page":1,"comment_text":null,"comments":[{"id":1742320436747,"author":"Admin01 System","timestamp":"2025-03-18T17:53:56.364Z","text":null,"annotation_id":1742320436565,"parent_id":1742320436747}]}}]
                LONGTEXT;
        $json_str = json_decode($json_str, true)[0];
        

        return $json_str;
    }
        #$二つの場合
        #$json_str = <<<LONGTEXT
        #        [{"annotation": {"id": 1742322933033, "page": 1, "size": {"width": 187, "height": 279}, "type": "rectangle", "color": "red", "number": 1, "comments": [{"id": 1742322933117, "text": null, "author": "Admin01 System", "parent_id": 1742322933117, "timestamp": "2025-03-18T18:35:32.629Z", "annotation_id": 1742322933033}], "position": {"x": 290.5, "y": 6}, "comment_text": null}}, {"annotation": {"id": 1742323008304, "page": 1, "size": {"width": 158, "height": 96}, "type": "rectangle", "color": "red", "number": 2, "comments": [{"id": 1742323009070, "text": null, "author": "Admin01 System", "parent_id": 1742323009070, "timestamp": "2025-03-18T18:36:48.085Z", "annotation_id": 1742323008304}], "position": {"x": 244.5, "y": 439}, "comment_text": null}}]
        #        LONGTEXT;
        #$json_str = json_decode($json_str, true)[$i]; #0];
        
        #一つの場合
        #$json_str = <<<LONGTEXT
        #        [{"annotation":{"id":1742320436565,"type":"rectangle","position":{"x":281.5,"y":160},"size":{"width":300,"height":208},"number":1,"color":"red","page":1,"comment_text":null,"comments":[{"id":1742320436747,"author":"Admin01 System","timestamp":"2025-03-18T17:53:56.364Z","text":null,"annotation_id":1742320436565,"parent_id":1742320436747}]}}]
        #        LONGTEXT;
        #$json_str = json_decode($json_str, true)[0];
        

    #    return $json_str;
    #}

    //$a = ["[{\"annotation\":{\"id\":1742320436565,\"type\":\"rectangle\",\"position\":{\"x\":281.5,\"y\":160},\"size\":{\"width\":300,\"height\":208},\"number\":1,\"color\":\"red\",\"page\":1,\"comment_text\":null,\"comments\":[{\"id\":1742320436747,\"author\":\"Admin01 System\",\"timestamp\":\"2025-03-18T17:53:56.364Z\",\"text\":null,\"annotation_id\":1742320436565,\"parent_id\":1742320436747}]}}]"];
    //$b = [{"annotation":{"id":1742322291182,"type":"rectangle","position":{"x":309.5,"y":160},"size":{"width":102,"height":149},"number":1,"color":"red","page":1,"comment_text":null,"comments":[{"id":1742322291746,"author":"Admin01 System","timestamp":"2025-03-18T18:24:50.918Z","text":null,"annotation_id":1742322291182,"parent_id":1742322291746}]}}]

    //$c = [{"annotation":{"id":1742322291182,"type":"rectangle","position":{"x":309.5,"y":160},"size":{"width":102,"height":149},"number":1,"color":"red","page":1,"comment_text":null,"comments":[{"id":1742322291746,"author":"Admin01 System","timestamp":"2025-03-18T18:24:50.918Z","text":null,"annotation_id":1742322291182,"parent_id":1742322291746}]}}]
}
