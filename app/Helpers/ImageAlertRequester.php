<?php

namespace App\Helpers;

use App\Models\Proof;
use App\Enums\PRMEnum;
use App\Models\Cartridge;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Client\Response;

// 画像アラートAPIへのリクエストヘルパークラス
class ImageAlertRequester
{
    public function __construct(Proof $proof, string $pdf_path, string $cache_key)
    {
        $this->proof = $proof;
        $this->pdf_path = $pdf_path;
        $this->cache_key = $cache_key;
        $this->client = Http::baseUrl(config('services.ethics-confirm-assistant.baseurl'));
    }

    public function request()
    {
        // 画像のロードと変換
        $content = Storage::disk('s3')->get($this->pdf_path);
        $pdf_file_name = pathinfo($this->pdf_path, PATHINFO_FILENAME);
        $cartridgeElements = Cartridge::makeCartridgeElementsByThisEnv();
        
        // python用サーバーにリクエストすること。現在はサンプル
        $response = $this->client->attach(
            'upload_file', $content, $pdf_file_name)
            ->post('/image-alert',[
                'cache_key' => $this->cache_key,
                'sub_concepts_json' => $cartridgeElements->toJson()
            ]);

        $response_json = $response->json();

        return $response_json;
    }
}
