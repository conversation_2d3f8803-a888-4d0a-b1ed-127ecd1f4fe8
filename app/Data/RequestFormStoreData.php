<?php

namespace App\Data;

use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use <PERSON><PERSON>\LaravelData\Data;

class RequestFormStoreData extends Data
{
    public string $uuid;

    public string $title;

    public CarbonImmutable $externalDeadlineAt;

    /** @var array<int> */
    public array $ips;

    public int $categoryId;

    public RequestFormPublishDateData $publishDate;

    public RequestFormFilesData $files;

    public ?string $supplement = null;

    public function __construct(
        string $uuid,
        string $title,
        string $externalDeadlineAt,
        array $ips,
        int $categoryId,
        array $publishDate,
        array $files,
        ?string $supplement = null
    ) {
        $this->uuid = $uuid;
        $this->title = $title;
        $this->externalDeadlineAt = CarbonImmutable::createFromFormat('Y-m-d H:i:s', $externalDeadlineAt);
        $this->ips = $ips;
        $this->categoryId = $categoryId;
        $this->publishDate = new RequestFormPublishDateData(...$publishDate);
        $this->files = new RequestFormFilesData($files);
        $this->supplement = $supplement;
    }

    public static function fromRequest(Request $request)
    {
        $files = data_get($request->all('files'), 'files', []);
        $elementFiles = data_get($request->all('element_files') ?? [], 'element_files', []);
        if (!empty($elementFiles)) {
            $files = array_merge($files, $elementFiles);
        }
        return new self(
            uuid: $request->input('uuid', ''),
            title: $request->input('title', ''),
            externalDeadlineAt: $request->input('external_deadline_at'),
            ips: $request->input('ips', []),
            categoryId: $request->input('category_id', ''),
            publishDate: $request->publish_date ?? [
                'start' => null,
                'end' => null,
            ],
            files: $files,
            supplement: $request->input('supplement'),
        );
    }
}
