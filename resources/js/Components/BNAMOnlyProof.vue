<template>
  <div>
    <v-switch
      label="BNAMのみ"
      hide-details
      dense
      :input-value="this.filter.am_only"
      @change="change"
    />
  </div>
</template>

<script>
import { filterSerialize as serialize } from '../lib/utils';

export default {
  name: 'BNAMOnlyProof',
  props: ['filter', 'target'],
  methods: {
    reset() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: false,
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },
    submit() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: true,
        },
        page: 1,
      };

      newFilter.xp_only = false;
      newFilter.al_only = false;
      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },
    change(active) {
      active ? this.submit() : this.reset();
    },
  },
};
</script>
