<?php

namespace App\Actions\Proof;

use App\Actions\BinaryToUploadedFileAction;
use App\Actions\Proof\CreateProofFileAndUploadAction;
use App\Data\RequestFormPdfData;
use App\Data\RequestFormPublishDateData;
use App\Http\API\EthicsConfirmAssistant;
use App\Models\Proof;
use App\Models\ProofFile;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

/**
 * 「PDFに対する説明」、「公開開始日」、「公開終了日」のいずれか一つでも値がある場合、
 * 1ページ（メタ情報が記入）を追加したPDFを生成する。
 * その後、PDFをS3にアップロードしてからProofFileモデルを生成する。
 *
 * ※旧申請入力フォームの処理("/unnatural-text-check", "/image-alert")に入れる前に処理を行う。
 */
class GenerateOldVersionOfProofPdfAction
{
    public function __construct(
        private EthicsConfirmAssistant $api,
        private CreateProofFileAndUploadAction $createProofFileAndUploadAction,
        private BinaryToUploadedFileAction $binaryToUploadedFileAction,
        private MergePdfAction $mergePdfAction
    ) {
    }

    /**
     * @param Proof $proof
     * @param RequestFormPdfData $pdf
     * @param RequestFormPublishDateData $publishDate
     */
    public function execute(
        Proof $proof,
        Collection $pdfs,
        RequestFormPublishDateData $publishDate
    ): ProofFile {
        $merged = $pdfs->map(function (RequestFormPdfData $pdf) use ($publishDate) {
            // add comment page
            return $this->shouldAddCommentPage($pdf, $publishDate) ?
                $this->getPdfWithAddCommentPage($pdf, $publishDate) :
                $pdf->value ;
        })->pipe(function (Collection $files) use ($proof) {
            // merge pdf
            return $this->mergePdfAction->execute(
                fileBaseName: $this->getMergedFileName($proof),
                files: $files
            );
        });

        return $this->createProofFileAndUploadAction->execute(
            $proof,
            $merged,
            config('filesystems.disks.s3.converted_dir'),
            ProofFile::TYPE_CONVERTED
        );
    }

    private function getPdfWithAddCommentPage(
        RequestFormPdfData $pdf,
        RequestFormPublishDateData $publishDate
    ): UploadedFile {
        $response = $this->api->makeCommentPage($pdf, $publishDate);

        return $this->binaryToUploadedFileAction->execute(
            $pdf->value->getClientOriginalName(),
            $response->body()
        );
    }

    /**
     * @param RequestFormPdfData $pdf
     * @param RequestFormPublishDateData $publishDate
     */
    private function shouldAddCommentPage(
        RequestFormPdfData $pdf,
        RequestFormPublishDateData $publishDate
    ): bool {
        return $pdf->hasComment() || $publishDate->hasStart() || $publishDate->hasEnd();
    }

    private function getMergedFileName(Proof $proof): string
    {
        $fileName = $proof->title;

        // $proof_id = str_pad($proof->id, 5, '0', STR_PAD_LEFT);
        $proof_no = $proof->proof_number;

        return Str::of($fileName)
            ->prepend("{$proof_no}_")
            ->replaceMatches('/\//', '_')
            ->append('.pdf')
            ->__toString();
    }
}
