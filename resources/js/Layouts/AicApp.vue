<template>
  <v-app style="background-color: rgba(0,0,0,0);">
    <slot />
    <div id="canvas" style="position: fixed; display: block; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; background-color: white;">
      <canvas id="bgCanvas" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: -1; background-color: white;"></canvas>
    </div>
  </v-app>
</template>

<script>
import { set_bg as setBg, get_app as getApp } from '../Pages/Aic/Components/utils/site.js';
import '../Pages/Aic/Components/utils/site.css';
import { onMounted } from 'vue';
export default {
  name: 'AicApp',
  data() {
    return {
    };
  },
  setup() {
    onMounted(() => {
      const canvas = document.getElementById('canvas');
      console.log('setup onMounted');
      console.log('canvas', canvas);
      console.log('bgApp');
      setBg(canvas);
      console.log('after setBg');
      const bgApp = getApp();
      console.log('bgApp', bgApp);
    });
  },
};
</script>

<style>
</style>
