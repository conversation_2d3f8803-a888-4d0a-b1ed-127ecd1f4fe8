<?php

namespace App\Helpers;

use App\Http\API\AwsCloudSearch;
use App\Models\ProofCloudSearch;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class ProofCloudSearchManager
{
    // Upload Document Type
    const DOCUMENT_UPLOAD_TYPE_ADD = 'add';
    const DOCUMENT_UPLOAD_TYPE_DELETE = 'delete';

    // API Gateway - tf-bapi-cloudsearch
    const API_GATEWAY_BASE_URL = "https://mo1j4zxed9.execute-api.ap-northeast-1.amazonaws.com";
    const API_GATEWAY_PARSE_PATH = "/parse";

    /**
     * AWS Lambda Function(tf-bapi-cloudsearch-parser)からPDF情報取得
     *
     * @param  string  $path
     * "bnam-bapi@【倫理】回答アーカイブ/(0718修正）Testファイル01.pdf"
     *
     * @return array
     * [
     *      "title" => string,
     *      "content" => string,
     *      "category" => string,
     *      "keywords" => <string>array,
     *      "annotations" => <string>array,
     *      "annotation_authors" => <string>array,
     *      "page_count" => int,
     *      "created" => string,
     *      "thumbnail" => string
     *      "bucket" => string,
     *      "key" => string,
     * ]
     */
    public static function parse(string $path)
    {
        $splited = self::splitPath($path);

        $parser = new PdfParser($splited['bucket'], $splited['key']);
        
        return $parser->parse();
    }

    /**
    * ProofCloudSearchManager@parseから取得したPDF情報をRDSとCloudSearchに登録
    *
    * @param  array  $fields
    * [
    *      "title" => string,
    *      "content" => string,
    *      "category" => string,
    *      "keywords" => <string>array,
    *      "annotations" => <string>array,
    *      "annotation_authors" => <string>array,
    *      "page_count" => int,
    *      "created" => string,
    *      "thumbnail" => string
    *      "bucket" => string,
    *      "key" => string,
    * ]
    *
    * @return Collection
    * [
    *    "status": string,
    *    "adds": int,
    *    "deletes": int,
    *    "warnings": object<array>,
    *    "@metadata": object,
    *    "headers": object,
    *    "transferStats": object
    * ]
    */
    public static function upload(array $fields)
    {
        try {
            DB::beginTransaction();
            
            $model = ProofCloudSearch::create($fields);
            $document = self::createDocument($model);
            $result = AwsCloudSearch::uploads([$document]);

            DB::commit();

            return [
                'document' => $document,
                'model' => $model,
                'result' => $result
            ];
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    /**
     * @param  string $path
     * "bnam-bapi@【倫理】回答アーカイブ/(0718修正）Testファイル01.pdf"
     *
     * @return Collection
     */
    public static function uploadDocument(string $path)
    {
        $fields = self::parse($path);
        $result = self::upload($fields);
        
        return $result;
    }
    
    public static function deleteDocument(ProofCloudSearch $model)
    {
        $document = self::createDocument($model, true);
        $result = AwsCloudSearch::uploads([$document]);
        $model->delete();
        
        return [
            'document' => $document,
            'result' => $result
        ];
    }
    
    /**
     * CloudSearchからPDF検索結果を取得する
     * @param  string|null  $query
     * @param  int|null  $page
     * @param  array  $filterQuery
     *
     * @return array
     * [
     *      "hits" => <object>array
     *      "facets" => <object>array
     * ]
     */
    public static function search(string|null $query, int|null $page, array $filterQuery = [])
    {
        if (is_null($query)) {
            return [];
        }

        $results = AwsCloudSearch::search(
            $query,
            $page ?? 1,
            self::createFilterQuery($filterQuery)
        );

        return Arr::only($results, ['hits', 'facets']);
    }

    /**
     * CloudSearchからPDF検索のサジェスト(title)を取得する
     * @param string|null $query
     *
     * @return
     * [
     *      "suggest" => <object>array
     * ]
     */
    public static function suggest(string|null $query)
    {
        if (is_null($query)) {
            return [];
        }

        $results = AwsCloudSearch::suggest($query);

        return Arr::only($results, ['suggest']);
    }
    
    /**
     * {bucket}@{key}
     *
     * @param  string $path
     * @return array
     */
    public static function splitPath(string $path)
    {
        $delim = '@';
        
        $splited = explode($delim, $path);

        $bucket = array_shift($splited);
        $key = count($splited) > 1 ? implode($delim, $splited) : $splited[0];
        
        return [
            'bucket' => $bucket,
            'key' => $key
        ];
    }

    /**
     * CloudSearchにアップロードするドキュメントのフォーマットを生成
     * https://docs.aws.amazon.com/ja_jp/cloudsearch/latest/developerguide/preparing-data.html
     *
     * @param  ProofCloudSearch $model
     * @param  bool $isDelete
     *
     * @return array
     * [
     *      'id' => string,
     *      'type' => string,
     *      'fields' => <object>array,
     * ]
     * or
     * [
     *      'id' => string,
     *      'type' => string,
     * ]
     */
    private static function createDocument(ProofCloudSearch $model, bool $isDelete = false): array
    {
        $document = [];
        $document['id'] = $model->id;

        if ($isDelete) {
            $document['type'] = self::DOCUMENT_UPLOAD_TYPE_DELETE;
        } else {
            $document['type'] = self::DOCUMENT_UPLOAD_TYPE_ADD;
            $document['fields'] = self::parseDocumentFields($model->toArray());
        }

        return $document;
    }
    
    /**
     * CloudSearchにアップロードするドキュメントのfieldsを取得
     * CloudSearch tf-bapiのIndexing Options参考
     *
     * @param  array $array
     * @return array
     */
    private static function parseDocumentFields(array $array): array
    {
        return Arr::only($array, [
            "title", "content",
            "category", "keywords",
            "annotations", "annotation_authors",
            "page_count", "created"
        ]);
    }
    
    /**
     * CloudSearchのPDF検索でフィルターをかけるQueryを作成する。
     *
     * @param  array $filterQuery
     * [
     *      "annotation_authors" => <string>array,
     *      "created" => [
     *          "from" => "2020-01-01 12:00:00",
     *      ],
     * ]
     *
     * @return string
     */
    private static function createFilterQuery(array $filterQuery): string
    {
        $filter = new CloudSearchFilter();

        foreach ($filterQuery as $key => $value) {
            $filter->set($key, $value);
        }

        return $filter->parse();
    }
}

/**
 * CloudSearchのPDF検索でフィルターをかけるQueryを作成する。
 * https://docs.aws.amazon.com/ja_jp/cloudsearch/latest/developerguide/search-api.html#submitting-search-requests-api
 */
class CloudSearchFilter
{
    private $values;

    public function __construct()
    {
        $this->values = collect($this->values);
    }

        
    /**
     * マッピングする各メソッドを呼び出す。
     *
     * @param  string $method
     * @param  mixed $value
     * @return void
     */
    public function set(string $method, $value)
    {
        return call_user_func([$this, $method], $value);
    }

    /**
     * 生成したフィルター用テキスト文字列をマージする
     *
     * @return string
     */
    public function parse(): string
    {
        if ($this->values->count() == 0) {
            return '';
        }
        
        if ($this->values->count() == 1) {
            return sprintf("(and %s)", $this->values->first());
        }

        $result = $this->values
                    ->map(fn ($value) => sprintf("(and %s)", $value))
                    ->join(" ");
        
        return sprintf("(and %s)", $result);
    }
    
    /**
     * createdデータをフィルター用に変換
     * @param  array $created
     * [
     *      "from" => string,
     *      "to" => string
     * ]
     *
     * @return void
     */
    private function created(array $created)
    {
        $methodName = __FUNCTION__;

        $parseCreated = function (string $key) use ($created) {
            $result = Str::of('');

            $isFrom = $key === 'from';
            if (Arr::has($created, $key)) {
                // DateTimeフォーマット変更
                $formatedDateTime = $isFrom ?
                    Carbon::parse($created[$key])->format('Y-m-d\TH:i:s\Z') :
                    Carbon::parse($created[$key])->endOfMonth()->format('Y-m-d\TH:i:s\Z');

                $result = $result->append(sprintf("'%s'", $formatedDateTime));
                
                $bracket = $isFrom ? '[' : ']';
            } else {
                $bracket = $isFrom ? '{' : '}';
            }

            $value = $isFrom ?
                    $result->prepend($bracket) :
                    $result->append($bracket);

            return $value;
        };

        $from = $parseCreated('from');
        $to = $parseCreated('to');

        $result = sprintf("%s:%s,%s", $methodName, $from, $to);

        $this->values->push($result);
    }
    
    /**
     * annotation_authorsデータをフィルター用に変換
     *
     * @param  array|Collection $authors
     * [
     *      "Test (<EMAIL>)",
     *      "Test2 (<EMAIL>)",
     * ]
     *
     * @return void
     */
    private function annotation_authors(array|Collection $authors)
    {
        $methodName = __FUNCTION__;
        
        $result = collect($authors)
            ->map(fn ($author) => sprintf("%s:'%s'", $methodName, $author))
            ->join(" ");
    
        $this->values->push($result);
    }
}
