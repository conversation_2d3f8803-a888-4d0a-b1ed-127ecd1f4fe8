<?php

namespace App\EloquentBuilder;

use App\Models\Notice;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;

class NoticeBuilder extends Builder
{
    public function whereVisible()
    {
        return $this->where('visible', true);
    }

    public function wherePublish(Carbon $date)
    {
        return $this->where('published_at', '<=', $date)->where('closed_at', '>', $date);
    }

    public function whereClose(Carbon $date)
    {
        return $this->where('closed_at', '>=', $date);
    }

    public function latestVisible()
    {
        return $this->latest('visible');
    }

    public function latestUpdate()
    {
        return $this->latest('updated_at');
    }

    public function whereUserTypeId(int $id)
    {
        return $this->where('user_type_id', $id);
    }

}