<?php

namespace App\Data;

use Illuminate\Http\UploadedFile;
use Spa<PERSON>\LaravelData\Attributes\WithoutValidation;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Optional;

class RequestFormTextData extends Data
{
    public UploadedFile $value;
    public ?string $comment = null;

    public function __construct(UploadedFile $value, ?string $comment = null, ?int $index = null)
    {
        $this->value = $value;
        $this->comment = $comment;
    }

    private function stringToUploadedFile(string $content, ?int $index = null): UploadedFile
    {
        $num = is_null($index) ? null : $index + 1;

        // 一時ファイルのパスを生成(年月日時分秒ミリ秒.txt)
        $fileName = "テキスト{$num}_" . now()->format('YmdHisv');
        $path = sys_get_temp_dir() . '/' . $fileName . '.txt';

        // 文字列をファイルに書き込み
        file_put_contents($path, $content);

        // 書き込んだファイルをアップロードファイルに変換
        return new UploadedFile($path, basename($path), 'text/plain');
    }
}
