<?php

namespace App\Http\Controllers\Member;

use App\Actions\Proof\UpsertAnnotationTemplateAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Member\AnnotaionTemplateController\UpsertRequest;
use App\Models\AnnotationTemplate;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class AnnotationTemplateController extends Controller
{
    /**
     * 倫理確認カテゴリーリスト
     */
    public function index()
    {
        $paginator = AnnotationTemplate::query()
                    ->self()
                    ->orderByDesc("id")
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

        return Inertia::render('Member/Util/AnnotationTemplate/Index', [
            'paginator' => $paginator,
        ]);
    }

    public function newIndex()
    {
        $paginator = AnnotationTemplate::query()
            ->self()
            ->orderByDesc("id")
            ->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        // 作成者の情報を取得
        $paginator->getCollection()->transform(function ($item) {
            $item->user = $item->user()->first()->last_name . ' ' . $item->user()->first()->first_name;
            return $item;
        });

        return Inertia::render('Member/Util/AnnotationTemplate/NewIndex', [
            'paginator' => $paginator,
        ]);
    }

    public function show(AnnotationTemplate $annotationTemplate)
    {
        $this->authorize('view', $annotationTemplate);

        return Inertia::render('Member/Util/AnnotationTemplate/Show', [
            'annotationTemplate' => $annotationTemplate
        ]);
    }

    public function newShow(AnnotationTemplate $annotationTemplate)
    {
        $this->authorize('view', $annotationTemplate);

        return Inertia::render('Member/Util/AnnotationTemplate/NewShow', [
            'annotationTemplate' => $annotationTemplate
        ]);
    }

    public function create()
    {
        return Inertia::render('Member/Util/AnnotationTemplate/Create', []);
    }

    public function newCreate()
    {
        return Inertia::render('Member/Util/AnnotationTemplate/NewCreate', []);
    }

    public function upsert(UpsertRequest $request, UpsertAnnotationTemplateAction $action)
    {
        if($request->id) {
            // update
            $this->authorize('update', AnnotationTemplate::find($request->id));
        }

        try {
            DB::transaction(
                fn () =>
                $action->execute($request->data(), auth()->user())
            );

            return redirect()
                    ->route('member.utils.annotation-templates.index', [], self::HTTP_STATUS_SEE_OTHER)
                    ->with('snackbar', [
                        'color' => 'primary',
                        'icon' => 'mdi-check-circle',
                        'title' => '成功',
                        'message' => '注釈テンプレートを保存しました。'
                    ]);
        } catch (\Throwable $th) {
            return redirect()
                    ->back()
                    ->with('snackbar', [
                        'color' => 'error',
                        'icon' => 'mdi-alert',
                        'title' => '失敗',
                        'message' => $th->getMessage()
                    ]);
        }
    }

    public function destroy(AnnotationTemplate $annotationTemplate)
    {
        $this->authorize('delete', $annotationTemplate);

        try {
            DB::beginTransaction();

            $annotationTemplate->delete();

            DB::commit();

            return redirect()
                    ->route('member.utils.annotation-templates.index', [], self::HTTP_STATUS_SEE_OTHER)
                    ->with('snackbar', [
                        'color' => 'primary',
                        'icon' => 'mdi-check-circle',
                        'title' => '成功',
                        'message' => "注釈テンプレートを削除しました。"
                    ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
