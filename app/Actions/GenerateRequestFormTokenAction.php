<?php

namespace App\Actions;

class GenerateRequestFormTokenAction
{
    public const PREFIX = 'EGFUKLVWUJCY';
    public const SECRET_KEY = 'A7m5Z_x)';
    public const ALGHORITHM = 'sha256';

    /*
     * Generate token for request form
     *
     * @param string $email
     *
     * @return string
     */
    public function execute(string $email): string
    {
        $data = self::PREFIX . $email;

        return hash_hmac(self::ALGH<PERSON>ITHM, $data, self::SECRET_KEY);
    }
}
