<?php

namespace App\Events;

use App\Models\Proof;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class ProofCommentChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $proof;
    public $proofComments;
    public $writer;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Collection $proofComments, Proof $proof, User $writer)
    {
        $this->proofComments = $proofComments;
        $this->proof = $proof;
        $this->writer = $writer;
    }
}
