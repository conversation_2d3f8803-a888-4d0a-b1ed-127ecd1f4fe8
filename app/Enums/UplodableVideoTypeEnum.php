<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static General()
 * @method static static Legal()
 */
final class UplodableVideoTypeEnum extends Enum
{
    public string $mime;

    const M4v    =   'm4v';
    const Mp4    =   'mp4';
    const Wmv    =   'wmv';
    const Avi    =   'avi';
    const Mpeg   =   'mpeg';
    const Mkv    =   'mkv';

    public function mime()
    {
        return match ($this->value) {
            self::M4v => 'video/x-m4v',
            self::Mp4 => 'video/mp4',
            self::Wmv => 'video/x-ms-wmv',
            self::Avi => 'video/avi',
            self::Mpeg => 'video/mpeg',
            self::Mkv => 'video/x-matroska',
            default => ''
        };
    }

    public function toArray()
    {
        $this->mime = $this->mime();

        return $this;
    }
}
