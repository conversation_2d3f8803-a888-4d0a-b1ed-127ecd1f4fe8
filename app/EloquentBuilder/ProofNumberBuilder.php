<?php

namespace App\EloquentBuilder;
use App\Models\Proof;
use App\Models\ProofNumber;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class ProofNumberBuilder extends Builder
{
    /**
     * 申請番号を取得する
     *
     * @param string $company_domain
     * @return string|null
     */
    public static function getProofNumber(string $company_domain): ?string
    {
        // 会社識別ドメインから申請番号を取得する
        $proof_number = ProofNumber::where('company_domain', $company_domain)->first();
        if ($proof_number) {
            // 会社コードとproof.proof_numberの先頭が一致する物で最もidが大きいものを取得する
            $proof = Proof::where('proof_number', 'like', $proof_number->company_code . '%')
                ->orderBy('id', 'desc')
                ->first();
            if ($proof) {
                // 申請番号を取得する
                $number = $proof->proof_number;
                Log::info('申請番号取得', ['number' => $number]);
                // 申請番号の数字部分を取得する
                $nextNumber = substr($number, strlen($number) - strlen($proof_number->company_code));
                Log::info('申請番号の数字部分取得', ['nextNumber' => $nextNumber]);
                // 申請番号の数字部分をインクリメントする
                (int)$nextNumber++;
                Log::info('申請番号の数字部分インクリメント', ['nextNumber' => $nextNumber]);
                // 申請番号を生成する
                return $proof_number->company_code . '-' . str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
            }else{
                // 申請番号が存在しない場合は、会社コードを元に申請番号を生成する
                $number = 1;
                // 申請番号を生成する
                return $proof_number->company_code . '-' . str_pad($number, 5, '0', STR_PAD_LEFT);
            }
        } else {
            // 会社識別ドメインが存在しない場合は、BNXPで生成する
            $proof_number = ProofNumber::where('company_domain', 'default')->first();
            if ($proof_number) {
                // 会社コードとproof.proof_numberの先頭が一致する物で最もidが大きいものを取得する
                $proof = Proof::where('proof_number', 'like', $proof_number->company_code . '%')
                    ->orderBy('id', 'desc')
                    ->first();
                if ($proof) {
                    // 申請番号を取得する
                    $number = $proof->proof_number;
                    Log::info('申請番号取得', ['number' => $number]);
                    // 申請番号の数字部分を取得する
                    $nextNumber = substr($number, strlen($number) - strlen($proof_number->company_code));
                    Log::info('申請番号の数字部分取得', ['nextNumber' => $nextNumber]);
                    // 申請番号の数字部分をインクリメントする
                    (int)$nextNumber++;
                    Log::info('申請番号の数字部分インクリメント', ['nextNumber' => $nextNumber]);
                    // 申請番号を生成する
                    return $proof_number->company_code . '-' . str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
                } else {
                    // 申請番号が存在しない場合は、会社コードを元に申請番号を生成する
                    $number = 1;
                    // 申請番号を生成する
                    return $proof_number->company_code . '-' . str_pad($number, 5, '0', STR_PAD_LEFT);
                }
            }
            return null;
        }
        return null;
    }

    public static function getCompanyCode(string $company_domain): ?string
    {
        // 会社識別ドメインから会社コードを取得する
        $proof_number = ProofNumber::where('company_domain', $company_domain)->first();
        if ($proof_number) {
            Log::info('会社コード取得', ['company_code' => $proof_number->company_code]);
            return $proof_number->company_code;
        }else{
            return ProofNumber::where('company_domain', 'default')->first()->company_code;
        }
        return null;
    }

    public function scopeWhereCompanyDomain($query, string $company_domain): Builder
    {
        return $query->where('company_domain', $company_domain);
    }
}