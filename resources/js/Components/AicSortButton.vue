<template>
  <v-btn
    color="white"
    @click="clickSort()"
    small
    v-model="filterDrawer"
    style="padding-right: 10px; padding-left: 10px; height: 28px; box-shadow: none; border: solid 1px; border-color: black !important;"
    light
  >
    <v-icon color="black">
      mdi-menu-down
    </v-icon>
    <span style="font-size: 14px; font-weight: bold; color: black; ">ソート</span>&nbsp;
  </v-btn>
</template>

<script>
export default {
  props: ['filterDrawer'],
  computed: {
    _filterDrawer: {
      get() {
        return this.$page.props.filterDrawer;
      },
    },
  },
  methods: {
    clickSort() {
      this.$emit('update:filterDrawer', true);
    },
  }
};
</script>