<?php

namespace App\Helpers;

use Exception;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Yaml\Yaml;

// 画像アラートAI用質問データヘルパークラス
class ImageAlertExamHelper
{
    const FILE_NAME = "exam_items.yaml";

    /*
     * Yaml形式のデータを取得
     */
    public function getYamlData()
    {
        // このソースと同じディレクトリにデータが置いてあります。
        // TODO:laravelとして、正しい置き場があれば移動させること
        $current = dirname(__FILE__);
        $path = "{$current}/" . self::FILE_NAME;
        $content = file_get_contents($path);

        if ($content === false) {
            return null;
        }

        try {
            $exam_items = Yaml::parse($content);
        } catch (Exception $e) {
            Log::debug(sprintf("Unable to parse the YAML string: %s", $e->getMessage()));
            return null;
        }
        
        return $exam_items;
    }
    
    
    /*
     * API用のプロンプトテキストを取得
     */
    public function getPromptText()
    {
        // 設定ファイルのロード
        $exam_items = $this->getYamlData();

        // プロンプトのリストを生成
        $prompt_list = [];
        foreach ($exam_items as $index => $exam_item) {
            $prompt = sprintf("\"Question: %s Answer:\"", $exam_item["question"]);
            array_push($prompt_list, $prompt);
        }

        $prompt_text = "[";
        $prompt_num = count($prompt_list);
        
        foreach ($prompt_list as $index => $prompt) {
            $prompt_text .= "[{$prompt}]";
            
            if ($index == $prompt_num - 1) {
                $prompt_text .= "]";
            }
            else {
                $prompt_text .= ",";
            }
        }

        return $prompt_text;
    }
}
