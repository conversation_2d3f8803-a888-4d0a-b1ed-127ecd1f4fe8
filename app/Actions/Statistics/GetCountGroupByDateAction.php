<?php

namespace App\Actions\Statistics;

use App\Enums\StatisticsUnitEnum;
use Closure;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Stringable;

class GetCountGroupByDateAction
{
    private string $dateColumn = 'date';
    private string $countColumn = 'count';

    public function execute(
        Model|string $model,
        string $column = 'created_at',
        string $dateformat = StatisticsUnitEnum::Day,
        ?Closure $callback = null,
    ): Collection {
        $dateFormatQuery = $this->convertDateFormatQuery($column, $dateformat);

        $columns = [
            DB::raw("{$dateFormatQuery} as {$this->dateColumn}"),
            DB::raw("COUNT(*) as {$this->countColumn}")
        ];

        return $this->resolveModel($model)
            ->select($columns)
            ->groupByRaw($dateFormatQuery)
            ->when($callback, fn ($query) => $callback($query))
            ->get()
            ->mapWithkeys(fn ($item) => [
                $item[$this->dateColumn] => $item[$this->countColumn]
            ]);
    }

    private function resolveModel(Model|string $model): Model
    {
        return is_string($model) ? resolve($model) : $model;
    }

    private function convertDateFormatQuery(string $column, string $format): string
    {
        return Str::of($format)
            ->pipe(fn (Stringable $format) =>
                $format
                    ->explode('-')
                    ->map(fn (string $char) => "%" . $char)
                    ->implode('-'))
            ->pipe(fn (string $format) =>
                "DATE_FORMAT(`$column`, '$format')");
    }
}
