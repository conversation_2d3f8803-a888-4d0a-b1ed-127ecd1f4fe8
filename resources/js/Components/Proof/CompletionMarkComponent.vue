<template>
  <section class=" d-flex">
    <section
      class="d-flex justify-center align-center text-center mr-4"
      style="width: 150px"
    >
      <section>
        <div>完了印</div>
        <div>
          ({{ currentCompletionMarks.length }} /
          {{ proof.capable_completion_mark_users.length }})
        </div>
      </section>
    </section>

    <section class="flex-grow-1 d-flex flex-column ma-2" style="gap: 8px;">
      <section class="d-flex">
        <v-col cols="2" class="pa-0">
          <span class="font-weight-bold red--text" style="font-size: 16px;">
            スペシャルサポーター
          </span>
        </v-col>

        <v-row class="mb-2">
          <v-col
            v-for="user in specialSupporters"
            :key="user.id"
            sm="4"
            md="3"
            lg="2"
            class="py-1 d-flex align-center"
          >
            <v-checkbox
              color="red"
              class="mt-0"
              :label="user.name"
              :input-value="includeCompletionMark(user)"
              :disabled="disabled"
              hide-details
              @change="completionMarking(user)"
            >
              <template v-slot:label>
                <span class="font-weight-bold red--text">
                  {{ user.name }}
                </span>
              </template>
            </v-checkbox>
          </v-col>
        </v-row>
      </section>

      <section class="d-flex">
        <v-col cols="2" class="pa-0">
          <span style="font-size: 16px;">
            サポーター
          </span>
        </v-col>

        <v-row class="mb-2">
          <v-col
            v-for="user in supporters"
            :key="user.id"
            sm="4"
            md="3"
            lg="2"
            class="py-1 d-flex align-center"
          >
            <v-checkbox
              class="mt-0"
              :input-value="includeCompletionMark(user)"
              :disabled="disabled"
              hide-details
              @change="completionMarking(user)"
            >
              <template v-slot:label>
                <span>
                  {{ user.name }}
                </span>
              </template>
            </v-checkbox>
          </v-col>
        </v-row>
      </section>

      <section class="d-flex">
        <v-col cols="2" class="pa-0 mt-1">
          <span class="font-weight-bold" style="font-size: 16px;">
            メンバー
          </span>
        </v-col>

        <v-row>
          <v-col
            v-for="user in admins"
            :key="user.id"
            sm="4"
            md="3"
            lg="2"
            class="py-1 d-flex align-center"
          >
            <v-checkbox
              class="mt-0"
              :input-value="includeCompletionMark(user)"
              :disabled="disabled"
              hide-details
              @change="completionMarking(user)"
            >
              <template v-slot:label>
                <span class="font-weight-bold">
                  {{ user.name }}
                </span>
              </template>
            </v-checkbox>
          </v-col>
        </v-row>
      </section>
    </section>
  </section>
</template>

<script>
import { chain } from 'lodash';

const SPECIAL_USER_MAIL_LIST = ['<EMAIL>'];

export default {
  props: {
    currentCompletionMarks: {
      type: Array,
      default: () => [],
    },
    proof: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    specialSupporters() {
      const specialSupporterIds = this.proof.members.map(user => user.id);
      const isSpecialSupporter = user => specialSupporterIds.includes(user.id);

      return this.proof.capable_completion_mark_users
        .filter(isSpecialSupporter)
        .toSorted(this.userOrderDesc('special_supporter'));
    },
    supporters() {
      const hasAssignmentedMember = user =>
        this.specialSupporters.some(m => m.id === user.id);

      const hasAdmin = user => this.admins.some(a => a.id === user.id);

      const isNotAdminAndAssignmentedMember = user =>
        !hasAssignmentedMember(user) && !hasAdmin(user);

      return this.proof.capable_completion_mark_users
        .filter(isNotAdminAndAssignmentedMember)
        .toSorted(this.userOrderDesc('supporter'));
    },
    admins() {
      const isAdminOrSpecialUser = user =>
        this.isAdmin(user) || this.isSpecialUser(user);

      return this.proof.capable_completion_mark_users
        .filter(isAdminOrSpecialUser)
        .toSorted(this.userOrderDesc('admin'));
    },
  },
  data() {
    return {};
  },
  methods: {
    isAdmin(user) {
      return user.type.value == 'admin';
    },
    isSpecialUser(user) {
      return SPECIAL_USER_MAIL_LIST.includes(user.email);
    },
    isNotSpecialUser(user) {
      return !this.isSpecialUser(user);
    },
    includeCompletionMark(user) {
      return this.currentCompletionMarks.some(
        completion_mark => completion_mark.user.id == user.id,
      );
    },
    userOrderDesc(sortType) {
      const getPriority = user => {
        const DEFAULT_PRIORITY = 100000000;
        const equalSortType = sort => sort.sort_type === sortType;

        return chain(user.completion_mark_sorts)
          .find(equalSortType)
          .get('priority', DEFAULT_PRIORITY)
          .value();
      };

      return function(x, y) {
        return getPriority(x) - getPriority(y);
      };
    },
    completionMarking(user) {
      const path = this.route(
        `${this.$page.props.user.type.value}.proofs.completion-mark`,
        {
          proof: this.proof.id,
          user: user.id,
        },
      );

      this.$axios.post(path).then(({ data }) => {
        this.$page.props.snackbar = data.snackbar;
        this.completionMarks = data.completion_marks;
      });
    },
  },
};
</script>

<style></style>
