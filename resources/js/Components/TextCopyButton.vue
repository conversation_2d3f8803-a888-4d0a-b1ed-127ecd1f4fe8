<template>
  <v-tooltip left :color="status.color">
    <template v-slot:activator="{ on, attrs }">
      <v-icon :class="btnClass" v-bind="attrs" v-on="on" @click="copy">
        mdi-content-copy
      </v-icon>
    </template>
    <span>{{ status.message }}</span>
  </v-tooltip>
</template>

<script>
const Status = {
  init: {
    message: 'コピー',
    color: 'gray',
  },
  success: {
    message: 'コピーしました',
    color: 'success',
  },
  error: {
    message: 'コピーに失敗しました',
    color: 'red',
  },
}

export default {
  name: 'ProofCountCalendarEventDetail',
  props: {
    text: {
      type: String,
      required: true,
      default: ''
    },
    top: {
      type: Boolean,
      required: false,
      default: false,
    },
    right: {
      type: Boolean,
      required: false,
      default: false,
    },
    bottom: {
      type: Boolean,
      required: false,
      default: false,
    },
    left: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      status: Status.init,
      btnClass: {
        'copy-btn': true,
        top: this.top,
        right: this.right,
        bottom: this.bottom,
        left: this.left,
      }
    }
  },
  methods: {
    changeToSuccess() {
      this.status = Status.success;
    },
    changeToError() {
      this.status = Status.error;
    },
    changeToInit() {
      this.status = Status.init;
    },
    copy() {
      const { clipboard } = navigator;
      const initAfterAt = 2000; //  ms

      clipboard.writeText(this.text)
        .then(this.changeToSuccess)
        .catch(this.changeToError)
        .finally(() => setTimeout(this.changeToInit, initAfterAt))
    },
  }
}
</script>

<style lang="scss" scoped>
.bottom {
  position: absolute;
  bottom: 0;
  padding-bottom: 8px;
}

.left {
    position: absolute;
    left: 0;
    padding-left: 8px;
}

.top {
  position: absolute;
  top: 0;
  padding-top: 8px;

}
.right {
  position: absolute;
  right: 0;
  padding-right: 8px;
}

.copy-btn {
  cursor: pointer;
  color: #757575;
}
</style>
