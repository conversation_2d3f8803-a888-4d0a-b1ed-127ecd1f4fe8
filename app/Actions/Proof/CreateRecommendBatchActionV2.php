<?php

namespace App\Actions\Proof;

use App\Data\RequestFormFilesData;
use App\Data\RequestFormPublishDateData;
use App\Jobs\JobCachePredictGeneralAndLegalPRM;
use App\Jobs\JobRequestImageAlert;
use App\Jobs\JobRequestPlainFileCheck;
use App\Jobs\JobRequestUnnaturalTextCheck;
use App\Mail\ProofFailedMail;
use App\Models\JobBatch;
use App\Models\Proof;
use App\Models\User;
use Illuminate\Support\Facades\Bus;
use Illuminate\Bus\Batch;
use Illuminate\Bus\PendingBatch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Throwable;

class CreateRecommendBatchActionV2
{
    public function execute(
        Proof $proof,
        RequestFormFilesData $files,
        RequestFormPublishDateData $publishDate,
    ): PendingBatch {
        $jobs = $this->getJobs($proof, $files, $publishDate);

        return Bus::batch($jobs)
            ->then(function (Batch $batch) use ($proof) {
                JobBatch::whereId($batch->id)->update([
                    'status' => JobBatch::STATUS_SUCCEED
                ]);
            })
            ->catch(function (Batch $batch, \Throwable $error) use ($proof) {
                // $this->failProcess($proof, $batch, $error);
            })
            ->name("{$proof->title} ({$proof->id}) - リコメンド")
            ->onQueue('recommend');
    }

    /**
     * PDFがある場合は、旧申請フォームの処理を行う
     * テキストまたは画像がある場合は、新申請フォームの処理を行う
     */
    private function getJobs(
        Proof $proof,
        RequestFormFilesData $files,
        RequestFormPublishDateData $publishDate,
    ): array {
        return collect([])
            ->when($files->hasPdfs(), function (Collection $jobs) use ($proof, $publishDate) {
                return $jobs->merge([
                    new JobCachePredictGeneralAndLegalPRM($proof),
                    new JobRequestUnnaturalTextCheck($proof, $publishDate),
                    new JobRequestImageAlert($proof),
                ]);
            })
            ->when($files->hasGendocable(), function (Collection $jobs) use ($proof) {
                return $jobs->merge([
                    new JobRequestPlainFileCheck($proof),
                ]);
            })
            ->toArray();
    }

    public function failProcess(Proof $proof, Batch $batch, Throwable $error)
    {
        $admins = User::admin()->get();

        Mail::to($admins)->send(new ProofFailedMail(
            $proof,
            $error->getMessage()
        ));
    }
}
