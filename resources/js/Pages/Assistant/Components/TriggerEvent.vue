<template>
  <div class="trigger-event-system">
    <!-- This component handles trigger events for the Assistant character -->
  </div>
</template>

<script>
export default {
  name: 'TriggerEvent',

  props: {
    // No props needed - communication through events
  },

  data() {
    return {
      // Trigger event definitions based on the specification images
      triggerEvents: {
        // First View - When character appears
        firstView: {
          animation: 'greeting',
          displayText: 'Hello! May I help you with something?',
          displayTextJa: 'こんにちは！何かお手伝いできることはありますか？',
          remarks: 'When a character appears',
          autoTrigger: true,
          triggerCondition: 'character_first_appearance'
        },

        // User Away - Auto-play with timer
        userAway: {
          animation: 'idle',
          displayText: '...',
          displayTextJa: '...',
          remarks: 'Auto-play with timer',
          autoTrigger: true,
          triggerCondition: 'user_inactive',
          idleVariations: ['idle1', 'idle2', 'idle3', 'idle4', 'idle5', 'idle6', 'idle7', 'idle8', 'idle9']
        },

        // Help button pressed
        helpButtonPressed: {
          animation: 'getAttention',
          displayText: 'Are you in trouble? May I help you?',
          displayTextJa: 'お困りですか？お手伝いしましょうか？',
          remarks: 'Focus Requirements',
          autoTrigger: false,
          triggerCondition: 'help_button_click'
        },

        // Warning/Abnormality Detection
        warningDetection: {
          animation: 'alert',
          displayText: 'Caution required!',
          displayTextJa: '注意が必要です！',
          remarks: 'Must be called explicitly',
          autoTrigger: false,
          triggerCondition: 'warning_detected'
        },

        // Close button
        closeButton: {
          animation: 'goodbye',
          displayText: 'See you again soon!',
          displayTextJa: 'また今度お会いしましょう！',
          remarks: 'Play before character is hidden',
          autoTrigger: false,
          triggerCondition: 'close_button_click'
        },

        // Email sent
        emailSent: {
          animation: 'sendMail',
          displayText: 'Email sent.',
          displayTextJa: 'メールを送信しました。',
          remarks: 'After sending is complete',
          autoTrigger: true,
          triggerCondition: 'email_sent_complete'
        },

        // Print execution
        printExecution: {
          animation: 'print',
          displayText: 'Printing...',
          displayTextJa: '印刷中...',
          remarks: 'Print command link',
          autoTrigger: true,
          triggerCondition: 'print_command_executed'
        },

        // Save Operation
        saveOperation: {
          animation: 'save',
          displayText: 'Saved.',
          displayTextJa: '保存完了時',
          remarks: '保存完了時',
          autoTrigger: true,
          triggerCondition: 'save_complete'
        },

        // Delete Process
        deleteProcess: {
          animation: 'emptyTrash',
          displayText: 'Emptying trash...',
          displayTextJa: 'ゴミ箱を空にしています...',
          remarks: 'Optional performance, when deleting UI',
          autoTrigger: true,
          triggerCondition: 'delete_operation'
        },

        // Welcome time
        welcomeTime: {
          animation: 'wave',
          displayText: 'Hi!',
          displayTextJa: 'こんにちは！',
          remarks: 'Simple call',
          autoTrigger: false,
          triggerCondition: 'welcome_greeting'
        },

        // Thinking/processing
        thinkingProcessing: {
          animation: 'thinking',
          displayText: "I'm thinking about it...",
          displayTextJa: 'ちょっと考えています...',
          remarks: 'When delayed, out of sync',
          autoTrigger: true,
          triggerCondition: 'processing_delay'
        },

        // Processing in progress
        processingInProgress: {
          animation: 'processing',
          displayText: 'Processing...',
          displayTextJa: '処理中...',
          remarks: 'During server communication, etc.',
          autoTrigger: true,
          triggerCondition: 'server_processing'
        },

        // Input reception
        inputReception: {
          animation: 'hearing_1',
          displayText: "I'm listening.",
          displayTextJa: '聞いています。',
          remarks: 'Waiting for voice or input',
          autoTrigger: true,
          triggerCondition: 'waiting_for_input'
        },

        // Explanation Run
        explanationRun: {
          animation: 'explain',
          displayText: "I'll explain.",
          displayTextJa: '説明します。',
          remarks: 'When playing tutorials, etc.',
          autoTrigger: false,
          triggerCondition: 'tutorial_mode'
        },

        // Write operations
        writeOperations: {
          animation: 'writing',
          displayText: 'Entering...',
          displayTextJa: '入力中...',
          remarks: 'Input processing animation',
          autoTrigger: true,
          triggerCondition: 'text_input'
        },

        // When a form is submitted
        formSubmitted: {
          animation: 'writing',
          displayText: 'Recording your input...',
          displayTextJa: '入力を記録しています...',
          remarks: 'Immediately after sending data',
          autoTrigger: true,
          triggerCondition: 'form_submission'
        },

        // File Verification
        fileVerification: {
          animation: 'checkingSomething',
          displayText: 'Checking...',
          displayTextJa: 'チェック中...',
          remarks: 'When loading or checking status',
          autoTrigger: true,
          triggerCondition: 'file_check'
        },

        // Data Import
        dataImport: {
          animation: 'checkingSomething',
          displayText: 'Loading...',
          displayTextJa: '読み込み中...',
          remarks: 'DB access etc.',
          autoTrigger: true,
          triggerCondition: 'data_loading'
        },

        // Art Mode
        artMode: {
          animation: 'getArtsy',
          displayText: 'Challenging myself with art!',
          displayTextJa: 'アートに挑戦しています！',
          remarks: 'Any performance',
          autoTrigger: false,
          triggerCondition: 'art_mode_activated'
        },

        // Tech Mode
        techMode: {
          animation: 'getTechy',
          displayText: 'Technical mode.',
          displayTextJa: 'テクニカルモード。',
          remarks: 'Any performance',
          autoTrigger: false,
          triggerCondition: 'tech_mode_activated'
        },

        // Magic Mode
        magicMode: {
          animation: 'getWizardy',
          displayText: "I'm looking for a magic solution...",
          displayTextJa: '魔法の解決策を探しています...',
          remarks: 'Any performance',
          autoTrigger: false,
          triggerCondition: 'magic_mode_activated'
        },

        // Click on the notification
        clickNotification: {
          animation: 'gestureUp',
          displayText: 'Here is the notice.',
          displayTextJa: 'こちらが通知です。',
          remarks: '通知領域強調',
          autoTrigger: false,
          triggerCondition: 'notification_clicked'
        },

        // Opened the menu
        openedMenu: {
          animation: 'gestureRight',
          displayText: "Here's the control panel.",
          displayTextJa: 'こちらがコントロールパネルです。',
          remarks: 'UI誘導',
          autoTrigger: false,
          triggerCondition: 'menu_opened'
        },

        // Go to the left menu
        leftMenu: {
          animation: 'gestureLeft',
          displayText: 'Here is the menu.',
          displayTextJa: 'こちらがメニューです。',
          remarks: 'Navigation Guidance',
          autoTrigger: false,
          triggerCondition: 'left_menu_navigation'
        },

        // Show modal
        showModal: {
          animation: 'gestureDown',
          displayText: 'Please check here',
          displayTextJa: 'こちらをご確認ください',
          remarks: 'Highlighting important elements',
          autoTrigger: false,
          triggerCondition: 'modal_display'
        }
      }
    };
  },

  mounted() {
    // Initialize event listeners for automatic triggers
    this.initializeAutoTriggers();
  },

  beforeDestroy() {
    // Clean up event listeners
    this.cleanupAutoTriggers();
  },

  methods: {
    // Initialize automatic trigger event listeners
    initializeAutoTriggers() {
      // Listen for various DOM events that should trigger character behaviors

      // Save operations
      document.addEventListener('save', this.handleSaveOperation);

      // Print operations
      document.addEventListener('beforeprint', this.handlePrintExecution);

      // Form submissions (for email sending)
      document.addEventListener('submit', this.handleFormSubmission);

      // Delete operations
      document.addEventListener('delete', this.handleDeleteOperation);

      // User activity tracking for idle detection
      this.initializeIdleTracking();
    },

    // Clean up event listeners
    cleanupAutoTriggers() {
      document.removeEventListener('save', this.handleSaveOperation);
      document.removeEventListener('beforeprint', this.handlePrintExecution);
      document.removeEventListener('submit', this.handleFormSubmission);
      document.removeEventListener('delete', this.handleDeleteOperation);

      if (this.idleTimer) {
        clearTimeout(this.idleTimer);
      }
      if (this.activityListeners) {
        this.activityListeners.forEach(({ event, handler }) => {
          document.removeEventListener(event, handler);
        });
      }
    },

    // Initialize idle tracking for user away detection
    initializeIdleTracking() {
      let idleTime = 0;
      const idleThreshold = 30000; // 30 seconds

      // Reset idle time on user activity
      const resetIdleTime = () => {
        idleTime = 0;
        if (this.idleTimer) {
          clearTimeout(this.idleTimer);
        }
        this.idleTimer = setTimeout(() => {
          this.triggerEvent('userAway');
        }, idleThreshold);
      };

      // Activity events to track
      const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

      this.activityListeners = activityEvents.map(event => {
        const handler = resetIdleTime;
        document.addEventListener(event, handler, true);
        return { event, handler };
      });

      // Start idle timer
      resetIdleTime();
    },

    // Handle save operations
    handleSaveOperation() {
      this.triggerEvent('saveOperation');
    },

    // Handle print execution
    handlePrintExecution() {
      this.triggerEvent('printExecution');
    },

    // Handle form submissions (potential email sending)
    handleFormSubmission(event) {
      const form = event.target;
      if (!form) return;

      // Check if this is an email form
      if (form.action.includes('mail') || form.action.includes('email') ||
          form.querySelector('input[type="email"]')) {
        // Delay the trigger to allow form processing
        setTimeout(() => {
          this.triggerEvent('emailSent');
        }, 1000);
      }
      // Check if this is a request form
      else if (form.action.includes('request') || form.classList.contains('request-form')) {
        setTimeout(() => {
          this.triggerEvent('formSubmitted');
        }, 500);
      }
      // Check if this is a proof form
      else if (form.action.includes('proof') || form.classList.contains('proof-form')) {
        setTimeout(() => {
          this.triggerEvent('processingInProgress');
        }, 500);
      }
      // Generic form submission
      else {
        setTimeout(() => {
          this.triggerEvent('writeOperations');
        }, 300);
      }
    },

    // Handle delete operations
    handleDeleteOperation() {
      this.triggerEvent('deleteProcess');
    },

    // Main method to trigger events
    triggerEvent(eventName, options = {}) {
      const event = this.triggerEvents[eventName];
      if (!event) {
        console.warn(`Unknown trigger event: ${eventName}`);
        return;
      }

      // Emit event to parent component (AICAssistant)
      this.$emit('trigger-event', {
        eventName,
        animation: event.animation,
        displayText: options.useEnglish ? event.displayText : event.displayTextJa,
        remarks: event.remarks,
        options
      });
    },

    // Public methods for manual triggering
    triggerFirstView() {
      this.triggerEvent('firstView');
    },

    triggerHelpButton() {
      this.triggerEvent('helpButtonPressed');
    },

    triggerWarning() {
      this.triggerEvent('warningDetection');
    },

    triggerClose() {
      this.triggerEvent('closeButton');
    },

    triggerWelcome() {
      this.triggerEvent('welcomeTime');
    },

    triggerThinking() {
      this.triggerEvent('thinkingProcessing');
    },

    // Get event information
    getEventInfo(eventName) {
      return this.triggerEvents[eventName] || null;
    },

    // Get all available events
    getAllEvents() {
      return Object.keys(this.triggerEvents);
    }
  }
};
</script>

<style scoped>
.trigger-event-system {
  /* This component is primarily logic-based and doesn't need visible styling */
  display: none;
}
</style>