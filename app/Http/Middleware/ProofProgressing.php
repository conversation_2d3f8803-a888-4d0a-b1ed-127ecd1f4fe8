<?php

namespace App\Http\Middleware;

use App\Models\ProofStatus;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;

class ProofProgressing
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            if($this->isAdmin() || $request->proof?->batch?->finished()) {
                return $next($request);
            }

            return redirect()->back()->with([
                'snackbar' =>  [
                    'color' => 'primary',
                    'icon' => 'mdi-information',
                    'title' => '情報',
                    'message' => '変換が完了するまで少々お待ちください。'
                ],
            ]);
        } catch (\Throwable $th) {
            return redirect()->back()->with([
                'snackbar' =>  [
                    'color' => 'error',
                    'icon' => 'mdi-alert-circle',
                    'title' => '失敗',
                    'message' => $th->getMessage()
                ],
            ]);
        }
    }

    private function isAdmin(): bool
    {
        return auth()->user()?->user_type_id === User::USER_TYPE_ADMIN;
    }
}
