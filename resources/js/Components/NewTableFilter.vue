<template>
  <v-navigation-drawer
    v-if="_drawer"
    v-model="_drawer"
    width="500px"
    absolute
    right
    hide-overlay
  >
    <v-card height="inherit" elevation="0">
      <v-card-title class="mb-3 pl-2 py-3 pr-3">
        ソート
        <v-spacer> </v-spacer>
        <v-btn icon @click="_drawer = false">
          <v-icon>mdi-window-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <slot />
      </v-card-text>
      <v-card-actions class="pb-4 pr-4">
        <v-row>
          <v-col>
            <v-btn
              color="grey darken-1"
              @click="reset(_filter)"
              block
              class="ml-2"
            >
              <span style="color: white">初期化</span>
            </v-btn>
          </v-col>

          <v-col>
            <v-btn color="success" @click="submit(_filter)" block class="mr-2">
              検索
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-navigation-drawer>
</template>

<script>
import {
  filterDeserialize as deserialize,
  filterSerialize as serialize,
} from '../lib/utils';

export default {
  name: 'NewTableFilter',
  props: ['filter', 'drawer'],
  computed: {
    _filter: {
      get() {
        return this.filter;
      },
      set(value) {
        this.$emit('update:filter', value);
      },
    },
    _drawer: {
      get() {
        return this.drawer;
      },
      set(value) {
        this.$emit('update:drawer', value);
      },
    },
  },

  methods: {
    // フィルター初期化
    initFilter() {
      let filtered = deserialize();
      console.log("initFilter");
      console.log(filtered);
      if( typeof filtered.my_special_sup_only == "string" ) {
        filtered.my_special_sup_only == "true" ? filtered.my_special_sup_only = true : filtered.my_special_sup_only = false;
      }

      if( typeof filtered.checkedOnly == "string" ) {
        filtered.checkedOnly == "true" ? filtered.checkedOnly = true : filtered.checkedOnly = false;
      }

      if( typeof filtered.unCheckedOnly == "string" ) {
        filtered.unCheckedOnly == "true" ? filtered.unCheckedOnly = true : filtered.unCheckedOnly = false;
      }

      if( typeof filtered.deleted_at == "string" ) {
        filtered.deleted_at == "true" ? filtered.deleted_at = true : filtered.deleted_at = false;
      }

      if( typeof filtered.xp_only == "string" ) {
        filtered.xp_only == "true" ? filtered.xp_only = true : filtered.xp_only = false;
      }

      if( typeof filtered.am_only == "string" ) {
        filtered.am_only == "true" ? filtered.am_only = true : filtered.am_only = false;
      }

      if( typeof filtered.al_only == "string" ) {
        filtered.al_only == "true" ? filtered.al_only = true : filtered.al_only = false;
      }

      if( typeof filtered.admin_role == "string" ) {
        filtered.admin_role == "true" ? filtered.admin_role = true : filtered.admin_role = false;
      }

      if (typeof filtered.proof_number == "number") {
        // 数値の場合で5桁未満なら先頭から0を埋める
        filtered.proof_number = String(filtered.proof_number).padStart(5, '0');
      }
      this._filter = Object.assign(this.filter, filtered);
    },

    reset() {
      const path = location.pathname;

      this.$inertia.get(path);
    },

    // フィルター送信
    submit(filter) {
      filter.page = 1;
      console.log("submit");
      console.log(filter);
      console.log(filter.proof_number);

      const path = location.pathname + serialize(filter);

      this.$inertia.get(path);
    },
  },
  created() {
    this.initFilter();
  },
};
</script>
