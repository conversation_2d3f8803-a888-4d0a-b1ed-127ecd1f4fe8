<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Proof\GetProofReservationWithInPeriodAction;
use App\Actions\Proof\UpsertProofReservationAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProofReservationController\UpsertRequest;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ProofReservationController extends Controller
{
    public function index(Request $request, GetProofReservationWithInPeriodAction $action)
    {
        $start = CarbonImmutable::parse($request->input('start'))->subMonth()->startOfMonth();
        $end = CarbonImmutable::parse($request->input('end'))->addMonth()->endOfMonth();

        $reservations = $action->execute($start, $end);

        return Inertia::render("Admin/Util/Reservation/Index", [
            'reservations' => fn () => $reservations,
        ]);
    }

    public function upsert(UpsertRequest $request, UpsertProofReservationAction $action)
    {
        try {
            DB::transaction(
                fn () =>
                $action->execute($request->data())
            );

            return redirect()
                    ->route('admin.utils.reservations.index', [
                        'start' => $request->data()->date->startOfMonth()->format('Y-m-d'),
                        'end' => $request->data()->date->endOfMonth()->format('Y-m-d'),
                    ], self::HTTP_STATUS_SEE_OTHER)
                    ->with('snackbar', [
                        'color' => 'primary',
                        'icon' => 'mdi-check-circle',
                        'title' => '成功',
                        'message' => 'キャパシティー数を保存しました。'
                    ]);
        } catch (\Throwable $th) {
            return redirect()
                    ->back()
                    ->with('snackbar', [
                        'color' => 'error',
                        'icon' => 'mdi-alert',
                        'title' => '失敗',
                        'message' => $th->getMessage()
                    ]);
        }
    }
}
