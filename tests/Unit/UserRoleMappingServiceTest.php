<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\UserType;
use App\Services\UserRoleMappingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserRoleMappingServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create user types
        UserType::create(['value' => 'requester', 'text' => '依頼者']);
        UserType::create(['value' => 'member', 'text' => '事務局メンバー']);
        UserType::create(['value' => 'admin', 'text' => '管理者']);
    }

    /** @test */
    public function it_maps_requester_to_admin_manual()
    {
        $result = UserRoleMappingService::mapUserTypeToManualRole('requester');
        $this->assertEquals('admin', $result);
    }

    /** @test */
    public function it_denies_access_to_member_users()
    {
        $result = UserRoleMappingService::mapUserTypeToManualRole('member');
        $this->assertNull($result);
    }

    /** @test */
    public function it_denies_access_to_admin_users()
    {
        $result = UserRoleMappingService::mapUserTypeToManualRole('admin');
        $this->assertNull($result);
    }

    /** @test */
    public function it_denies_access_for_unknown_types()
    {
        $result = UserRoleMappingService::mapUserTypeToManualRole('unknown');
        $this->assertNull($result);
    }

    /** @test */
    public function it_returns_all_mappings()
    {
        $mappings = UserRoleMappingService::getAllMappings();

        $expected = [
            'requester' => 'admin',
            'member' => null,
            'admin' => null
        ];

        $this->assertEquals($expected, $mappings);
    }

    /** @test */
    public function it_gets_manual_role_for_authenticated_requester_user()
    {
        $userType = UserType::where('value', 'requester')->first();
        $user = User::factory()->create(['user_type_id' => $userType->id]);

        $this->actingAs($user);

        $result = UserRoleMappingService::getManualRoleForAuthenticatedUser();
        $this->assertEquals('member', $result);
    }

    /** @test */
    public function it_denies_access_for_authenticated_member_user()
    {
        $userType = UserType::where('value', 'member')->first();
        $user = User::factory()->create(['user_type_id' => $userType->id]);

        $this->actingAs($user);

        $result = UserRoleMappingService::getManualRoleForAuthenticatedUser();
        $this->assertNull($result);
    }

    /** @test */
    public function it_returns_null_for_unauthenticated_user()
    {
        $result = UserRoleMappingService::getManualRoleForAuthenticatedUser();
        $this->assertNull($result);
    }

    /** @test */
    public function it_gets_user_context_for_authenticated_user()
    {
        $userType = UserType::where('value', 'admin')->first();
        $user = User::factory()->create([
            'user_type_id' => $userType->id,
            'email' => '<EMAIL>',
            'first_name' => 'Test',
            'last_name' => 'User'
        ]);

        $this->actingAs($user);

        $context = UserRoleMappingService::getUserContextForAssistant();

        $this->assertEquals($user->id, $context['user_id']);
        $this->assertEquals('admin', $context['user_type']);
        $this->assertNull($context['manual_role']);
        $this->assertFalse($context['has_manual_access']);
        $this->assertEquals('<EMAIL>', $context['user_email']);
        $this->assertEquals('User Test', $context['user_name']);
    }

    /** @test */
    public function it_gets_guest_context_for_unauthenticated_user()
    {
        $context = UserRoleMappingService::getUserContextForAssistant();

        $this->assertEquals('guest', $context['user_type']);
        $this->assertNull($context['manual_role']);
        $this->assertFalse($context['has_manual_access']);
    }

    /** @test */
    public function it_validates_manual_roles()
    {
        $this->assertTrue(UserRoleMappingService::isValidManualRole('admin'));
        $this->assertFalse(UserRoleMappingService::isValidManualRole('member'));
        $this->assertFalse(UserRoleMappingService::isValidManualRole('supporter'));
        $this->assertFalse(UserRoleMappingService::isValidManualRole('invalid'));
        $this->assertFalse(UserRoleMappingService::isValidManualRole(null));
    }

    /** @test */
    public function it_checks_manual_access_correctly()
    {
        $this->assertTrue(UserRoleMappingService::hasManualAccess('requester'));
        $this->assertFalse(UserRoleMappingService::hasManualAccess('member'));
        $this->assertFalse(UserRoleMappingService::hasManualAccess('admin'));
        $this->assertFalse(UserRoleMappingService::hasManualAccess('invalid'));
    }

    /** @test */
    public function it_gets_manual_file_names()
    {
        $memberFile = UserRoleMappingService::getManualFileNameForRole('member');
        $supporterFile = UserRoleMappingService::getManualFileNameForRole('supporter');
        $adminFile = UserRoleMappingService::getManualFileNameForRole('admin');

        // Only admin role returns the admin manual file, others return null
        $this->assertNull($memberFile);
        $this->assertNull($supporterFile);
        $this->assertEquals('AIC_倫理確認システム_マニュアル（管理者）_20250602.md', $adminFile);
    }

    /** @test */
    public function it_gets_display_names()
    {
        $userTypeDisplay = UserRoleMappingService::getUserTypeDisplayName('member');
        $manualRoleDisplay = UserRoleMappingService::getManualRoleDisplayName('supporter');

        $this->assertEquals('事務局メンバー', $userTypeDisplay);
        $this->assertEquals('サポーター向け', $manualRoleDisplay);
    }
}