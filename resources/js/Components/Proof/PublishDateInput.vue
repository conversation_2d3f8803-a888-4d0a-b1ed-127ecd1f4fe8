<template>
  <v-menu
    ref="menu"
    v-model="menu"
    :close-on-content-click="false"
    :return-value.sync="_date"
    transition="scale-transition"
    offset-y
    min-width="auto"
    :disabled="disabled"
  >
    <template v-slot:activator="{ on, attrs }">
      <v-text-field
        v-model="_displayDate"
        :label="label"
        :prepend-inner-icon="icon"
        readonly
        v-bind="attrs"
        v-on="on"
        outlined
        :error-messages="errorMessages"
        :disabled="disabled"
      />
    </template>

    <v-date-picker
      v-model="_date"
      no-title
      scrollable
      locale="ja-jp"
      :dayFormat="date => new Date(date).getDate()"
      :min="min"
      :type="type"
    >
      <v-spacer></v-spacer>
      <v-btn color="grey lighten-1" text @click="handleClose">
        キャンセル
      </v-btn>
      <v-btn color="green darken-1" text @click="handleSave">
        確認
      </v-btn>
    </v-date-picker>
  </v-menu>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'PublishDateInput',
  props: {
    value: {
      type: String | null,
      required: true,
    },
    label: {
      type: String,
      required: false,
    },
    errorMessages: {
      type: Array,
      required: false,
    },
    min: {
      type: String,
      required: false,
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    type: {
      type: String,
      required: false,
      default: 'date',
    },
  },
  computed: {
    _date: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);
      },
    },
    icon() {
      return this.type === 'date' ? 'mdi-calendar' : 'mdi-calendar-month';
    },
    // 表示用の日付。内部の日付を「YYYY年M月」形式に変換
    _displayDate: {
      get() {
        return this._date ? dayjs(this._date).format('YYYY年 M月') : '';
      },
      // ユーザーからの入力を直接受け付けないのでsetは必須ではない
      set(val) {
        // 必要に応じて逆変換処理を追加できます
      }
    }
  },
  data() {
    return {
      menu: false,
    };
  },
  methods: {
    handleClose() {
      this.menu = false;
      this.$refs.menu.save('');
    },
    handleSave() {
      this.menu = false;
      this.$refs.menu.save(this._date);
    },
  },
};
</script>

<style></style>
