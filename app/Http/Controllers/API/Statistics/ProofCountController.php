<?php

namespace App\Http\Controllers\API\Statistics;

use App\Actions\Statistics\GetCountGroupByDateAction;
use App\Data\StatisticRequestData;
use App\Http\Controllers\Controller;
use App\Models\Proof;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;

class ProofCountController extends Controller
{
    private $column = 'created_at';

    public function __invoke(
        StatisticRequestData $data,
        GetCountGroupByDateAction $action
    ) {
        $dateformat = $data->unit->dateformat();

        $range = CarbonPeriod::create(
            $data->range->from ?? Proof::min($this->column),
            $data->range->to ?? now()->endOfDay()
        );

        return collect($range)->pipeThrough([
            // Initialize Return Data
            fn (Collection $range) => $range->mapWithKeys(fn (Carbon $date) => [
                $date->format($dateformat) => 0
            ]),
            // Replace with the data retrieved from DB
            fn (Collection $initData) => $initData->replace(
                $action->execute(
                    model: Proof::class,
                    column: $this->column,
                    dateformat: $dateformat,
                    callback: fn (Builder $builder) =>
                        $builder->whereBetween($this->column, [
                            $range->start,
                            $range->end
                        ])
                )
            )
        ]);
    }
}
