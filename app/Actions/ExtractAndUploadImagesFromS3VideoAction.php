<?php

namespace App\Actions;

use App\Data\ExtractAndUploadImagesFromS3VideoData;
use App\Events\VideoToImageProgressEvent;
use Illuminate\Contracts\Filesystem\FileExistsException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\LaravelFFMpeg\Exporters\EncodingException;
use ProtoneMedia\LaravelFFMpeg\FFMpeg\ImageFormat;
use ProtoneMedia\LaravelFFMpeg\FFMpeg\ProgressListenerDecorator;
use ProtoneMedia\LaravelFFMpeg\Support\FFMpeg;

class ExtractAndUploadImagesFromS3VideoAction
{
    const ProgressMessage = 'イメージ抽出中...';

    //  The range for JPEG is 2-31, with 2 being the best quality and 31 being the worst.
    // https://github.com/protonemedia/laravel-ffmpeg#export-multiple-frames-at-once
    const Quality = 23;

    // xxxxxxxx.png
    private string $fileName = '%08d.jpg';

    public function execute(ExtractAndUploadImagesFromS3VideoData $data): array
    {
        Log::info('Extract And Upload Images From S3 Video', $data->toArray());

        // 既にディレクトリーにファイルがある場合はそのまま返す
        if($files = $this->getFiles($data->toDir, $data->disk)) {
            return $files;
        }

        try {
            $this->extractAndUploadFromS3(
                $data->video,
                $data->toDir,
                $data->interval_type->value,
                $data->disk,
                $data->initProgress
            );
        } catch(FileExistsException $th) {
        } catch (EncodingException $th) {
            $command = $th->getCommand();
            $errorLog = $th->getErrorOutput();

            Log::error($errorLog);
            Log::info($command);

            throw $th;
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            throw $th;
        }

        return $this->getFiles($data->toDir, $data->disk);
    }

    private function extractAndUploadFromS3(
        string $video,
        string $toDir,
        float $interval,
        string $disk = 's3',
        int $initProgress = 40
    ): void {
        FFMpeg::fromDisk($disk)
            ->open($video)
            ->export()
            ->addFilter(['-vf', "fps=1/{$interval}"])
            ->addFilter(['-qscale:v', self::Quality])
            ->inFormat(ProgressListenerDecorator::decorate(new ImageFormat))
            ->toDisk($disk)
            ->save("{$toDir}/{$this->fileName}");
    }

    // private function fromUploadedFile(
    //     UploadedFile $video,
    //     string $disk,
    //     float $interval,
    //     string $output
    // ): void {
    //     FFMpeg::open($video)
    //         ->exportFramesByInterval($interval)
    //         ->toDisk($disk)
    //         ->save($output);
    // }

    private function getFiles(string $directory, string $disk): array
    {
        return Storage::disk($disk)->files($directory);
    }

    private function calculateProgress(int $initProgress, int $percentage): int
    {
        $extractProgress = $percentage * (100 - $initProgress) / 100;

        return intval($initProgress + $extractProgress);
    }
}
