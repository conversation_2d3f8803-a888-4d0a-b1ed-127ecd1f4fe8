<?php

namespace App\Http\API;

use App\Helpers\ProofFileManager;
use App\Http\API\AdobeAccessTokenProvider as JWTProvider;
use App\Models\ProofFile;
use App\Models\AdobeApiLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Collection;
use GuzzleHttp\Middleware;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Karriere\PdfMerge\PdfMerge;

/**
 * Class AdobePDFTools
 * Adobe PDF Tools API
 */
class AdobePDFTools
{
    // Http Response Status
    const HTTP_STATUS_IN_PROGRESSING = 202;

    // End Point(URL)
    const CREATE_PDF_ENDPOINT = 'https://cpf-ue1.adobe.io/ops/:create?respondWith=%7B%22reltype%22%3A%20%22http%3A%2F%2Fns.adobe.com%2Frel%2Fprimary%22%7D';
    const COMBINE_PDF_ENDPOINT = 'https://cpf-ue1.adobe.io/ops/:create?respondWith=%7B%22reltype%22%3A%20%22http%3A%2F%2Fns.adobe.com%2Frel%2Fprimary%22%7D';
    const POLL_ENDPOINT = 'https://cpf-ue1.adobe.io/ops/id/';

    // Time to wait for a task progressing
    const REQUEST_ASYNC_WAIT_SECONDS = 60;

    // Retry Request Count
    const REQUEST_RETRIES_COUNT = 3;

    const PDF_MAGIC_NUMBER = "%PDF-";
    /**
     * PDF Tools API Client ID (Adobe IO Console)
     */
    private $clientId;

    /**
     * PDF Tools API CLIENT SECRET (Adobe IO Console)
     */
    private $secretId;

    /**
     * PDF Tools API TECHNICAL ACCOUNT ID (Adobe IO Console)
     */
    private $technicalAccountId;

    /**
     * PDF Tools API TECHNICAL ACCOUNT EMAIL (Adobe IO Console)
     */
    private $technicalAccountEmail;

    /**
     * PDF Tools API ORGANIZATION ID (Adobe IO Console)
     */
    private $organizationId;

    /**
     * Adobe Auth JWT Metascopes (JWT Metascopes)
     * https://www.adobe.io/authentication/auth-methods.html#!AdobeDocs/adobeio-auth/master/JWT/Scopes.md
     */
    private $metaScopes;

    /**
     * Adobe Auth Token (Access Token)
     * https://www.adobe.io/authentication/auth-methods.html#!AdobeDocs/adobeio-auth/master/JWT/JWT.md
     */
    private $accessToken;

    /**
     * Guzzle history Middleware Data
     */
    private $histories = [];

    public function __construct(
        $clientId,
        $secretId,
        $technicalAccountId,
        $technicalAccountEmail,
        $organizationId,
        $metaScopes
    ) {
        $this->clientId = $clientId;
        $this->secretId = $secretId;
        $this->technicalAccountId = $technicalAccountId;
        $this->technicalAccountEmail = $technicalAccountEmail;
        $this->organizationId = $organizationId;
        $this->metaScopes = $metaScopes;
        $this->accessToken = $this->createAccessToken();
    }

    public function createAccessToken()
    {
        $config = [
                'clientId' => $this->clientId,
                'secretId' => $this->secretId,
                'technicalAccountId' => $this->technicalAccountId,
                'technicalAccountEmail' => $this->technicalAccountEmail,
                'organizationId' => $this->organizationId,
                'metaScopes' => $this->metaScopes
            ];

        return JWTProvider::auth($config);
    }

    public function createPDF(ProofFile $proofFile)
    {
        $content = ProofFileManager::getContentFromS3($proofFile->path);

        $client = Http::withToken($this->accessToken)
                        ->withHeaders([
                            'Accept' => 'application/json, text/plain, */*',
                            'x-api-key' => $this->clientId,
                            'Prefer' => 'respond-async,wait=' . self::REQUEST_ASYNC_WAIT_SECONDS
                        ])
                        ->withMiddleware(Middleware::history($this->histories))
                        ->attach('InputFile0', $content);
        $body = [
                'cpf:inputs' => [
                    'documentIn' => [
                        "cpf:location" => "InputFile0",
                        "dc:format" => $proofFile->mime
                    ],
                    'params' => [
                        'cpf:inline' => [
                            'pptFormatOptions' => [
                                'documentLanguage' => $proofFile->language->value
                            ]
                        ]
                    ]
                ],
                "cpf:engine" => [
                    "repo:assetId" => "urn:aaid:cpf:Service-1538ece812254acaac2a07799503a430"
                ],
                "cpf:outputs" => [
                    "documentOut" => [
                        "cpf:location" => "multipartLabelOut",
                        "dc:format" => "application/pdf"
                    ]
                ]
            ];

        $requestBody = [
                'contentAnalyzerRequests' => json_encode($body, JSON_INVALID_UTF8_IGNORE),
            ];

        $response = $client->retry(self::REQUEST_RETRIES_COUNT)
                           ->post(self::CREATE_PDF_ENDPOINT, $requestBody);


        // コンバート処理が進行中の場合
        if ($response->status() == self::HTTP_STATUS_IN_PROGRESSING) {
            $content = $this->poll($response->header('x-request-id'));
        }

        $content = $response->body();

        // Log
        // $adobeApiLog = AdobeApiLog::success(
        //     $this->histories,
        //     $proofFile->name . 'ファイルを変換しました。'
        // );

        // Log Foreign
        // $proofFile->adobe_api_log_id = $adobeApiLog->id;
        // $proofFile->save();

        if ($index = strpos($content, self::PDF_MAGIC_NUMBER)) {
            $content = substr($content, $index);
        }

        return $content;
    }

    // public function combinePDF(Collection $proofFiles)
    // {
    //     $client = Http::withToken($this->accessToken)
    //                     ->withHeaders([
    //                         'Accept' => 'application/json, text/plain, */*',
    //                         'x-api-key' => $this->clientId,
    //                         'Prefer' => 'respond-async,wait=' . self::REQUEST_ASYNC_WAIT_SECONDS
    //                     ])
    //                     ->withMiddleware(Middleware::history($this->histories));
    //     $body = [
    //             'cpf:inputs' => [
    //                 'documentsIn' => []
    //             ],
    //             "cpf:engine" => [
    //                 "repo:assetId" => "urn:aaid:cpf:Service-916ee91c156b42349a7847a7d564fb13"
    //             ],
    //             "cpf:outputs" => [
    //                 "documentOut" => [
    //                     "cpf:location" => "multipartLabelOut",
    //                     "dc:format" => "application/pdf"
    //                 ]
    //             ]
    //         ];

    //     foreach ($proofFiles as $index => $proofFile) {
    //         $documentIn =  [
    //                 'documentIn' => [
    //                     "cpf:location" => "InputFile" . $index,
    //                     "dc:format" => "application/pdf"
    //                 ]
    //             ];

    //         $content = ProofFileManager::getContentFromS3($proofFile->path);

    //         $client->attach('InputFile' . $index, $content);

    //         $body['cpf:inputs']['documentsIn'][] = $documentIn;
    //     }

    //     $requestBody = [
    //             'contentAnalyzerRequests' => json_encode($body, JSON_INVALID_UTF8_IGNORE)
    //         ];

    //     $response = $client->retry(self::REQUEST_RETRIES_COUNT)
    //                             ->post(self::COMBINE_PDF_ENDPOINT, $requestBody);

    //     // コンバート処理が進行中の場合
    //     if ($response->status() == self::HTTP_STATUS_IN_PROGRESSING) {
    //         $content = $this->poll($response->header('x-request-id'));
    //     }

    //     $content = $response->body();

    //     // Log
    //     // $adobeApiLog = AdobeApiLog::success(
    //     //     $this->histories,
    //     //     $proofFile->proof->title . 'を結合しました。'
    //     // );

    //     // Log Foreign
    //     // foreach ($proofFiles as $proofFile) {
    //     //     $proofFile->adobe_api_log_id = $adobeApiLog->id;
    //     //     $proofFile->save();
    //     // }

    //     if ($index = strpos($content, self::PDF_MAGIC_NUMBER)) {
    //         $content = substr($content, $index);
    //     }

    //     return $content;
    // }

    public static function combinePDF(Collection $proofFiles)
    {
        $pdf = new PdfMerge();

        $filesystem = Storage::disk('temp');
        $dir = uniqid();

        try {
            $proofFiles->each(function (ProofFile $proofFile) use ($filesystem, $dir, $pdf) {
                $downloadPath = "{$dir}/{$proofFile->name}";

                // Download from S3 to Local
                $filesystem->put(
                    $downloadPath,
                    ProofFileManager::getContentFromS3($proofFile->path)
                );

                // Add PDF to Merge
                $pdf->add($filesystem->path($downloadPath));
            });

            $mergedOutput = "{$dir}/merged.pdf";
            $pdf->merge($filesystem->path($mergedOutput));

            return $filesystem->get($mergedOutput);
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            throw $th;
        } finally {
            $filesystem->deleteDirectory($dir);
        }
    }

    // polling
    public function poll($xRequestId)
    {
        $client = Http::withToken($this->accessToken)
                        ->withHeaders([
                            'Accept' => 'application/json, text/plain, */*',
                            'x-api-key' => $this->clientId,
                        ])
                        ->withMiddleware(Middleware::history($this->histories));

        $response = $client->get(self::POLL_ENDPOINT . $xRequestId);

        return $response->body();
    }
}
