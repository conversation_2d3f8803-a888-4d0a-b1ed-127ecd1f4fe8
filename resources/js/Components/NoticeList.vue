<template>
  <v-card max-height="400" class="overflow-auto mb-4">
    <v-card-title>
      <v-toolbar flat>
        <v-toolbar-title>
          お知らせ
        </v-toolbar-title>
      </v-toolbar>
    </v-card-title>

    <v-divider></v-divider>

    <v-card-text v-if="isNotEmpty">
      <v-card
        v-for="notice in notices"
        :key="notice.id"
        style="margin-bottom: 1rem;"
      >
        <v-card-title class="d-flex">
          <span class="text-subtitle-1 font-weight-bold">
            {{ notice.title }}
          </span>

          <v-spacer></v-spacer>

          <span class="text-caption">{{ notice.updated_at }}</span>
        </v-card-title>
        <v-divider></v-divider>

        <v-card-text v-html="notice.body" style="white-space: pre-wrap;" />
      </v-card>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'NoticeList',
  props: {
    notices: {
      type: Array,
      required: true,
    },
  },
  computed: {
    isNotEmpty() {
      return this.notices?.length > 0;
    },
  },
};
</script>

<style lang="scss" scoped></style>
