<?php

namespace App\Actions\Proof;

use App\Actions\BinaryToUploadedFileAction;
use App\Actions\OfficeConvertToPdfAction;
use App\Actions\Proof\CreateProofFileAndUploadAction;
use App\Data\EthicsConfirmAssistantMakePdfByPlainFileData;
use App\Data\RequestFormExcelData;
use App\Data\RequestFormFilesData;
use App\Data\RequestFormImageData;
use App\Data\RequestFormPdfData;
use App\Data\RequestFormPowerPointData;
use App\Data\RequestFormPublishDateData;
use App\Data\RequestFormTextData;
use App\Data\RequestFormWordData;
use App\Enums\EthicsConfirmAssistantPlainDataTypeEnum;
use App\Helpers\EthicsConfirmAssistantHelper;
use App\Http\API\EthicsConfirmAssistant;
use App\Models\Cartridge;
use App\Models\Proof;
use App\Models\ProofFile;
use Carbon\CarbonImmutable;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

/**
 * 新申請入力フォームで入力したテキストと画像を使ってPDFを生成する。
 *
 * その後、PDFをS3にアップロードしてからProofFileモデルを生成する。
 */
class GenerateNewVersionOfProofPdfAction
{
    public function __construct(
        private EthicsConfirmAssistant $api,
        private CreateProofFileAndUploadAction $createProofFileAndUploadAction,
        private BinaryToUploadedFileAction $binaryToUploadedFileAction,
        private OfficeConvertToPdfAction $officeConvertToPdfAction,
        private MergePdfAction $mergePdfAction,
    ) {}

    /**
     * @param Proof $proof
     * @param RequestFormPdfData $pdf
     * @param RequestFormFilesData $files
     */
    public function execute(
        Proof $proof,
        RequestFormPublishDateData $publishDate,
        RequestFormFilesData $files
    ): ProofFile {
        $file = $this->getPdfFile($proof, new EthicsConfirmAssistantMakePdfByPlainFileData(
            proofId: $proof->id,
            proofUuid: $proof->uuid,
            date: CarbonImmutable::now(),
            title: $proof->title,
            publishDate: $publishDate,
            texts: $files->texts->map(fn(RequestFormTextData $data) => $this->createTextInputDataForApi($data)),
            images: $files->images->map(fn(RequestFormImageData $data) => $this->createImageInputDataForApi($proof, $data)),
            subConcepts: Cartridge::makeCartridgeElementsByThisEnv(),
        ));

        if ($files->hasOfficeFiles()) {
            $file = $this->mergeOfficeFilesToBase($file, $files->getOfficeFiles());
        }

        return $this->createProofFileAndUploadAction->execute(
            $proof,
            $file,
            config('filesystems.disks.s3.converted_dir'),
            ProofFile::TYPE_CONVERTED
        );
    }

    private function getPdfFile(
        Proof $proof,
        EthicsConfirmAssistantMakePdfByPlainFileData $data,
    ): UploadedFile {
        $response = $this->api->makePdfByPlainFile($data);

        return $this->binaryToUploadedFileAction->execute(
            $this->getFileName($proof),
            $response->body()
        );
    }

    private function createTextInputDataForApi(RequestFormTextData $text)
    {
        $type = EthicsConfirmAssistantPlainDataTypeEnum::Text();

        return $type->convertApiInputData(
            value: $text->value->getContent(),
            description: $text->comment ?? ''
        );
    }

    private function createImageInputDataForApi(Proof $proof, RequestFormImageData $image)
    {
        $type = EthicsConfirmAssistantPlainDataTypeEnum::Image();

        return $type->convertApiInputData(
            value: EthicsConfirmAssistantHelper::getRequestFormFilePath(
                $proof,
                $image->value->getClientOriginalName(),
                '受付'
            ),
            description: $image->comment ?? ''
        );
    }

    /**
     * アップロードファイル名
     * https://tech-flag.atlassian.net/browse/BAPI-265
     */
    private function getFileName(Proof $proof): string
    {
        $fileName = $proof->title;
        // $proof_id = str_pad($proof->id, 5, '0', STR_PAD_LEFT);
        $proof_no = $proof->proof_number;

        return Str::of($fileName)
            ->prepend("{$proof_no}_")
            ->replaceMatches('/\//', '_')
            ->append(ProofFile::PLAIN_FILE_CHECKED_SUFIX)
            ->__toString();
    }

    private function mergeOfficeFilesToBase(UploadedFile $base, array $officeFiles): UploadedFile
    {
        $pdfs = collect($officeFiles)
            ->flatten()
            ->map(
                fn(RequestFormExcelData|RequestFormWordData|RequestFormPowerPointData $data) =>
                $this->officeConvertToPdfAction->execute($data->value)
            )
            ->prepend($base);

        return $this->mergePdfAction->execute(
            fileBaseName: $base->getClientOriginalName(),
            files: $pdfs
        );
    }
}
