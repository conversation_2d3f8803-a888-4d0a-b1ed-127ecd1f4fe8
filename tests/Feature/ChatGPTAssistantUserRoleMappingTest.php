<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserType;
use App\Services\ChatGPTAssistantService;
use App\Services\UserRoleMappingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class ChatGPTAssistantUserRoleMappingTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create user types
        UserType::create(['value' => 'requester', 'text' => '依頼者']);
        UserType::create(['value' => 'member', 'text' => '事務局メンバー']);
        UserType::create(['value' => 'admin', 'text' => '管理者']);
    }

    /** @test */
    public function assistant_api_includes_user_role_mapping_for_requester()
    {
        // Mock ChatGPT API response
        Http::fake([
            '*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'システムの概要について説明します。',
                            'tool_calls' => [
                                [
                                    'id' => 'call_123',
                                    'function' => [
                                        'name' => 'getEthicsManual',
                                        'arguments' => json_encode(['section' => 'overview'])
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ])
        ]);

        $userType = UserType::where('value', 'requester')->first();
        $user = User::factory()->create(['user_type_id' => $userType->id]);

        $response = $this->actingAs($user)
            ->postJson('/api/assistant/query', [
                'query' => 'システムの概要について教えてください',
                'current_page' => 'ホーム'
            ]);

        $response->assertStatus(200);

        // Verify that the request context includes proper role mapping
        Http::assertSent(function ($request) {
            $data = $request->data();
            $messages = $data['messages'] ?? [];

            // Look for context message that includes user role mapping
            foreach ($messages as $message) {
                if ($message['role'] === 'system' && str_contains($message['content'], 'コンテキスト')) {
                    $contextData = json_decode(str_replace('現在のコンテキスト: ', '', $message['content']), true);
                    return isset($contextData['user_type']) && 
                           $contextData['user_type'] === 'requester' &&
                           isset($contextData['manual_role']) && 
                           $contextData['manual_role'] === 'member';
                }
            }
            return false;
        });
    }

    /** @test */
    public function assistant_api_includes_user_role_mapping_for_member()
    {
        Http::fake([
            '*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'サポーター向けの情報を提供します。'
                        ]
                    ]
                ]
            ])
        ]);

        $userType = UserType::where('value', 'member')->first();
        $user = User::factory()->create(['user_type_id' => $userType->id]);

        $response = $this->actingAs($user)
            ->postJson('/api/assistant/query', [
                'query' => 'サポーターの役割について教えてください',
                'current_page' => 'ホーム'
            ]);

        $response->assertStatus(200);

        // Verify that member user type maps to supporter manual role
        Http::assertSent(function ($request) {
            $data = $request->data();
            $messages = $data['messages'] ?? [];

            foreach ($messages as $message) {
                if ($message['role'] === 'system' && str_contains($message['content'], 'コンテキスト')) {
                    $contextData = json_decode(str_replace('現在のコンテキスト: ', '', $message['content']), true);
                    return isset($contextData['user_type']) && 
                           $contextData['user_type'] === 'member' &&
                           isset($contextData['manual_role']) && 
                           $contextData['manual_role'] === 'supporter';
                }
            }
            return false;
        });
    }

    /** @test */
    public function assistant_api_includes_user_role_mapping_for_admin()
    {
        Http::fake([
            '*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => '管理者向けの機能について説明します。'
                        ]
                    ]
                ]
            ])
        ]);

        $userType = UserType::where('value', 'admin')->first();
        $user = User::factory()->create(['user_type_id' => $userType->id]);

        $response = $this->actingAs($user)
            ->postJson('/api/assistant/query', [
                'query' => '管理者機能について教えてください',
                'current_page' => 'ホーム'
            ]);

        $response->assertStatus(200);

        // Verify that admin user type maps to admin manual role
        Http::assertSent(function ($request) {
            $data = $request->data();
            $messages = $data['messages'] ?? [];

            foreach ($messages as $message) {
                if ($message['role'] === 'system' && str_contains($message['content'], 'コンテキスト')) {
                    $contextData = json_decode(str_replace('現在のコンテキスト: ', '', $message['content']), true);
                    return isset($contextData['user_type']) && 
                           $contextData['user_type'] === 'admin' &&
                           isset($contextData['manual_role']) && 
                           $contextData['manual_role'] === 'admin';
                }
            }
            return false;
        });
    }

    /** @test */
    public function user_role_mapping_service_integration_works_for_requester()
    {
        $userType = UserType::where('value', 'requester')->first();
        $user = User::factory()->create(['user_type_id' => $userType->id]);

        $this->actingAs($user);

        $context = UserRoleMappingService::getUserContextForAssistant();

        $this->assertEquals('requester', $context['user_type']);
        $this->assertEquals('admin', $context['manual_role']);
        $this->assertTrue($context['has_manual_access']);
        $this->assertEquals($user->id, $context['user_id']);
    }

    /** @test */
    public function user_role_mapping_service_denies_access_for_member()
    {
        $userType = UserType::where('value', 'member')->first();
        $user = User::factory()->create(['user_type_id' => $userType->id]);

        $this->actingAs($user);

        $context = UserRoleMappingService::getUserContextForAssistant();

        $this->assertEquals('member', $context['user_type']);
        $this->assertNull($context['manual_role']);
        $this->assertFalse($context['has_manual_access']);
        $this->assertEquals($user->id, $context['user_id']);
    }

    /** @test */
    public function manual_file_service_validates_roles()
    {
        $service = new \App\Services\ManualFileService();

        // Test with admin role (should have access)
        $result = $service->loadManualContent('admin');
        $this->assertIsArray($result);

        // Test with invalid role (should be denied access)
        $result = $service->loadManualContent('invalid_role');
        $this->assertIsArray($result);
        $this->assertArrayHasKey('access_denied', $result);

        // Test with null role (should be denied access)
        $result = $service->loadManualContent(null);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('access_denied', $result);
    }
}