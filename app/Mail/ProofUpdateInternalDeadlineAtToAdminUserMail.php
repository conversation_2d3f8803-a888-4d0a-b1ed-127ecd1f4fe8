<?php

namespace App\Mail;

use App\Models\Proof;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProofUpdateInternalDeadlineAtToAdminUserMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    public $subject;
    public $proof;
    public $receiver;
    public $before;
    public $after;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Proof $proof, User $receiver, string $before, string $after)
    {
        // $proof_id = str_pad($proof->id, 5, '0', STR_PAD_LEFT);
        $proof_no = $proof->proof_number;
        $this->subject  = "※重要【倫理確認システム】内部締切日が変更されました（{$proof_no}）";
        $this->proof = $proof;
        $this->receiver = $receiver;
        $this->before = $before;
        $this->after = $after;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        Log::info('ProofUpdateInternalDeadlineAtToAdminUserMail build');
        Log::info("receiver",[$this->receiver]);
        return $this->subject($this->subject)
            ->view('emails.proof_internal_update_deadline_at_admin_users')
            ->with([
                'proof' => $this->proof,
                'receiver' => $this->receiver,
                'before' => $this->before,
                'after' => $this->after,
            ]);
    }
}
