<?php

namespace App\Http\Controllers\Member;

use App\Helpers\ProofCloudSearchManager;
use App\Helpers\ProofFileManager;
use App\Http\Controllers\Controller;
use App\Models\ProofCloudSearch;
use App\Models\ProofFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Inertia\Inertia;

class ProofSearchController extends Controller
{
    protected $perPage;

    public function __construct()
    {
        $this->perPage = config('awscloudsearch.search.options.size');
    }

    /**
     * PDFビュアー
     */
    public function viewer()
    {
        $proofFile = ProofFile::ofCloudSearchId(request()->id)->first();

        return Inertia::render(
            ProofFileManager::ViewerComponent($proofFile),
            [
                'user' => request()->user(),
                'proofFile' => $proofFile,
                'isReadOnly' => true
            ]
        );
    }
}
