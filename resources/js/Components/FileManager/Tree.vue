<template>
  <v-card
    flat
    tile
    min-height="380"
    max-height="500"
    class="d-flex flex-column folders-tree-card overflow-y-auto"
  >
    <div class="grow scroll-x">
      <v-treeview
        :open="open"
        :active="active"
        :items="items"
        @update:active="changeActive"
        item-key="path"
        item-text="name"
        dense
        activatable
        transition
        class="folders-tree"
      >
        <template v-slot:prepend="{ open }">
          <v-icon>
            {{ open ? 'mdi-folder-open-outline' : 'mdi-folder-outline' }}
          </v-icon>
        </template>
      </v-treeview>
    </div>
  </v-card>
</template>

<script>
export default {
  props: {
    handler: Object,
    icons: Object,
    user: Object,
    proof: Object,
    readonly: Boolean,
  },

  computed: {
    items() {
      return this.handler.getTree();
    },
    active() {
      return [this.handler.current.model.path];
    },

    open() {
      const paths = this.handler.current.getPath();
      const opens = paths.map(node => node.model.path);

      return opens;
    },
  },
  methods: {
    changeActive(path) {
      if (path) {
        this.handler.changeActive(path[0]);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.folders-tree-card {
  height: 100%;

  .scroll-x {
    overflow-x: auto;
  }
  ::v-deep .folders-tree {
    min-width: 380px;

    .v-treeview-node {
      cursor: pointer;
      &:hover {
        background-color: rgba(0, 0, 0, 0.02);
      }
    }
  }
}
</style>
