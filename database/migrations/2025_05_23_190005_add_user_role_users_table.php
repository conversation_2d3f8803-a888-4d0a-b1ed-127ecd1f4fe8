<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUserRoleUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        // usersテーブルにuser_roleカラムを追加
        // 2025年5月23日
        Schema::table('users', function (Blueprint $table) {
            $table->string('user_role')->default(null)->nullable()->after('user_type_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
        // usersテーブルのuser_roleカラムを削除
        // 2025年5月23日
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('user_role');
        });
    }
}
