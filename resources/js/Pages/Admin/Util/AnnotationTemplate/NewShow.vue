<template>
  <div>
    <h1 style="color: rgba(64, 64, 64, 1); height: 100px;" class="d-flex mx-7">
      注釈テンプレート編集
    </h1>
    <div class="mx-3 mb-9">
      <BackButton />
    </div>
    <ValidationObserver ref="observer">
      <v-dialog v-model="deleteDialog" persistent max-width="350">
        <v-card>
          <v-card-title class="dialog-headline">
            削除確認画面
          </v-card-title>
          <v-card-text class="dialog-text">
            本当に削除しますか？
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn @click="deleteDialog = false" color="secondary">
              いいえ
            </v-btn>
            <v-btn color="error" @click="deleteSubmit">
              はい
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="loading" persistent width="300">
        <v-card color="primary" dark>
          <v-card-text>
            注釈テンプレート更新中
            <v-progress-linear
              indeterminate
              color="white"
              class="mb-0"
            ></v-progress-linear>
          </v-card-text>
        </v-card>
      </v-dialog>

      <!-- Body -->
      <v-form>
        <!-- 申請名 -->
        <ValidationProvider
          v-slot="{ errors }"
          rules="required"
          name="annotation"
        >
        <v-textarea
          v-model="form.annotation"
          label="注釈"
          name="annotation"
          class="mx-7 annotation-textarea"
          outlined
          auto-grow
          :error-messages="errors"
        />
      </ValidationProvider>
      </v-form>

      <!-- Footer -->
      <div class="pa-3 d-flex flex-wrap justify-end">
        <v-spacer></v-spacer>

        <v-btn class="mt-2 mb-4 mx-4 annotation-button" color="error" @click="deleteDialog = true">
          <v-icon left dark>mdi-delete-alert</v-icon>
          削除
        </v-btn>

        <v-btn
          class="mt-2 mb-4 mx-4 annotation-button"
          color="success"
          @click="updateSubmit"
          :disabled="
            loading || form.annotation === annotationTemplate.annotation
          "
        >
          <v-icon left dark>mdi-comment-check</v-icon>
          更新
        </v-btn>
      </div>
    </ValidationObserver>
  </div>
</template>

<script>
import Layout from '@/Layouts/NewLayout';
import BackButton from '@/Components/AicBackButton';
import { ValidationObserver, ValidationProvider } from 'vee-validate';

export default {
  name: 'AdminUtilsAnnotationTemplateShow',
  layout: Layout,
  props: {
    annotationTemplate: {
      type: Object,
      required: true,
    },
  },
  components: {
    BackButton,
    ValidationObserver,
    ValidationProvider,
  },

  data() {
    return {
      form: {
        id: this.annotationTemplate.id,
        annotation: this.annotationTemplate.annotation,
      },
      deleteDialog: false,
      loading: false,
    };
  },
  methods: {
    deleteSubmit() {
      const path = this.route('admin.utils.annotation-templates.delete', {
        annotationTemplate: this.annotationTemplate.id,
      });

      this.$inertia.delete(path, {
        onError: errors => {
          this.loading = false;
          this.$refs.observer.setErrors(errors['default']);
        },
      });
    },

    async updateSubmit() {
      if (await this.$refs.observer.validate()) {
        this.loading = true;

        const path = this.route('admin.utils.annotation-templates.upsert');

        this.$inertia.post(path, this.form, {
          onError: errors => {
            this.loading = false;
            this.$refs.observer.setErrors(errors['default']);
          },
        });
      }
    },
  },
};
</script>
<style scoped lang="scss">
.annotation-button {
  font-size: 14px;
  font-weight: bold;
  height: 40px !important;
  width: 120px;
  z-index: 1;
}

.annotation-textarea ::v-deep(textarea) {
  font-size: 14px;
  height: 240px !important;
  width: 100%;
  z-index: 1;
}

.dialog-headline{
  background-color: #ff5252;
  color: white;
  font-size: 20px !important;
  font-weight: bold !important;
}

.dialog-text{
  font-size: 14px !important;
  color: rgba(64, 64, 64, 1) !important;
}
</style>