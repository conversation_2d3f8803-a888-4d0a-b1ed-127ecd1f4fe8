<?php

namespace App\Actions;

use App\Data\GetNoticeData;
use App\Models\Notice;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;

class GetNoticeAction
{
    public function execute(GetNoticeData $data): Collection
    {
        return Notice::query()
                ->whereUserTypeId($data->user_type->id)
                ->latestUpdate()
                ->wherePublish(Carbon::now())
                ->get();
    }
}
