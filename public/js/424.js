/*! For license information please see 424.js.LICENSE.txt */
"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[424],{22424:(e,t,n)=>{n.r(t),n.d(t,{default:()=>le});const{entries:o,setPrototypeOf:r,isFrozen:i,getPrototypeOf:a,getOwnPropertyDescriptor:l}=Object;let{freeze:c,seal:s,create:u}=Object,{apply:m,construct:p}="undefined"!=typeof Reflect&&Reflect;c||(c=function(e){return e}),s||(s=function(e){return e}),m||(m=function(e,t,n){return e.apply(t,n)}),p||(p=function(e,t){return new e(...t)});const f=v(Array.prototype.forEach),d=v(Array.prototype.lastIndexOf),h=v(Array.prototype.pop),g=v(Array.prototype.push),T=v(Array.prototype.splice),y=v(String.prototype.toLowerCase),E=v(String.prototype.toString),A=v(String.prototype.match),_=v(String.prototype.replace),S=v(String.prototype.indexOf),N=v(String.prototype.trim),b=v(Object.prototype.hasOwnProperty),w=v(RegExp.prototype.test),R=(O=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return p(O,t)});var O;function v(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return m(e,t,o)}}function C(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:y;r&&r(e,null);let o=t.length;for(;o--;){let r=t[o];if("string"==typeof r){const e=n(r);e!==r&&(i(t)||(t[o]=e),r=e)}e[r]=!0}return e}function D(e){for(let t=0;t<e.length;t++){b(e,t)||(e[t]=null)}return e}function L(e){const t=u(null);for(const[n,r]of o(e)){b(e,n)&&(Array.isArray(r)?t[n]=D(r):r&&"object"==typeof r&&r.constructor===Object?t[n]=L(r):t[n]=r)}return t}function k(e,t){for(;null!==e;){const n=l(e,t);if(n){if(n.get)return v(n.get);if("function"==typeof n.value)return v(n.value)}e=a(e)}return function(){return null}}const x=c(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),I=c(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),M=c(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),U=c(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),z=c(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),P=c(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),H=c(["#text"]),F=c(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),B=c(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),W=c(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),G=c(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Y=s(/\{\{[\w\W]*|[\w\W]*\}\}/gm),j=s(/<%[\w\W]*|[\w\W]*%>/gm),X=s(/\$\{[\w\W]*/gm),q=s(/^data-[\-\w.\u00B7-\uFFFF]+$/),$=s(/^aria-[\-\w]+$/),K=s(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),V=s(/^(?:\w+script|data):/i),Z=s(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=s(/^html$/i),Q=s(/^[a-z][.\w]*(-[.\w]+)+$/i);var ee=Object.freeze({__proto__:null,ARIA_ATTR:$,ATTR_WHITESPACE:Z,CUSTOM_ELEMENT:Q,DATA_ATTR:q,DOCTYPE_NAME:J,ERB_EXPR:j,IS_ALLOWED_URI:K,IS_SCRIPT_OR_DATA:V,MUSTACHE_EXPR:Y,TMPLIT_EXPR:X});const te=1,ne=3,oe=7,re=8,ie=9,ae=function(){return"undefined"==typeof window?null:window};var le=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ae();const n=t=>e(t);if(n.version="3.2.4",n.removed=[],!t||!t.document||t.document.nodeType!==ie||!t.Element)return n.isSupported=!1,n;let{document:r}=t;const i=r,a=i.currentScript,{DocumentFragment:l,HTMLTemplateElement:s,Node:m,Element:p,NodeFilter:O,NamedNodeMap:v=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:D,DOMParser:Y,trustedTypes:j}=t,X=p.prototype,q=k(X,"cloneNode"),$=k(X,"remove"),V=k(X,"nextSibling"),Z=k(X,"childNodes"),Q=k(X,"parentNode");if("function"==typeof s){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let le,ce="";const{implementation:se,createNodeIterator:ue,createDocumentFragment:me,getElementsByTagName:pe}=r,{importNode:fe}=i;let de={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof o&&"function"==typeof Q&&se&&void 0!==se.createHTMLDocument;const{MUSTACHE_EXPR:he,ERB_EXPR:ge,TMPLIT_EXPR:Te,DATA_ATTR:ye,ARIA_ATTR:Ee,IS_SCRIPT_OR_DATA:Ae,ATTR_WHITESPACE:_e,CUSTOM_ELEMENT:Se}=ee;let{IS_ALLOWED_URI:Ne}=ee,be=null;const we=C({},[...x,...I,...M,...z,...H]);let Re=null;const Oe=C({},[...F,...B,...W,...G]);let ve=Object.seal(u(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Ce=null,De=null,Le=!0,ke=!0,xe=!1,Ie=!0,Me=!1,Ue=!0,ze=!1,Pe=!1,He=!1,Fe=!1,Be=!1,We=!1,Ge=!0,Ye=!1,je=!0,Xe=!1,qe={},$e=null;const Ke=C({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ve=null;const Ze=C({},["audio","video","img","source","image","track"]);let Je=null;const Qe=C({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),et="http://www.w3.org/1998/Math/MathML",tt="http://www.w3.org/2000/svg",nt="http://www.w3.org/1999/xhtml";let ot=nt,rt=!1,it=null;const at=C({},[et,tt,nt],E);let lt=C({},["mi","mo","mn","ms","mtext"]),ct=C({},["annotation-xml"]);const st=C({},["title","style","font","a","script"]);let ut=null;const mt=["application/xhtml+xml","text/html"];let pt=null,ft=null;const dt=r.createElement("form"),ht=function(e){return e instanceof RegExp||e instanceof Function},gt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ft||ft!==e){if(e&&"object"==typeof e||(e={}),e=L(e),ut=-1===mt.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,pt="application/xhtml+xml"===ut?E:y,be=b(e,"ALLOWED_TAGS")?C({},e.ALLOWED_TAGS,pt):we,Re=b(e,"ALLOWED_ATTR")?C({},e.ALLOWED_ATTR,pt):Oe,it=b(e,"ALLOWED_NAMESPACES")?C({},e.ALLOWED_NAMESPACES,E):at,Je=b(e,"ADD_URI_SAFE_ATTR")?C(L(Qe),e.ADD_URI_SAFE_ATTR,pt):Qe,Ve=b(e,"ADD_DATA_URI_TAGS")?C(L(Ze),e.ADD_DATA_URI_TAGS,pt):Ze,$e=b(e,"FORBID_CONTENTS")?C({},e.FORBID_CONTENTS,pt):Ke,Ce=b(e,"FORBID_TAGS")?C({},e.FORBID_TAGS,pt):{},De=b(e,"FORBID_ATTR")?C({},e.FORBID_ATTR,pt):{},qe=!!b(e,"USE_PROFILES")&&e.USE_PROFILES,Le=!1!==e.ALLOW_ARIA_ATTR,ke=!1!==e.ALLOW_DATA_ATTR,xe=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ie=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Me=e.SAFE_FOR_TEMPLATES||!1,Ue=!1!==e.SAFE_FOR_XML,ze=e.WHOLE_DOCUMENT||!1,Fe=e.RETURN_DOM||!1,Be=e.RETURN_DOM_FRAGMENT||!1,We=e.RETURN_TRUSTED_TYPE||!1,He=e.FORCE_BODY||!1,Ge=!1!==e.SANITIZE_DOM,Ye=e.SANITIZE_NAMED_PROPS||!1,je=!1!==e.KEEP_CONTENT,Xe=e.IN_PLACE||!1,Ne=e.ALLOWED_URI_REGEXP||K,ot=e.NAMESPACE||nt,lt=e.MATHML_TEXT_INTEGRATION_POINTS||lt,ct=e.HTML_INTEGRATION_POINTS||ct,ve=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(ve.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(ve.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(ve.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Me&&(ke=!1),Be&&(Fe=!0),qe&&(be=C({},H),Re=[],!0===qe.html&&(C(be,x),C(Re,F)),!0===qe.svg&&(C(be,I),C(Re,B),C(Re,G)),!0===qe.svgFilters&&(C(be,M),C(Re,B),C(Re,G)),!0===qe.mathMl&&(C(be,z),C(Re,W),C(Re,G))),e.ADD_TAGS&&(be===we&&(be=L(be)),C(be,e.ADD_TAGS,pt)),e.ADD_ATTR&&(Re===Oe&&(Re=L(Re)),C(Re,e.ADD_ATTR,pt)),e.ADD_URI_SAFE_ATTR&&C(Je,e.ADD_URI_SAFE_ATTR,pt),e.FORBID_CONTENTS&&($e===Ke&&($e=L($e)),C($e,e.FORBID_CONTENTS,pt)),je&&(be["#text"]=!0),ze&&C(be,["html","head","body"]),be.table&&(C(be,["tbody"]),delete Ce.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw R('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw R('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');le=e.TRUSTED_TYPES_POLICY,ce=le.createHTML("")}else void 0===le&&(le=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+r+" could not be created."),null}}(j,a)),null!==le&&"string"==typeof ce&&(ce=le.createHTML(""));c&&c(e),ft=e}},Tt=C({},[...I,...M,...U]),yt=C({},[...z,...P]),Et=function(e){g(n.removed,{element:e});try{Q(e).removeChild(e)}catch(t){$(e)}},At=function(e,t){try{g(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){g(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Fe||Be)try{Et(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},_t=function(e){let t=null,n=null;if(He)e="<remove></remove>"+e;else{const t=A(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===ut&&ot===nt&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=le?le.createHTML(e):e;if(ot===nt)try{t=(new Y).parseFromString(o,ut)}catch(e){}if(!t||!t.documentElement){t=se.createDocument(ot,"template",null);try{t.documentElement.innerHTML=rt?ce:o}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),ot===nt?pe.call(t,ze?"html":"body")[0]:ze?t.documentElement:i},St=function(e){return ue.call(e.ownerDocument||e,e,O.SHOW_ELEMENT|O.SHOW_COMMENT|O.SHOW_TEXT|O.SHOW_PROCESSING_INSTRUCTION|O.SHOW_CDATA_SECTION,null)},Nt=function(e){return e instanceof D&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof v)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},bt=function(e){return"function"==typeof m&&e instanceof m};function wt(e,t,o){f(e,(e=>{e.call(n,t,o,ft)}))}const Rt=function(e){let t=null;if(wt(de.beforeSanitizeElements,e,null),Nt(e))return Et(e),!0;const o=pt(e.nodeName);if(wt(de.uponSanitizeElement,e,{tagName:o,allowedTags:be}),e.hasChildNodes()&&!bt(e.firstElementChild)&&w(/<[/\w]/g,e.innerHTML)&&w(/<[/\w]/g,e.textContent))return Et(e),!0;if(e.nodeType===oe)return Et(e),!0;if(Ue&&e.nodeType===re&&w(/<[/\w]/g,e.data))return Et(e),!0;if(!be[o]||Ce[o]){if(!Ce[o]&&vt(o)){if(ve.tagNameCheck instanceof RegExp&&w(ve.tagNameCheck,o))return!1;if(ve.tagNameCheck instanceof Function&&ve.tagNameCheck(o))return!1}if(je&&!$e[o]){const t=Q(e)||e.parentNode,n=Z(e)||e.childNodes;if(n&&t){for(let o=n.length-1;o>=0;--o){const r=q(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,V(e))}}}return Et(e),!0}return e instanceof p&&!function(e){let t=Q(e);t&&t.tagName||(t={namespaceURI:ot,tagName:"template"});const n=y(e.tagName),o=y(t.tagName);return!!it[e.namespaceURI]&&(e.namespaceURI===tt?t.namespaceURI===nt?"svg"===n:t.namespaceURI===et?"svg"===n&&("annotation-xml"===o||lt[o]):Boolean(Tt[n]):e.namespaceURI===et?t.namespaceURI===nt?"math"===n:t.namespaceURI===tt?"math"===n&&ct[o]:Boolean(yt[n]):e.namespaceURI===nt?!(t.namespaceURI===tt&&!ct[o])&&!(t.namespaceURI===et&&!lt[o])&&!yt[n]&&(st[n]||!Tt[n]):!("application/xhtml+xml"!==ut||!it[e.namespaceURI]))}(e)?(Et(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!w(/<\/no(script|embed|frames)/i,e.innerHTML)?(Me&&e.nodeType===ne&&(t=e.textContent,f([he,ge,Te],(e=>{t=_(t,e," ")})),e.textContent!==t&&(g(n.removed,{element:e.cloneNode()}),e.textContent=t)),wt(de.afterSanitizeElements,e,null),!1):(Et(e),!0)},Ot=function(e,t,n){if(Ge&&("id"===t||"name"===t)&&(n in r||n in dt))return!1;if(ke&&!De[t]&&w(ye,t));else if(Le&&w(Ee,t));else if(!Re[t]||De[t]){if(!(vt(e)&&(ve.tagNameCheck instanceof RegExp&&w(ve.tagNameCheck,e)||ve.tagNameCheck instanceof Function&&ve.tagNameCheck(e))&&(ve.attributeNameCheck instanceof RegExp&&w(ve.attributeNameCheck,t)||ve.attributeNameCheck instanceof Function&&ve.attributeNameCheck(t))||"is"===t&&ve.allowCustomizedBuiltInElements&&(ve.tagNameCheck instanceof RegExp&&w(ve.tagNameCheck,n)||ve.tagNameCheck instanceof Function&&ve.tagNameCheck(n))))return!1}else if(Je[t]);else if(w(Ne,_(n,_e,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==S(n,"data:")||!Ve[e]){if(xe&&!w(Ae,_(n,_e,"")));else if(n)return!1}else;return!0},vt=function(e){return"annotation-xml"!==e&&A(e,Se)},Ct=function(e){wt(de.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Nt(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Re,forceKeepAttr:void 0};let r=t.length;for(;r--;){const i=t[r],{name:a,namespaceURI:l,value:c}=i,s=pt(a);let u="value"===a?c:N(c);if(o.attrName=s,o.attrValue=u,o.keepAttr=!0,o.forceKeepAttr=void 0,wt(de.uponSanitizeAttribute,e,o),u=o.attrValue,!Ye||"id"!==s&&"name"!==s||(At(a,e),u="user-content-"+u),Ue&&w(/((--!?|])>)|<\/(style|title)/i,u)){At(a,e);continue}if(o.forceKeepAttr)continue;if(At(a,e),!o.keepAttr)continue;if(!Ie&&w(/\/>/i,u)){At(a,e);continue}Me&&f([he,ge,Te],(e=>{u=_(u,e," ")}));const m=pt(e.nodeName);if(Ot(m,s,u)){if(le&&"object"==typeof j&&"function"==typeof j.getAttributeType)if(l);else switch(j.getAttributeType(m,s)){case"TrustedHTML":u=le.createHTML(u);break;case"TrustedScriptURL":u=le.createScriptURL(u)}try{l?e.setAttributeNS(l,a,u):e.setAttribute(a,u),Nt(e)?Et(e):h(n.removed)}catch(e){}}}wt(de.afterSanitizeAttributes,e,null)},Dt=function e(t){let n=null;const o=St(t);for(wt(de.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)wt(de.uponSanitizeShadowNode,n,null),Rt(n),Ct(n),n.content instanceof l&&e(n.content);wt(de.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,r=null,a=null,c=null;if(rt=!e,rt&&(e="\x3c!--\x3e"),"string"!=typeof e&&!bt(e)){if("function"!=typeof e.toString)throw R("toString is not a function");if("string"!=typeof(e=e.toString()))throw R("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Pe||gt(t),n.removed=[],"string"==typeof e&&(Xe=!1),Xe){if(e.nodeName){const t=pt(e.nodeName);if(!be[t]||Ce[t])throw R("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof m)o=_t("\x3c!----\x3e"),r=o.ownerDocument.importNode(e,!0),r.nodeType===te&&"BODY"===r.nodeName||"HTML"===r.nodeName?o=r:o.appendChild(r);else{if(!Fe&&!Me&&!ze&&-1===e.indexOf("<"))return le&&We?le.createHTML(e):e;if(o=_t(e),!o)return Fe?null:We?ce:""}o&&He&&Et(o.firstChild);const s=St(Xe?e:o);for(;a=s.nextNode();)Rt(a),Ct(a),a.content instanceof l&&Dt(a.content);if(Xe)return e;if(Fe){if(Be)for(c=me.call(o.ownerDocument);o.firstChild;)c.appendChild(o.firstChild);else c=o;return(Re.shadowroot||Re.shadowrootmode)&&(c=fe.call(i,c,!0)),c}let u=ze?o.outerHTML:o.innerHTML;return ze&&be["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&w(J,o.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+u),Me&&f([he,ge,Te],(e=>{u=_(u,e," ")})),le&&We?le.createHTML(u):u},n.setConfig=function(){gt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Pe=!0},n.clearConfig=function(){ft=null,Pe=!1},n.isValidAttribute=function(e,t,n){ft||gt({});const o=pt(e),r=pt(t);return Ot(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&g(de[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=d(de[e],t);return-1===n?void 0:T(de[e],n,1)[0]}return h(de[e])},n.removeHooks=function(e){de[e]=[]},n.removeAllHooks=function(){de={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}()}}]);