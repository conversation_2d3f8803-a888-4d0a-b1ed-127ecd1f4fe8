<?php

namespace App\Actions\Proof;
use App\Models\ProofCompletionMark;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Proof;

class DeleteCompletionMark
{
    public function execute(int $userId): void
    {
        Log::info('DeleteCompletionMark Action');
        Log::info('userId', [$userId]);

        DB::transaction(function () use ($userId) {
            ProofCompletionMark::where('user_id', $userId)->delete();
        });
    }
}