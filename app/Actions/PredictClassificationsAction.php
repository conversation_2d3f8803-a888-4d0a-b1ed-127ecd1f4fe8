<?php

namespace App\Actions;

use App\Enums\PRMEnum;
use App\Helpers\PdfParser;
use App\Http\API\PRM\Contracts\PRM;
use App\Http\API\PRM\UnnaturalTextCheckerPRM;
use App\Models\ProofFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class PredictClassificationsAction
{
    public function execute(ProofFile $proofFile, array|PRMEnum $prmEnums): array
    {
        Log::debug("PredictClassificationsAction excute call");
        return collect($prmEnums)
                ->map(function (PRMEnum $prmEnum) use ($proofFile) {
                    $prm = app($prmEnum->prm);

                    Log::debug("PredictClassificationsAction enum {$prmEnum->value} predict start");
                    if ($prmEnum->value == PRMEnum::UnnaturalTextChecker) {
                        $predict = $this->getPredictForUnnaturalTextCheck($proofFile);
                    } elseif ($prmEnum->value == PRMEnum::ImageAlert) {
                        $predict = $this->getPredictForImageAlert($prm, $proofFile);
                    } else {
                        $key = $this->getKey($prm, $prmEnum->value, $proofFile);
                        $predict = $this->getPredictFromS3Cache($key) ?? $this->getPredict($prm, $proofFile);
                        $this->rememberPredict($key, $predict);
                    }

                    Log::debug("PredictClassificationsAction enum {$prmEnum->value} predict end");
                    if ($prmEnum->value == PRMEnum::UnnaturalTextChecker) {
                        return [
                            'value' => $prmEnum->value,
                            'description' => $prmEnum->description,
                            'predicts' => $predict
                        ];
                    } elseif ($prmEnum->value == PRMEnum::ImageAlert) {
                        return [
                            'value' => $prmEnum->value,
                            'description' => $prmEnum->description,
                            'predicts' => $predict
                        ];
                    } else {
                        return [
                            'value' => $prmEnum->value,
                            'description' => $prmEnum->description,
                            'predicts' => $this->getSampleAnnotations($prm, $predict)
                        ];
                    }
                })
                ->values()
                ->toArray();
    }

    // 予測（セット事例と関連法律事例用）をキャッシュする（申請時のJobで使う）
    public function cachePredict(ProofFile $proofFile, array|PRMEnum $prmEnums)
    {
        foreach ($prmEnums as $prmEnum) {
            if ($prmEnum->value == PRMEnum::General || $prmEnum->value == PRMEnum::Legal) {
                $prm = app($prmEnum->prm);
                $key = $this->getKey($prm, $prmEnum->value, $proofFile);
                $predict = $this->getPredict($prm, $proofFile);

                $this->rememberPredict($key, $predict);
            }
        }
    }

    // レコメンドの予測結果をS3にあるキャッシュから取得
    private function getPredictFromS3Cache($key)
    {
        $dir = config("prm.cache_path");
        $path = "{$dir}/{$key}";
        if (!Storage::disk('s3')->exists($path)) {
            return null;
        }

        $content = Storage::disk('s3')->get($path);
        $predict = json_decode($content, true);
        return $predict;
    }

    private function getKey(PRM $prm, string $suffix, ProofFile $proofFile): string
    {
        $version = $prm->version();

        return sprintf(
            "%s.%s.%s",
            $version,
            $suffix,
            $proofFile->id
        );
    }

    private function getPredict(PRM $prm, ProofFile $proofFile): array
    {
        $parser = new PdfParser(
            config('filesystems.disks.s3.bucket'),
            $proofFile->path
        );

        try {
            return $prm->predict(
                data_get($parser->parse(), 'content')
            );
        } catch (\Throwable $th) {
            // テキストがない場合等、予測が失敗した場合は空の配列を返す
            // 失敗（エラー）がない。
            return [
                "result" => "fail",
                "probability" => [],
                "label" => [],
            ];
        }


    }

    // テキストチェック用予測の取得
    private function getPredictForUnnaturalTextCheck(ProofFile $proofFile): array
    {
        return (new UnnaturalTextCheckerPRM())->predicts($proofFile->proof);
    }

    // 画像アラート用予測の取得
    private function getPredictForImageAlert(PRM $prm, ProofFile $proofFile): array
    {
        return $prm->predicts(
            $proofFile
        );
    }

    private function getSampleAnnotations(PRM $prm, $predict): array
    {
        $annotations = $prm->annotationSamples();

        $recommends = collect(data_get($predict, "probability"))
                    ->map(fn ($probability, $classNo) => [
                        "probability" => $probability,
                        "label" => data_get($predict, "label.{$classNo}"),
                        "annotations" => data_get($annotations, $classNo),
                        "classNo" => $classNo,
                    ])
                    ->sortByDesc('probability')
                    ->take(5)
                    ->reject(fn ($item) => $item['probability'] <= 0.000)
                    ->values()
                    ->toArray();

        return $recommends;
    }

    private function rememberPredict(string $key, array $predict): bool
    {
        $dir = config("prm.cache_path");
        $path = "{$dir}/{$key}";
        $content = json_encode($predict);
        if ($content === false) {
            return false;
        }

        return Storage::disk('s3')->put($path, $content);
    }
}
