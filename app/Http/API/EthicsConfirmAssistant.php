<?php

namespace App\Http\API;

use App\Data\EthicsConfirmAssistantMakePdfByPlainFileData as MakeByPlainFileData;
use App\Data\RequestFormPdfData;
use App\Data\RequestFormPublishDateData;
use App\Helpers\EthicsConfirmAssistantHelper;
use App\Helpers\ProofFileManager;
use App\Models\Cartridge;
use App\Models\Proof;
use App\Models\ProofFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\Response;
use Exception;
use Illuminate\Http\Client\PendingRequest;

class EthicsConfirmAssistant
{
    public PendingRequest $client;

    public function __construct()
    {
        $this->client = Http::baseUrl(config('services.ethics-confirm-assistant.baseurl'));
    }

    /**
     * 不自然な文章チェッカーリコメンドAPI
     * @param ProofFile $proofFile 作業用ファイル
     * @return Response
     * @throws Exception
     */
    public function unnaturalTextCheck(
        ProofFile $proofFile,
        RequestFormPublishDateData $publishDate
    ) {
        $sharedKey = $proofFile->path;

        $contents = ProofFileManager::getContentFromS3($sharedKey);
        $filename = pathinfo($sharedKey, PATHINFO_BASENAME);
        $cacheDirectory = pathinfo(
            EthicsConfirmAssistantHelper::unnaturalTextCheckCacheFilename($proofFile->proof),
            PATHINFO_DIRNAME
        );
        $cartridgeElements = Cartridge::makeCartridgeElementsByThisEnv();

        return $this->client
            ->acceptJson()
            ->attach('file', $contents, $filename)
            ->post("/unnatural-text-check", [
                'directory' => $cacheDirectory,

                // 公開・稼動開始日
                "publish_date_start" => $publishDate->start?->format("Y-m-d"),

                // 公開・稼動終了日
                "publish_date_end" => $publishDate->end?->format("Y-m-d"),

                // サブコンセプト
                'sub_concepts_json' => $cartridgeElements->toJson(),
            ])
            ->throw();
    }

    public function makeCommentPage(
        RequestFormPdfData $pdf,
        RequestFormPublishDateData $publishDate
    ): Response {
        $data = [
            'comment' => $pdf->comment,
            // 公開・稼動開始日
            "publish_date_start" => $publishDate->start?->format("Y-m-d"),

            // 公開・稼動終了日
            "publish_date_end" => $publishDate->end?->format("Y-m-d"),
        ];

        return $this->client
            ->attach(
                name: 'file',
                contents: $pdf->value->getContent(),
                filename: $pdf->value->getClientOriginalName()
            )
            ->post('/make-comment-page', $data)
            ->throw();
    }

    public function makePdfByPlainFile(MakeByPlainFileData $data): Response
    {
        $data = [
            // 申請番号
            "proof_id" => $data->proofId,

            // 申請ID
            "proof_uuid" => $data->proofUuid,

            // 申請日
            "date" => $data->date->format("Y-m-d"),

            // 申請名
            "title" => $data->title,

            // 公開・稼動開始日
            "publish_date_start" => $data->publishDate->start?->format("Y-m-d"),

            // 公開・稼動終了日
            "publish_date_end" => $data->publishDate->end?->format("Y-m-d"),

            // 本文データ
            'plain_content' => [
                ...$data->texts->toArray(),
                ...$data->images->toArray(),
            ],
            // サブコンセプト
            'sub_concepts' => $data->subConcepts->toArray(),
        ];

        return $this->client
            ->post('/make-pdf-by-plain-file', $data)
            ->throw();
    }

    /**
     * @param Proof $proof
     */
    public function plainFileCheck(Proof $proof)
    {
        $data = [
            'pdf_uuid' => $proof->uuid,
        ];

        return $this->client
            ->asForm()
            ->post('/plain-file-check', $data)
            ->throw();
    }
}
