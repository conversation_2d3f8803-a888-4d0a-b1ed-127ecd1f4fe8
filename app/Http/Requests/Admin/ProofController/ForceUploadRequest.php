<?php

namespace App\Http\Requests\Admin\ProofController;

use Illuminate\Foundation\Http\FormRequest;

class ForceUploadRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file' => [
                'required',
                'file',
                'mimes:pdf',
            ],
        ];
    }

    public function messages()
    {
        return [
            'file.required' => 'ファイルを選択してください。',
            'file.file' => 'ファイルを選択してください。',
            'file.mimes' => 'PDFファイルを選択してください。',
        ];
    }
}
