<template>
  <div>
    <div class="notices-headline mp-4">
      <h3 class="pb-4"> お知らせ</h3>
    </div>
    <div v-if="!isNotEmpty" class="notices pb-4">
      <v-card elevation="1" class="notice" style="margin-bottom: 1rem; min-width: 100%;">
        <v-card-title style="height:100%;" class="d-flex">
          <span class="text-subtitle-1 font-weight-bold notice-title">システムからのお知らせ</span>
          <v-spacer></v-spacer>
          <span class="text-caption">{{ no_notice_date }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text v-html="no_notice_message" class="none-notice" />
      </v-card>
    </div>
    <div v-if="isNotEmpty" class="notices pb-4">
      <v-card
      v-for="notice in notices"
      :key="notice.id"
      style="margin-bottom: 1rem; color : rgba(64, 64, 64, 1);"
      elevation="1"
      class="notice"
      >
      <v-card-title class="d-flex">
        <span class="text-subtitle-1 font-weight-bold notice-title">
          {{ notice.title }}
        </span>
        <v-spacer></v-spacer>
        <span class="text-caption">{{ dateFormate(notice.published_at) }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text v-html="notice.body" class="notice-body"/>
      </v-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoticeList',
  props: {
    notices: {
      type: Array,
      required: true,
    },
  },
  computed: {
    isNotEmpty() {
      return this.notices?.length > 0;
    },
  },
  data() {
    return {
      no_notice_date: '',
      no_notice_message: '現在お知らせする事項はありません。',
    };
  },
  methods: {
    dateFormate(date) {
      return this.$moment(date).format('YYYY年M月D日 H時m分');
    },
  },
  created() {
    // YYYY-MM-DD HH:MM:SS
  },
};
</script>

<style lang="scss" scoped>

.notices-headline {
  color: rgba(64, 64, 64, 1) !important;
}

.notice-title::v-deep(span) {
  color: rgba(64, 64, 64, 1) !important;
}

.notice-title::v-deep(.v-card__text) {
  color: rgba(64, 64, 64, 1);
}

.none-notice {
  color: rgba(64, 64, 64, 1) !important;
  font-size: 15px;
}

div.notice-body::v-deep(p) {
  margin-bottom: 0px !important;
}

.notice{
  overflow-x: auto;
}
</style>
