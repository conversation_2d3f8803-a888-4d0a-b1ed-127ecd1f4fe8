<?php

namespace App\Http\Requests\Admin\ProofController;

use App\Data\UpdateProofFromAdminData;
use App\Models\ProofStatus;
use App\Models\User;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    public function authorize()
    {
        return !(
            $this->proof->status->value === ProofStatus::STATUS_CLOSED ||
            $this->proof->status->value === ProofStatus::STATUS_REJECT
        );
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'external_deadline_at' => "required|date",
            'internal_deadline_at' => "required|date",

            // Category
            'category_id' => [
                'required',
                'exists:categories,id'
            ],

            // IPs
            'ips' => [
                'required',
                'array',
                'min:1'
            ],
            'ips.*' => [
                'required',
                'exists:intellectual_properties,id'
            ],

            'comments' => "nullable|array",
            'comments.*.value' => "required|string",
            'comments.*.created_at' => "required|date_format:Y-m-d H:i:s",
            'comments.*.user.id' => 'required|exists:users,id',
            'comments.*.replies' => 'nullable',
        ];
    }

    public function data()
    {
        return UpdateProofFromAdminData::fromRequest($this);
    }

    public function attributes()
    {
        return [
            'ips' => '使用IP',
            'category_id' => '種別',
        ];
    }
}
