<?php

namespace App\Http\API;

use Illuminate\Support\Arr;
use Aws\CloudSearchDomain\CloudSearchDomainClient;

/**
 * Class AwsCloudSearch
 * AWS CloudSearch用 API
 */
class AwsCloudSearch
{
    // const PROMISE_STATE_PENDING = 'pending';
    // const PROMISE_STATE_FULFILLED = 'fulfilled';
    // const PROMISE_STATE_REJECTED = 'rejected';

    // Endpoint Type
    const ENDPOINT_TYPE_DOCUMENT = 'document';
    const ENDPOINT_TYPE_SEARCH = 'search';

    // Suggest Default Value
    const SUGGEST_DEFAULT_SUGGESTER = 'title';
    const SUGGEST_DEFAULT_SIZE = 10;

    /**
     * AWS CloudSearchへドキュメントをアップロードする
     * https://docs.aws.amazon.com/aws-sdk-php/v3/api/api-cloudsearchdomain-2013-01-01.html#uploaddocuments
     *
     * @param  array $documents
     *
     *  [
     *    [
     *       'type' => 'add',
     *       'id' => tf-bapi-00000001,
     *       'fields' => [
     *           'age'     => 30,                     // int
     *           'name'    => '山田太郎',              // text
     *           'check'   => [ 1, 2, 3, 4 ],         // int-array
     *           'created' => '2017-05-01T00:00:00Z', // date
     *      ],
     *    ],
     *    [...]
     *  ]
     *
     *  [
     *    [
     *       'type' => 'delete',
     *       'id' => tf-bapi-00000001,
     *    ],
     *    [...]
     *  ]
     *
     * @return array
     */
    public static function uploads(array $documents)
    {
        $client = self::initClient(self::ENDPOINT_TYPE_DOCUMENT);

        $response = $client->uploadDocuments([
            'contentType' => 'application/json',
            'documents' => json_encode($documents),
        ]);

        return $response->toArray();
    }

    /**
     * AWS CloudSearchからドキュメント検索結果を取得する
     * https://docs.aws.amazon.com/aws-sdk-php/v3/api/api-cloudsearchdomain-2013-01-01.html#search
     *
     * @param  string $query
     * @param  int $page
     * @param  string $filterQuery
     * @return void
     */
    public static function search(string $query, int $page, string $filterQuery)
    {
        $client = self::initClient(self::ENDPOINT_TYPE_SEARCH);
        $params = self::parseSearchParams($query, $page, $filterQuery);
        
        $response = $client->search($params);
        
        return $response->toArray();
    }
    
    /**
     * AWS CloudSearchからサジェストを取得する
     *
     * @param  string $query
     * @return array
     */
    public static function suggest(string $query)
    {
        $client = self::initClient(self::ENDPOINT_TYPE_SEARCH);
        
        $options = config('awscloudsearch.suggest.options');
        $response = $client->suggest([
            'query' => $query,
            'suggester' => Arr::get($options, 'suggester', self::SUGGEST_DEFAULT_SUGGESTER),
            'size' => Arr::get($options, 'size', self::SUGGEST_DEFAULT_SIZE)
        ]);
        
        return $response->toArray();
    }
        
    /**
     * AWS CloudSearchのClientを初期化する
     *
     * @param  string $endpointType
     * "document" | "search"
     *
     * @return CloudSearchDomainClient
     * https://docs.aws.amazon.com/aws-sdk-php/v3/api/class-Aws.CloudSearchDomain.CloudSearchDomainClient.html
     */
    private static function initClient(string $endpointType)
    {
        $appEnv = env('APP_ENV') === 'production' ? 'production' : 'local';
        $config = sprintf('awscloudsearch.endpoint.%s.%s', $appEnv, $endpointType);
        
        $endpoint = config($config);

        return CloudSearchDomainClient::factory([
            'endpoint'=> $endpoint,
            'version' => '2013-01-01',
        ]);
    }
    
    /**
     * AWS CloudSearchのドキュメント検索結果の条件を生成する。
     * https://docs.aws.amazon.com/ja_jp/cloudsearch/latest/developerguide/search-api.html
     *
     * @param  string $query
     * @param  int $page
     * @param  string $filterQuery
     *
     * @return array
     */
    private static function parseSearchParams(string $query, int $page, string $filterQuery)
    {
        $options = collect(config('awscloudsearch.search.options'))
                    ->filter(fn ($value) => (bool)$value)
                    ->map(function ($value, $key) {
                        if ($key === 'return') {
                            return join(',', $value);
                        }

                        return json_encode($value);
                    });
        
        $options['start'] = $options['size'] * ($page - 1); // offset
        $options['query'] = $query;

        if ($filterQuery) {
            $options['filterQuery'] = $filterQuery;
        }
        
        return $options->toArray();
    }
}
