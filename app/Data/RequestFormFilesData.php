<?php

namespace App\Data;

use App\Enums\RequestFormFileTypeEnum;
use Illuminate\Support\Collection;
use <PERSON><PERSON>\LaravelData\Data;

class RequestFormFilesData extends Data
{
    /** @var Collection<RequestFormTextData> */
    public Collection $texts;

    /** @var Collection<RequestFormImageData> */
    public Collection $images;

    /** @var Collection<RequestFormVideoData> */
    public Collection $videos;

    /** @var Collection<RequestFormPdfData> */
    public Collection $pdfs;

    /** @var Collection<RequestFormExcelData> */
    public Collection $excels;

    /** @var Collection<RequestFormWordData> */
    public Collection $words;

    /** @var Collection<RequestFormPowerPointData> */
    public Collection $powerpoints;

    public function __construct(array $files)
    {
        $groupByTypeFiles = collect($files)->groupBy('type');

        $this->texts    = $this->toTextsData($groupByTypeFiles);
        $this->images   = $this->toImagesData($groupByTypeFiles);
        $this->videos   = $this->toVideosData($groupByTypeFiles);
        $this->pdfs     = $this->toPdfsData($groupByTypeFiles);
        $this->excels   = $this->toExcelsData($groupByTypeFiles);
        $this->words    = $this->toWordsData($groupByTypeFiles);
        $this->powerpoints = $this->toPowerpointsData($groupByTypeFiles);
    }

    public function toCollection(): Collection
    {
        $files = [
            $this->texts,
            $this->images,
            $this->videos,
            $this->pdfs,
            $this->excels,
            $this->words,
            $this->powerpoints,
        ];

        return collect($files)->flatten();
    }

    public function toArray(): array
    {
        return $this->toCollection()->map->toArray()->all();
    }

    public function hasPdfs()
    {
        return $this->pdfs->isNotEmpty();
    }

    public function hasNotPdfs()
    {
        return !$this->hasPdfs();
    }

    public function hasVideos()
    {
        return $this->videos->isNotEmpty();
    }

    public function hasImages()
    {
        return $this->images->isNotEmpty();
    }

    public function hasTexts()
    {
        return $this->texts->isNotEmpty();
    }

    public function hasTextsOrImages()
    {
        return $this->hasTexts() || $this->hasImages();
    }

    public function hasNotTextsOrImages()
    {
        return !$this->hasTextsOrImages();
    }

    public function hasExcels()
    {
        return $this->excels->isNotEmpty();
    }

    public function hasWords()
    {
        return $this->words->isNotEmpty();
    }

    public function hasPowerpoints()
    {
        return $this->powerpoints->isNotEmpty();
    }

    public function hasOfficeFiles()
    {
        return $this->hasExcels() || $this->hasWords() || $this->hasPowerpoints();
    }

    public function getOfficeFiles()
    {
        return [
            'excels' => $this->excels,
            'words' => $this->words,
            'powerpoints' => $this->powerpoints,
        ];
    }

    public function hasGendocable()
    {
        return $this->hasTextsOrImages() || $this->hasOfficeFiles();
    }

    private function toTextsData(Collection $groupByTypeFiles): Collection
    {
        $texts = $groupByTypeFiles->get(RequestFormFileTypeEnum::Text, []);

        return collect($texts)->map(fn(array $text, int $index) => new RequestFormTextData(
            value: data_get($text, 'value', ''),
            comment: data_get($text, 'comment', ''),
            index: $index
        ));
    }

    private function toImagesData(Collection $groupByTypeFiles): Collection
    {
        $images = $groupByTypeFiles->get(RequestFormFileTypeEnum::Image, []);

        return collect($images)->map(fn(array $image) => new RequestFormImageData(
            value: data_get($image, 'value', ''),
            comment: data_get($image, 'comment', '')
        ));
    }

    private function toVideosData(Collection $groupByTypeFiles): Collection
    {
        $videos = $groupByTypeFiles->get(RequestFormFileTypeEnum::Video, []);

        return collect($videos)->map(fn(array $video) => new RequestFormVideoData(
            value: data_get($video, 'value', ''),
        ));
    }

    private function toPdfsData(Collection $groupByTypeFiles): Collection
    {
        $pdfs = $groupByTypeFiles->get(RequestFormFileTypeEnum::Pdf, []);

        return collect($pdfs)->map(fn(array $pdf) => new RequestFormPdfData(
            value: data_get($pdf, 'value', ''),
            comment: data_get($pdf, 'comment', '')
        ));
    }

    private function toExcelsData(Collection $groupByTypeFiles): Collection
    {
        $excels = $groupByTypeFiles->get(RequestFormFileTypeEnum::Excel, []);

        return collect($excels)->map(fn(array $excel) => new RequestFormExcelData(
            value: data_get($excel, 'value', ''),
            comment: data_get($excel, 'comment', '')
        ));
    }

    private function toWordsData(Collection $groupByTypeFiles): Collection
    {
        $words = $groupByTypeFiles->get(RequestFormFileTypeEnum::Word, []);

        return collect($words)->map(fn(array $word) => new RequestFormWordData(
            value: data_get($word, 'value', ''),
            comment: data_get($word, 'comment', '')
        ));
    }

    private function toPowerpointsData(Collection $groupByTypeFiles): Collection
    {
        $powerpoints = $groupByTypeFiles->get(RequestFormFileTypeEnum::PowerPoint, []);

        return collect($powerpoints)->map(fn(array $powerpoint) => new RequestFormPowerPointData(
            value: data_get($powerpoint, 'value', ''),
            comment: data_get($powerpoint, 'comment', '')
        ));
    }
}
