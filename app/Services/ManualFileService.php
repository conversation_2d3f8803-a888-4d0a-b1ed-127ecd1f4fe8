<?php

namespace App\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Exception;

class ManualFileService
{
    private array $manualPaths;
    private string $basePath;

    public function __construct()
    {
        $this->basePath = config('app.manual_files.base_path');
        $this->manualPaths = config('app.manual_files.files');
    }

    /**
     * Load manual content from file based on user role
     */
    public function loadManualContent(?string $userRole = null): array
    {
        try {
            // Check if user role has access to manual
            if ($userRole === null) {
                Log::warning("Manual access denied: No user role provided");
                return $this->getAccessDeniedContent();
            }

            // Only admin role (for requester users) has access
            if ($userRole !== 'admin') {
                Log::warning("Manual access denied for role: {$userRole}");
                return $this->getAccessDeniedContent();
            }

            $fileName = $this->manualPaths['admin'];
            $filePath = $this->basePath . DIRECTORY_SEPARATOR . $fileName;

            if (!File::exists($filePath)) {
                Log::warning("Manual file not found: {$filePath}");
                return $this->getFallbackContent($userRole);
            }

            $content = File::get($filePath);

            // Check if file is corrupted or binary
            if (!mb_check_encoding($content, 'UTF-8') || $this->isBinaryFile($content)) {
                Log::warning("Manual file appears to be corrupted or binary: {$filePath}");
                return $this->getFallbackContent($userRole);
            }

            return $this->parseMarkdownContent($content, $userRole);

        } catch (Exception $e) {
            Log::error("Error loading manual content: " . $e->getMessage());
            return $this->getFallbackContent($userRole);
        }
    }

    /**
     * Check if content appears to be a binary file
     */
    private function isBinaryFile(string $content): bool
    {
        // Check for common binary file signatures
        $binarySignatures = [
            'PK',     // ZIP/Office documents
            '%PDF',   // PDF
            'GIF',    // GIF
            'JFIF',   // JPEG
            "\x89PNG", // PNG
        ];

        foreach ($binarySignatures as $signature) {
            if (strpos($content, $signature) === 0) {
                return true;
            }
        }

        // Check for high percentage of non-printable characters
        $nonPrintableCount = 0;
        $totalLength = strlen($content);

        for ($i = 0; $i < min($totalLength, 1000); $i++) {
            $char = ord($content[$i]);
            if ($char < 32 && !in_array($char, [9, 10, 13])) { // Tab, LF, CR are allowed
                $nonPrintableCount++;
            }
        }

        return ($nonPrintableCount / min($totalLength, 1000)) > 0.3;
    }

    /**
     * Parse markdown content into structured data
     */
    private function parseMarkdownContent(string $content, string $userRole): array
    {
        // First try standard markdown parsing
        $sections = $this->parseStandardMarkdown($content);

        // If no sections found, try document-style parsing
        if (empty($sections)) {
            $sections = $this->parseDocumentStyle($content);
        }

        return $sections;
    }

    /**
     * Parse standard markdown format (# and ## headers)
     */
    private function parseStandardMarkdown(string $content): array
    {
        $sections = [];
        $lines = explode("\n", $content);
        $currentSection = null;
        $currentSubsection = null;
        $currentContent = [];

        foreach ($lines as $line) {
            $line = trim($line);

            if (empty($line)) {
                continue;
            }

            // Main section headers (# Header)
            if (preg_match('/^#\s+(.+)$/', $line, $matches)) {
                if ($currentSection) {
                    $sections[$currentSection] = $this->processSection($currentContent);
                }
                $currentSection = $this->slugify($matches[1]);
                $currentContent = ['title' => $matches[1], 'content' => []];
                $currentSubsection = null;
            }
            // Subsection headers (## Header)
            elseif (preg_match('/^##\s+(.+)$/', $line, $matches)) {
                if ($currentSubsection) {
                    $currentContent['content'][$currentSubsection] = $currentContent['subsection_content'] ?? [];
                }
                $currentSubsection = $this->slugify($matches[1]);
                $currentContent['subsection_content'] = ['title' => $matches[1], 'items' => []];
            }
            // List items
            elseif (preg_match('/^[-*]\s+(.+)$/', $line, $matches)) {
                if ($currentSubsection) {
                    $currentContent['subsection_content']['items'][] = $matches[1];
                } else {
                    $currentContent['content'][] = $matches[1];
                }
            }
            // Regular content
            else {
                if ($currentSubsection) {
                    if (!isset($currentContent['subsection_content']['description'])) {
                        $currentContent['subsection_content']['description'] = $line;
                    } else {
                        $currentContent['subsection_content']['description'] .= "\n" . $line;
                    }
                } else {
                    $currentContent['content'][] = $line;
                }
            }
        }

        // Add the last section
        if ($currentSection) {
            if ($currentSubsection) {
                $currentContent['content'][$currentSubsection] = $currentContent['subsection_content'] ?? [];
            }
            $sections[$currentSection] = $this->processSection($currentContent);
        }

        return $sections;
    }

    /**
     * Parse document-style format (numbered sections like "1.AI Center")
     */
    private function parseDocumentStyle(string $content): array
    {
        $sections = [];
        $lines = explode("\n", $content);
        $currentSection = null;
        $currentContent = [];

        foreach ($lines as $line) {
            $line = trim($line);

            if (empty($line)) {
                continue;
            }

            // Skip image references and table of contents
            if (strpos($line, '![alt text]') !== false || strpos($line, '目次') !== false) {
                continue;
            }

            // Document-style section headers (e.g., "1.AI Center", "2.倫理確認システム")
            if (preg_match('/^(\d+)\.(.+)$/', $line, $matches)) {
                if ($currentSection) {
                    $sections[$currentSection] = $this->processSection($currentContent);
                }
                $sectionTitle = trim($matches[2]);
                $currentSection = $this->slugify($sectionTitle);
                $currentContent = ['title' => $sectionTitle, 'content' => []];
            }
            // Subsection headers (e.g., "2.1.概要", "4.1.初回ログイン")
            elseif (preg_match('/^(\d+)\.(\d+)\.(.+)$/', $line, $matches)) {
                $subsectionTitle = trim($matches[3]);
                $currentContent['content'][] = "【{$subsectionTitle}】";
            }
            // Regular content lines
            else {
                // Skip very short lines that are likely formatting artifacts
                if (strlen($line) > 2) {
                    $currentContent['content'][] = $line;
                }
            }
        }

        // Add the last section
        if ($currentSection) {
            $sections[$currentSection] = $this->processSection($currentContent);
        }

        return $sections;
    }

    /**
     * Process section content into structured format
     */
    private function processSection(array $content): array
    {
        $processed = [
            'title' => $content['title'] ?? 'Untitled Section',
            'description' => '',
            'items' => [],
            'subsections' => []
        ];

        if (isset($content['content']) && is_array($content['content'])) {
            foreach ($content['content'] as $key => $value) {
                if (is_string($value)) {
                    $processed['items'][] = $value;
                } elseif (is_array($value)) {
                    $processed['subsections'][$key] = $value;
                }
            }
        }

        return $processed;
    }

    /**
     * Convert text to slug format
     */
    private function slugify(string $text): string
    {
        // Remove special characters and convert to lowercase
        $slug = preg_replace('/[^\p{L}\p{N}\s]/u', '', $text);
        $slug = preg_replace('/\s+/', '_', trim($slug));
        return strtolower($slug);
    }

    /**
     * Get fallback content when file loading fails
     */
    private function getFallbackContent(string $userRole): array
    {
        return [
            'overview' => [
                'title' => 'AIC倫理確認システム概要',
                'description' => 'AIC倫理確認システムは、製品や文書の倫理的妥当性を確認するためのシステムです。',
                'items' => [
                    'システムの目的: 製品・サービス・文書の倫理的リスクを事前に特定し、適切な対応を行う',
                    '対象範囲: 広告、製品仕様書、マーケティング資料、プレスリリース等',
                    'ユーザーの役割: 倫理確認申請の作成、必要書類の準備、結果の確認と対応',
                    '重要性: 企業の社会的責任を果たし、リスクを最小化するための重要なプロセス'
                ],
                'subsections' => []
            ],
            'getting_started' => [
                'title' => 'はじめに',
                'description' => 'システムの基本的な使い方について説明します。',
                'items' => [
                    'アカウントの作成とログイン',
                    '基本的な画面構成の理解',
                    '初回申請の作成方法',
                    'ヘルプとサポートの利用方法'
                ],
                'subsections' => []
            ],
            'contact' => [
                'title' => 'お問い合わせ',
                'description' => 'システムに関するお問い合わせ先です。',
                'items' => [
                    'システムサポート: <EMAIL>',
                    '倫理相談: <EMAIL>',
                    '緊急サポート: 内線1234（24時間対応）'
                ],
                'subsections' => []
            ]
        ];
    }

    /**
     * Get access denied content when user doesn't have manual access
     */
    private function getAccessDeniedContent(): array
    {
        return [
            'access_denied' => [
                'title' => 'アクセス権限がありません',
                'description' => 'このマニュアルにアクセスする権限がありません。',
                'items' => [
                    'マニュアルへのアクセスは申請者（requester）ユーザーのみに制限されています。',
                    'アクセス権限が必要な場合は、システム管理者にお問い合わせください。',
                    'ユーザータイプの変更については管理者までご連絡ください。'
                ],
                'subsections' => []
            ],
            'contact' => [
                'title' => 'お問い合わせ',
                'description' => 'アクセス権限に関するお問い合わせ先です。',
                'items' => [
                    'システム管理者: <EMAIL>',
                    'アクセス権限相談: <EMAIL>',
                    'サポート: <EMAIL>'
                ],
                'subsections' => []
            ]
        ];
    }

    /**
     * Get available manual files
     */
    public function getAvailableManuals(): array
    {
        $manuals = [];

        foreach ($this->manualPaths as $role => $fileName) {
            $filePath = $this->basePath . DIRECTORY_SEPARATOR . $fileName;
            $manuals[$role] = [
                'file_name' => $fileName,
                'file_path' => $filePath,
                'exists' => File::exists($filePath),
                'readable' => File::exists($filePath) && File::isReadable($filePath),
                'size' => File::exists($filePath) ? File::size($filePath) : 0
            ];
        }

        return $manuals;
    }

    /**
     * Validate manual file integrity
     */
    public function validateManualFile(string $userRole): array
    {
        $fileName = $this->manualPaths[$userRole] ?? null;

        if (!$fileName) {
            return [
                'valid' => false,
                'error' => 'Unknown user role',
                'details' => []
            ];
        }

        $filePath = $this->basePath . DIRECTORY_SEPARATOR . $fileName;

        if (!File::exists($filePath)) {
            return [
                'valid' => false,
                'error' => 'File does not exist',
                'details' => ['path' => $filePath]
            ];
        }

        try {
            $content = File::get($filePath);

            if ($this->isBinaryFile($content)) {
                return [
                    'valid' => false,
                    'error' => 'File appears to be binary or corrupted',
                    'details' => [
                        'path' => $filePath,
                        'size' => strlen($content),
                        'encoding' => mb_detect_encoding($content)
                    ]
                ];
            }

            return [
                'valid' => true,
                'error' => null,
                'details' => [
                    'path' => $filePath,
                    'size' => strlen($content),
                    'encoding' => mb_detect_encoding($content),
                    'line_count' => substr_count($content, "\n")
                ]
            ];

        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => 'Error reading file: ' . $e->getMessage(),
                'details' => ['path' => $filePath]
            ];
        }
    }
}