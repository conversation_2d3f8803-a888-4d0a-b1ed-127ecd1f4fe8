<?php

namespace App\Http\Middleware;

use App\Models\ProofNumber;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;

class GuardRequestForm
{
    /**
     * Handle an incoming request.
     * 新・申請フォームに「依頼者」のみが利用できるようにする（他のユーザータイプはガードする）
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next

     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = User::whereEmail($request->input('email'))->first();

        if(is_null($user)) {
            // userのemailからドメインを取得 @から.の間を取得する
            $domain = substr($request->input('email'), strpos($request->input('email'), '@') + 1, strpos($request->input('email'), '.') + 1);
            if (strpos($domain, '.') !== false) {
                $domain = substr($domain, 0, strpos($domain, '.'));
            }

            $company = ProofNumber::getCompanyCode($domain);
            if($company != 'BNXP') {
                // AICのユーザーは申請フォームを利用できない
                abort(403, '現在この機能はご利用いただけません。');
            }
            return $next($request);
        }else{
            $domain = User::getCompanyDomain($user);
            $company = ProofNumber::getCompanyCode($domain);
            if($company != 'BNXP') {
                // AICのユーザーは申請フォームを利用できない
                abort(403, '現在この機能はご利用いただけません。');
            }
        }

        // 依頼者
        if($user->user_type_id === User::USER_TYPE_REQUESTER || $user->user_type_id === User::USER_TYPE_ADMIN || $user->user_role === 'admin_role') {
            return $next($request);
        }

        abort(403, '申請者若しくはメンバー・管理者のみが利用できます');
    }
}
