<template>
  <v-dialog v-model="_drawer" scrollable content-class="v-dialog--replies">
    <v-card class="overflow-hidden" tile>
      <v-app-bar absolute color="grey" dark elevate-on-scroll height="48">
        <v-spacer></v-spacer>

        <v-btn icon @click="_drawer = false">
          <v-icon>mdi-window-close</v-icon>
        </v-btn>
      </v-app-bar>

      <!-- Comment -->
      <v-card
        style="margin-top: 48px; flex: 1;"
        tile
        elevation="0"
        class="d-flex flex-column overflow-y-auto"
      >
        <v-card-title class="text-subtitle-1 font-weight-bold">
          {{ comment.user.name }}
        </v-card-title>

        <v-card-subtitle class="text-caption">
          {{ comment.created_at }}
        </v-card-subtitle>

        <v-card-text class="text-body-2">
          <pre style="white-space: break-spaces">{{ comment.value }}</pre>
        </v-card-text>

        <div class="separator">{{ comment.replies.length }} replies</div>

        <!-- Replies -->
        <v-card elevation="0" tile style="flex: 1 0 0">
          <v-card
            v-for="reply in comment.replies"
            :key="reply.created_at"
            tile
            elevation="0"
          >
            <v-card-title class="text-subtitle-1 font-weight-bold">
              {{ reply.user.name }}

              <v-spacer></v-spacer>

              <v-tooltip bottom v-if="isYetUpdated(reply)">
                <template v-slot:activator="{ on, attrs }">
                  <v-icon color="red" v-bind="attrs" v-on="on">
                    mdi-alert-circle
                  </v-icon>
                </template>
                <span>更新ボタンを押して、コメントを反映させてください</span>
              </v-tooltip>
            </v-card-title>

            <v-card-subtitle class="text-caption">
              {{ reply.created_at }}
            </v-card-subtitle>

            <v-card-text class="text-body-2">
              <pre style="white-space: break-spaces">{{ reply.value }}</pre>
            </v-card-text>
          </v-card>
        </v-card>

        <!-- Reply Input -->
        <v-card-actions class="px-4 mt-auto" style="border: 1px solid #eeeeee">
          <v-textarea
            v-model="replyValue"
            name="reply"
            placeholder="コメント入力"
            dense
            outlined
            auto-grow
            single-line
            hide-details
            rows="1"
            row-height="30"
            class="my-2"
          >
            <template v-slot:append>
              <v-icon @click="addReply" :color="replyValue ? 'green' : 'grey'">
                mdi-arrow-right-box
              </v-icon>
            </template>
          </v-textarea>
        </v-card-actions>
      </v-card>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'CommentReplies',
  props: {
    drawer: {
      type: Boolean,
      required: true,
      default: false,
    },
    comment: {
      required: true,
    },
  },
  data() {
    return {
      replyValue: '',
    };
  },
  computed: {
    _drawer: {
      get() {
        return this.drawer;
      },
      set(value) {
        this.$emit('update:drawer', value);
      },
    },
  },
  methods: {
    addReply() {
      if (this.replyValue) {
        this.comment.replies.push({
          value: this.replyValue,
          created_at: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
          user: this.$page.props.user,
        });

        this.replyValue = '';
      }
    },

    isYetUpdated(reply) {
      return !reply.id;
    },
  },
};
</script>

<style>
.separator {
  display: flex;
  align-items: center;
  text-align: center;
  color: #9e9e9e;
  margin-bottom: 10px;
}

.separator::before,
.separator::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #eee;
}

.separator:not(:empty)::before {
  margin-right: 0.25em;
}

.separator:not(:empty)::after {
  margin-left: 0.25em;
}

.v-dialog--replies {
  width: 500px;
  height: 100vh;
  max-height: 100% !important;
  position: absolute;
  right: 0;
  margin: 0px;
}
</style>
