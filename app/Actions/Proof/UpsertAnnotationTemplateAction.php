<?php

namespace App\Actions\Proof;

use App\Data\AnnotationTemplateData;
use App\Models\AnnotationTemplate;
use App\Models\User;

class UpsertAnnotationTemplateAction
{
    public function execute(AnnotationTemplateData $data, User $user): AnnotationTemplate
    {
        // 既存のレコードを取得
        $existingTemplate = AnnotationTemplate::query()
            ->where('id', $data->id)
            ->first();
        // 既存のレコードが存在する場合は、更新
        if ($existingTemplate) {
            return AnnotationTemplate::updateOrCreate([
                'id' => $data->id,
            ], [
                ...$data->all(),
            ]);
        }else{
            return AnnotationTemplate::updateOrCreate([
                'id' => $data->id,
            ], [
                ...$data->all(),
                'user_id' => $user->id,
            ]);
        }
    }
}
