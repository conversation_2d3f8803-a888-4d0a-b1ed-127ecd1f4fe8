<?php

namespace App\Http\Controllers\Aic;

use App\Http\Controllers\Controller;
use App\Models\AicenterSystem;
use App\Models\CompanyAllowSystem;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Inertia\Inertia;
use PgSql\Lob;

class LoginController extends Controller
{
  use AuthenticatesUsers;

  /**
   * Where to redirect users after login.
   *
   * @var string
   */
  protected $redirectTo = RouteServiceProvider::AIC_HOME;

  public function loginView(Request $request)
  {
    return Inertia::render('Aic/Login');
  }

  public function authenticated()
  {
    $user = auth()->user();

    if (!$user->hasVerifiedEmail()) {
      // 証明されたURLを生成
      $signedUrl = URL::signedRoute('password.reset', [
        'user' => $user
      ]);

      auth()->logout();

      return redirect($signedUrl);
    }

    return redirect()->route('aic.index');
  }

  public function index()
  {
    if(!auth()->check()) {
      return redirect()->route('aic.login');
    }
    $user = auth()->user();
    Log::info($user);
    // $company = $user->company_id;
    // $allowSystems = CompanyAllowSystem::where('company_id', $company)->get()->toArray();
    $systems = AicenterSystem::all();
    // Log::info($allowSystems);
    return Inertia::render(
      'Aic/Index',
      [
        'user' => $user,
        // 'allowSystems' => $allowSystems,
        'systems' => $systems
      ]
    );
  }

  // AICのログイン画面に戻りたい時になったら、以下のコメントアウトを外す
  public function aic_logout(Request $request)
  {
    Log::info('aic_logout');
    $this->guard()->logout();

    $request->session()->invalidate();

    $request->session()->regenerateToken();

    if ($response = $this->loggedOut($request)) {
      return $response;
    }

    return $request->wantsJson()
      ? new JsonResponse([], 204)
      : redirect(route('aic.login_vue'));
  }
}
