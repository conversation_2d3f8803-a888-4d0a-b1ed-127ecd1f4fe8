<template>
  <v-card
    flat
    tile
    min-height="380"
    max-height="500"
    class="d-flex flex-column overflow-y-auto"
    :class="{ dragging: isDragging }"
  >
    <!-- ファイル追加 -->
    <div
      v-show="isDragging"
      id="dropDiv"
      ref="inputUpload"
      class="input-drag-upload"
      @drop.prevent="onDrop"
      @dragleave="onDragLeave"
      @dragover.prevent
    ></div>

    <!-- 空の場合 -->
    <v-card-text
      v-if="isEmpty"
      class="grow d-flex justify-center align-center grey--text"
      @dragenter="onDragEnter"
      @dragover.prevent
    >
      拡張子が「.pdf」のファイルを申請してください。<br />
      動画の場合、拡張子が「.mp4」「.wmv」「.avi」「.mpeg」「.mkv」「.m4v」のファイルを申請してください。
    </v-card-text>

    <!-- フォルダー又はファイルが存在している場合 -->
    <v-card-text
      v-else-if="!isDragging && (currentDirs.length || currentFiles.length)"
      class="grow"
      @dragenter="onDragEnter"
      @dragover.prevent
    >
      <v-list subheader v-if="currentDirs.length">
        <v-subheader>フォルダー</v-subheader>

        <v-list-item
          v-for="dir in currentDirs"
          :key="dir.model.path"
          @click="changeActive(dir.model.path)"
          class="pl-0"
        >
          <v-list-item-avatar class="ma-0">
            <v-icon>mdi-folder-outline</v-icon>
          </v-list-item-avatar>
          <v-list-item-content class="py-2">
            <v-list-item-title v-text="dir.model.name"></v-list-item-title>
          </v-list-item-content>

          <v-list-item-action>
            <v-btn v-if="isRemovable" icon @click.stop="removeDir(dir)">
              <v-icon>mdi-trash-can-outline</v-icon>
            </v-btn>
          </v-list-item-action>
        </v-list-item>
      </v-list>

      <!-- フォルダーとファイルが両方ある場合区分 -->
      <v-divider v-if="currentDirs.length && currentFiles.length"></v-divider>

      <!-- ファイルがある場合 -->
      <v-list subheader v-if="currentFiles.length">
        <v-subheader>ファイル</v-subheader>
        <v-list-item
          v-for="(file, index) in currentFiles"
          :key="index"
          class="pl-0"
        >
          <v-list-item-avatar class="ma-0">
            <v-icon>
              {{ icons[file.extension.toLowerCase()] || icons['other'] }}
            </v-icon>
          </v-list-item-avatar>
          <v-list-item-content class="py-2">
            <v-list-item-title>
              {{ file.name }}
            </v-list-item-title>
            <v-list-item-subtitle>
              {{ formatBytes(file.size) }} -
              {{ file.last_modified | moment('YYYY-MM-DD HH:mm:ss') }}
            </v-list-item-subtitle>
          </v-list-item-content>

          <v-list-item-action v-if="isRequestCreate">
            <v-select
              v-model="file.proof_language_id"
              :items="handler.languages"
              item-text="text"
              item-value="id"
              dense
              outlined
              hide-details
              :disabled="readonly"
            />
          </v-list-item-action>

          <v-list-item-action v-if="proof && !isPdfOpenable" class="ml-5">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <span v-bind="attrs" v-on="on">
                  <v-btn icon @click.stop="downloadFile(file)">
                    <v-icon>mdi-download</v-icon>
                  </v-btn>
                </span>
              </template>
              ダウンロード
            </v-tooltip>
          </v-list-item-action>

          <v-list-item-action
            v-if="proof && isPdfOpenable && isNotPlainFileChecked(file)"
            class="ml-5"
          >
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <span v-bind="attrs" v-on="on">
                  <v-btn icon @click.stop="openRecommendList(file)">
                    <v-icon>mdi-thumb-up-outline </v-icon>
                  </v-btn>
                </span>
              </template>
              アシスタント画面を開く
            </v-tooltip>
          </v-list-item-action>

          <v-list-item-action v-if="proof && isPdfOpenable" class="ml-5">
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <span v-bind="attrs" v-on="on">
                  <v-btn icon @click.stop="openPDF(file)">
                    <v-icon>mdi-open-in-new</v-icon>
                  </v-btn>
                </span>
              </template>
              PDFを開く
            </v-tooltip>
          </v-list-item-action>

          <v-list-item-action v-if="isRemovable">
            <v-btn icon @click.stop="removeFile(file)">
              <v-icon>mdi-trash-can-outline</v-icon>
            </v-btn>
          </v-list-item-action>
        </v-list-item>
      </v-list>
    </v-card-text>

    <!-- ドラッグアンドドロップ用 -->
    <v-snackbar
      v-model="isDragging"
      absolute
      timeout="-1"
      color="primary"
      content-class="text-center ma-0"
      class="dragging-snackbar"
      transition="v-dialog-bottom-transition"
    >
      <p class="ma-0">
        <v-icon size="2rem">mdi-cloud-upload-outline</v-icon>
      </p>
      <p class="white--text ma-0">
        アップロードするにはここにドロップしてください。
      </p>
    </v-snackbar>
  </v-card>
</template>

<script>
import { formatBytes } from './util';
import Confirm from './Confirm.vue';
import Path from 'path';
import S3 from '@/lib/AWS/S3';

export default {
  props: {
    icons: Object,
    handler: Object,
    readonly: Boolean,
    proof: Object,
    user: Object,
    share_path: String,
  },

  components: {
    Confirm,
  },

  data() {
    return {
      dragover: false,
    };
  },

  computed: {
    isDragging() {
      return this.dragover && this.isOriginPath && !this.readonly;
    },

    isEmpty() {
      return this.currentDirs.length === 0 && this.currentFiles.length === 0;
    },

    currentDirs() {
      return this.handler.current.children;
    },

    currentFiles() {
      return this.handler.current.model.files;
    },

    isOriginPath() {
      return this.handler.current.model.path.startsWith(
        '/' + this.handler.ORIGIN_PATH,
      );
    },

    isRequestCreate() {
      return !this.readonly && this.isOriginPath && this.proof === undefined;
    },

    isClosedPath() {
      return this.handler.current.model.path.startsWith(
        '/' + this.handler.CLOSED_PATH,
      );
    },
    isPdfOpenable() {
      return (
        this.handler.current.model.path.startsWith(
          '/' + this.handler.SHARED_PATH,
        ) &&
        (this.proof?.status.value === this.handler.PROOF_STATUS_ASSIGNMENT ||
          this.proof?.status.value === this.handler.PROOF_STATUS_PROGRESSING)
      );
    },

    isCloseCreate() {
      return (
        !this.readonly &&
        this.isClosedPath &&
        (this.proof?.status.value === this.handler.PROOF_STATUS_ASSIGNMENT ||
          this.proof?.status.value === this.handler.PROOF_STATUS_PROGRESSING)
      );
    },

    isRemovable() {
      return !this.readonly && this.isOriginPath && this.proof === undefined;
    },
  },

  methods: {
    formatBytes,

    /**
     * フォルダー移動
     * @param String path フォルダーパス /受付/aaa/bbb/xxx
     */
    changeActive(path) {
      this.handler.changeActive(path);
    },

    /**
     * フォルダー削除
     * @param Node node TreeModelのノード
     */
    removeDir(node) {
      this.handler.removeDir(node);
    },

    /**
     * ファイル削除
     * @param Object file (FileManager/util.jsのaddFileにあるfileInfo変数)
     */
    removeFile(file) {
      this.handler.removeFile(file);
    },

    /**
     * マウスがドラッグ・アンド・ドロップ範囲の中に入った場合
     * @param MouseEvent event
     */
    onDragEnter(event) {
      if (this.isOriginPath) {
        event.preventDefault();
        event.stopPropagation();
        this.dragover = true;
      }
    },

    /**
     * マウスがドラッグ・アンド・ドロップ範囲の中から離れた場合
     * @param MouseEvent event
     */
    onDragLeave(event) {
      if (this.isOriginPath) {
        event.preventDefault();
        event.stopPropagation();

        this.dragover = false;
      }
    },

    /**
     * ドロップした場合
     * @param MouseEvent event
     */
    onDrop(event) {
      event.stopPropagation();
      event.preventDefault();

      const items = event.dataTransfer.items;

      Array.from(items).forEach(item => {
        const entry = item.webkitGetAsEntry();

        this.handleEntry(entry);
      });

      this.dragover = false;
    },

    /**
     * ドロップした場合
     */
    async *getEntriesAsAsyncIterator(dirEntry) {
      const reader = dirEntry.createReader();
      const getNextBatch = () =>
        new Promise((resolve, reject) => {
          reader.readEntries(resolve, reject);
        });

      for (const entry of await getNextBatch()) {
        yield entry;
      }
    },

    async handleEntry(entry) {
      this.mkdir(entry);

      if (entry.isDirectory) {
        for await (const entry of this.getEntriesAsAsyncIterator(entry)) {
          if (entry.isDirectory) {
            await this.handleEntry(entry);
          } else {
            this.addFile(entry);
          }
        }
      } else {
        this.addFile(entry);
      }
    },

    /**
     * フォルダー生成
     */
    mkdir(entry) {
      const path = entry.isDirectory
        ? entry.fullPath
        : Path.dirname(entry.fullPath);

      if (this.handler.hasNotNode(path)) {
        this.handler.createDir(path);
      }
    },

    /**
     * ファイル生成
     */
    async addFile(entry) {
      const currentPath = this.handler.current.model.path;
      const dirName = Path.dirname(entry.fullPath);

      const path =
        dirName === '/'
          ? currentPath
          : Path.join(currentPath, Path.dirname(entry.fullPath));

      const file = await this.getFile(entry);

      this.handler.addFile(file, path);
    },

    async getFile(fileEntry) {
      try {
        return await new Promise((resolve, reject) =>
          fileEntry.file(resolve, reject),
        );
      } catch (err) {
        console.log(err);
      }
    },

    /**
     * S3からファイルダウンロード
     */
    downloadFile(file) {
      let fileName = file.name;
      let path = file.path;

      const s3 = new S3();

      if (path.charAt(0) == '/') {
        path = path.substr(1);
      }

      /**
       * S3からファイルダウンロードURLを取得
       * @param {String} path ファイルパス
       * @return {Promise<String>} ダウンロードURL Promise
       */
      const getUrl = path => s3.getSignedUrlPromise(path);

      /**
       * ダウンロードリンクを生成
       * @param {String} fileName ファイル名
       * @return {(url: String) => HTMLAnchorElement} ダウンロードリンク生成関数
       */
      const createDownloadLink = fileName => {
        /**
         * ダウンロードリンク生成
         * @param {String} url ダウンロードURL
         * @return {HTMLAnchorElement} ダウンロードリンク
         */
        return function(url) {
          const link = document.createElement('a');

          link.href = url;
          link.download = fileName;
          link.target = '_blank';

          return link;
        };
      };

      /**
       * ファイルダウンロード
       * @param {HTMLAnchorElement} link ダウンロードリンク
       * @return {void}
       */
      const download = link => {
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };

      Promise.resolve(path)
        .then(getUrl)
        .then(createDownloadLink(fileName))
        .then(download)
        .catch(error => {
          alert(error);
          console.error(error);
        });
    },

    openPDF(file) {
      const path = this.route(`${this.user.type.value}.proofs.viewer`, {
        proof: this.proof.id,
        proofFile: file.id,
      });

      window.open(path, '_blank');
    },

    openRecommendList(file) {
      const path = this.route(`${this.user.type.value}.proofs.prm.index`, {
        proof: this.proof.id,
        proofFile: file.id,
      });

      const oneThirdWith = window.innerHeight / 3;

      window.open(path, 'newwindow', `width=${oneThirdWith}`);
    },

    isNotPlainFileChecked(file) {
      return !this.handler.isPlainFileChecked(file.name);
    },
  },
};
</script>

<style lang="scss" scoped>
.v-card {
  height: 100%;
}

.dragging {
  border: dotted #1976d2;
}

.input-drag-upload {
  position: absolute;
  opacity: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
}
</style>
