<?php

namespace App\Actions\Proof;

use App\Actions\RequestForm\AssignmentMembersToProofAction;
use App\Mail\ProofAssignmentedToRequiredUserMail;
use App\Models\Proof;
use App\Models\ProofsCategories;
use App\Models\User;
use Illuminate\Support\Facades\Mail;

/**
 * Proofのカテゴリと使用IPを同期する
 */
class SyncProofCategories
{
    public function __construct(
        private AssignmentMembersToProofAction $assignmentMembersToProofAction
    ) {}

    public function execute(
        Proof $proof,
        array $ips,
        ?int $categoryId,
        bool $assignmentMembers = false
    ): void {
        if ($this->isNotUpdatableCategoriesOrIps($proof, $categoryId, $ips)) {
            return;
        }

        $this->syncIntellectualProperties($proof, $ips);
        $this->syncCategory($proof, $categoryId);

        if ($assignmentMembers) {
            $this->assignmentMembersToProof($proof);
        }
    }

    private function syncCategory(Proof $proof, ?int $categoryId)
    {
        // Delete
        if (is_null($categoryId)) {
            return ProofsCategories::query()
                ->where(['proof_id' => $proof->id])
                ->delete();
        }

        // Update Or Create
        return ProofsCategories::updateOrCreate([
            'proof_id' => $proof->id,
        ], [
            'category_id' => $categoryId
        ]);
    }

    private function syncIntellectualProperties(Proof $proof, array $ips)
    {
        $proof->ips()->sync($ips);
    }

    private function isNotUpdatableCategoriesOrIps(Proof $proof, ?int $categoryId, array $ips): bool
    {
        $currentIps = $proof->ips->pluck('id')->toArray();

        return
            is_same_array($currentIps, $ips) &&
            $proof->category->id == $categoryId;
    }

    private function assignmentMembersToProof(Proof $proof)
    {
        $proof->refresh();

        $this->assignmentMembersToProofAction
            ->execute($proof)
            ->each(
                fn(User $member) => Mail::to($member)->send(new ProofAssignmentedToRequiredUserMail($proof, $member))
            );
    }
}
