<?php

namespace App\Actions;

use App\Models\User;
use Illuminate\Support\Str;

class UpsertUserByEmail
{
    public function execute(string $email, array $attributes): User
    {
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            // Userが存在するなら$attributesからlast_nameとuser_type_idを取り除く
            unset($attributes['last_name']);
            unset($attributes['user_type_id']);
            // Update the existing user
            return User::updateOrCreate(
                [
                    'email' => $email
                ],
                [
                    'email_verified_at' => now(),
                    'remember_token' => Str::random(10),
                    ...$attributes
                ]
            );
        }else {
            // Create a new user
            return User::updateOrCreate(
                [
                    'email' => $email
                ],
                [
                    'last_name' => '',
                    'first_name' => '',
                    'email_verified_at' => now(),
                    'remember_token' => Str::random(10),
                    'password' => bcrypt(Str::random(10)),
                    ...$attributes
                ]
            );
        }
    }
}
