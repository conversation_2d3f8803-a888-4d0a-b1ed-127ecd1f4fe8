<?php

namespace App\Http\Requests\Admin\AnnotaionTemplateController;

use App\Data\AnnotationTemplateData;
use Illuminate\Foundation\Http\FormRequest;

class UpsertRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id' => [
                'nullable',
                'integer',
                'exists:annotation_templates,id'
            ],
            'annotation' => [
                "required",
                "string",
            ]
        ];
    }

    public function data(): AnnotationTemplateData
    {
        return AnnotationTemplateData::from($this->validated());
    }
}
