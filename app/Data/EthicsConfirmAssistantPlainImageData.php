<?php

namespace App\Data;

use App\Enums\EthicsConfirmAssistantPlainDataTypeEnum;
use <PERSON><PERSON>\LaravelData\Data;

class EthicsConfirmAssistantPlainImageData extends Data
{
    public EthicsConfirmAssistantPlainDataTypeEnum $type;
    public string $s3_path;
    public string $description;

    public function __construct(
        EthicsConfirmAssistantPlainDataTypeEnum $type,
        string $s3_path,
        string $description,
    ) {
        $this->type = $type;
        $this->s3_path = $this->normalizeS3Path($s3_path);
        $this->description = $description;
    }

    public function toArray(): array
    {
        return [
            'type' => $this->type->value,
            's3_path' => $this->s3_path,
            'description' => $this->description,
        ];
    }

    private function normalizeS3Path(string $s3_path): string
    {
        if (substr($s3_path, 0, 1) === '/') {
            $s3_path = substr($s3_path, 1);
        }

        return $s3_path;
    }
}
