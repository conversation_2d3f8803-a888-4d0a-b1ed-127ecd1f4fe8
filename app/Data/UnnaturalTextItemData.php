<?php

namespace App\Data;

use App\ValueObjects\Coordinate;
use <PERSON>tie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;

class UnnaturalTextItemData extends Data
{
    public function __construct(
        public PdfPageInfoData $page,
        /** @var UnnaturalTextOcrData[] */
        public DataCollection $ocr,
    ) {
    }

    public static function fromArray(array $predict): static
    {
        $isGeneratable = function (array $ocrItem): bool {
            return data_get($ocrItem, 'viewer.generatable', false) == 'true';
        };

        $toOcrDto = function (array $ocrItem): UnnaturalTextOcrData {
            return UnnaturalTextOcrData::from([
                'index' => $ocrItem['i'],
                'word' => $ocrItem['w'],
                'suggestion' => $ocrItem['s'],
                'reason' => $ocrItem['r'],
                'coords' => Coordinate::fromPoints($ocrItem['c']),
            ]);
        };

        return new static(
            page: PdfPageInfoData::from([
                'num' => $predict['page'],
                'height' => (int)($predict['page_height'] * $predict['scale']),
                'width' => (int)($predict['page_width'] * $predict['scale']),
                'scale' => $predict['scale'],
            ]),
            ocr: UnnaturalTextOcrData::collection(
                collect($predict['ocr'])
                    ->filter($isGeneratable)
                    ->map($toOcrDto)
            ),
        );
    }
}
