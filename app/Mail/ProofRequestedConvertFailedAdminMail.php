<?php

namespace App\Mail;

use App\Models\Proof;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProofRequestedConvertFailedAdminMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    public $subject;
    public $proof;
    public $user;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Proof $proof, User $user)
    {
        Log::info('ProofRequestedConvertFailedAdminMail construct');
        // $proof_id = str_pad($proof->id, 5, '0', STR_PAD_LEFT);
        $proof_no = $proof->proof_number;
        $this->subject  = "【倫理確認システム】新規の申請を受付ました（{$proof_no}）【PDF作成未了】";
        $this->proof = $proof;
        $this->user = $user;
        Log::info($this->subject);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        // IPsの情報を最新にする
        $this->proof->refresh();
        
        Log::info('ProofRequestedConvertFailedAdminMail build');
        return $this->subject($this->subject)
                    ->view('emails.proof_requested_failed_convert_admin')
                    ->with(['proof' => $this->proof, 'user_type_value' => $this->user->type_value]);
    }
}
