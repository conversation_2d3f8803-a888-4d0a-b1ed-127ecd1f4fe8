<?php

namespace App\Services\FunctionHandlers;

class GetFileUploadGuidelinesHandler
{
    /**
     * Get file upload guidelines
     */
    public function execute(array $arguments): array
    {
        $fileType = $arguments['fileType'] ?? 'all';

        $guidelines = [
            'PDF' => [
                'title' => 'PDFファイルのアップロード',
                'formats' => ['PDF'],
                'max_size' => '50MB',
                'requirements' => [
                    'テキストが選択可能であること（画像PDFは避ける）',
                    'パスワード保護されていないこと',
                    'フォントが埋め込まれていること',
                    '高解像度であること（300dpi以上推奨）'
                ],
                'tips' => [
                    'ファイル名は日本語でも英語でも可能です',
                    '複数ページのPDFも対応しています',
                    'アップロード前にファイルサイズを確認してください'
                ]
            ],
            '動画' => [
                'title' => '動画ファイルのアップロード',
                'formats' => ['MP4', 'AVI', 'MOV', 'WMV'],
                'max_size' => '500MB',
                'requirements' => [
                    '解像度: 720p以上推奨',
                    'フレームレート: 30fps以下',
                    '音声: AAC、MP3形式',
                    '長さ: 30分以内推奨'
                ],
                'tips' => [
                    'ファイルサイズが大きい場合は圧縮を検討してください',
                    'アップロードには時間がかかる場合があります',
                    '字幕がある場合は埋め込み形式を推奨します'
                ]
            ],
            '元素材' => [
                'title' => '元素材ファイルのアップロード',
                'formats' => ['PSD', 'AI', 'INDD', 'SKETCH', 'XD'],
                'max_size' => '100MB',
                'requirements' => [
                    'レイヤーが保持されていること',
                    '使用フォントが利用可能であること',
                    'リンクされた画像が含まれていること',
                    '最新バージョンで保存されていること'
                ],
                'tips' => [
                    'フォントファイルも一緒にアップロードしてください',
                    'カスタムブラシやパターンがある場合は別途アップロード',
                    'ファイル名に版数を含めることを推奨します'
                ]
            ]
        ];

        if ($fileType === 'all') {
            return [
                'success' => true,
                'guidelines' => $guidelines,
                'general_tips' => [
                    'アップロード前にファイルの破損がないか確認してください',
                    'ネットワーク環境が安定している時にアップロードしてください',
                    'アップロード中はブラウザを閉じないでください',
                    'エラーが発生した場合は時間をおいて再試行してください'
                ]
            ];
        }

        if (isset($guidelines[$fileType])) {
            return [
                'success' => true,
                'guideline' => $guidelines[$fileType],
                'file_type' => $fileType
            ];
        }

        return [
            'success' => false,
            'error' => '指定されたファイルタイプのガイドラインが見つかりません。'
        ];
    }
}