<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Services\ChatGPTAssistantService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class ChatGPTAssistantTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create();
    }

    /** @test */
    public function it_can_process_basic_query_without_chatgpt()
    {
        // Test the fallback functionality when ChatGPT is not available
        $service = new ChatGPTAssistantService();

        // Mock the ChatGPT API to fail
        Http::fake([
            'api.openai.com/*' => Http::response([], 500)
        ]);

        $result = $service->processQuery('こんにちは', 'ホーム');

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('一時的にサービスが利用できません', $result['response']);
    }

    /** @test */
    public function it_can_handle_function_calls()
    {
        // Mock successful ChatGPT response with function calls
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => null,
                            'tool_calls' => [
                                [
                                    'id' => 'call_123',
                                    'function' => [
                                        'name' => 'getCurrentPageInfo',
                                        'arguments' => json_encode(['page' => 'ホーム'])
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ])
        ]);

        $service = new ChatGPTAssistantService();
        $result = $service->processQuery('現在のページについて教えて', 'ホーム');

        $this->assertTrue($result['success']);
        $this->assertNotEmpty($result['function_calls']);
    }

    /** @test */
    public function assistant_api_requires_authentication()
    {
        $response = $this->postJson('/api/assistant/query', [
            'query' => 'テスト',
            'current_page' => 'ホーム'
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function authenticated_user_can_query_assistant()
    {
        // Mock ChatGPT API response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'こんにちは！AICシステムへようこそ。'
                        ]
                    ]
                ]
            ])
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/assistant/query', [
                'query' => 'こんにちは',
                'current_page' => 'ホーム'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ]);
    }

    /** @test */
    public function it_validates_query_parameters()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/assistant/query', [
                'query' => '', // Empty query should fail
                'current_page' => 'ホーム'
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'error' => 'Invalid request parameters'
            ]);
    }

    /** @test */
    public function it_can_get_assistant_capabilities()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/assistant/capabilities');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'success',
                'capabilities' => [
                    'functions',
                    'supported_languages',
                    'response_format',
                    'max_query_length'
                ]
            ]);
    }

    /** @test */
    public function it_can_check_system_status()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/assistant/status');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'success',
                'overall_status',
                'services' => [
                    'assistant_service',
                    'chatgpt_api',
                    'database',
                    'file_storage'
                ]
            ]);
    }

    /** @test */
    public function it_can_execute_individual_functions()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/assistant/function/getCurrentPageInfo', [
                'arguments' => ['page' => 'ホーム']
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'function_name' => 'getCurrentPageInfo'
            ]);
    }

    /** @test */
    public function it_handles_unknown_function_calls()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/assistant/function/unknownFunction', [
                'arguments' => []
            ]);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false
            ]);
    }
}