<?php

namespace App\Data;

use App\Http\Controllers\RequestFormController;
use <PERSON><PERSON>\LaravelData\Data;

class RequestFormData extends Data
{
    public function __construct(
        public string $name,
        public string $email,
        public ?string $department = null,
    ) {
    }

    public static function rules(): array
    {
        return [
            'name' => [
                'required',
                'string'
            ],
            'email' => [
                'required',
                'string',
                'email'
            ],
            'department' => [
                'nullable',
                'string'
            ]
        ];
    }

    public static function redirect(): string
    {
        return action([RequestFormController::class, 'error']);
    }
}
