<?php

namespace App\Actions\Annotations;

use App\Helpers\XmlHelper as XML;
use SimpleXMLElement;

class DeleteAnnotationsExtractAndMergeAction
{
    public function execute(SimpleXMLElement $xml, array $annotations): array
    {
        $names = XML::map(
            $xml->delete->children(),
            fn (string $id) => $id
        );

        return collect($annotations)
                ->reject(
                    fn (string $annotation) =>
                    $this->containAnnotation(
                        $names,
                        simplexml_load_string($annotation)
                    )
                )
                ->values()
                ->toArray();
    }

    private function containAnnotation(
        array $names,
        SimpleXMLElement $annotation,
    ) {
        return collect($names)
            ->contains($annotation['name']?->__toString());
    }
}
