<?php

namespace App\Services\FunctionHandlers;

use App\Models\Proof;
use Illuminate\Support\Facades\Auth;

class GetApplicationHistoryHandler
{
    /**
     * Get user's application history
     */
    public function execute(array $arguments): array
    {
        try {
            $status = $arguments['status'] ?? 'all';
            $limit = $arguments['limit'] ?? 10;
            
            $user = Auth::user();
            if (!$user) {
                return [
                    'success' => false,
                    'error' => 'ユーザーがログインしていません。'
                ];
            }

            $query = Proof::where('user_id', $user->id)
                          ->orderBy('created_at', 'desc')
                          ->limit($limit);

            // Apply status filter if specified
            if ($status !== 'all') {
                $statusMap = [
                    '承認待ち' => 'pending',
                    '承認済み' => 'approved',
                    '差し戻し' => 'rejected'
                ];
                
                if (isset($statusMap[$status])) {
                    $query->where('status', $statusMap[$status]);
                }
            }

            $applications = $query->get()->map(function ($proof) {
                return [
                    'id' => $proof->id,
                    'title' => $proof->title,
                    'status' => $this->getStatusLabel($proof->status),
                    'created_at' => $proof->created_at->format('Y-m-d H:i'),
                    'updated_at' => $proof->updated_at->format('Y-m-d H:i')
                ];
            });

            return [
                'success' => true,
                'applications' => $applications->toArray(),
                'total' => $applications->count(),
                'filter' => $status
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '申請履歴の取得に失敗しました: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get status label in Japanese
     */
    private function getStatusLabel(string $status): string
    {
        $statusLabels = [
            'pending' => '承認待ち',
            'approved' => '承認済み',
            'rejected' => '差し戻し',
            'draft' => '下書き'
        ];
        
        return $statusLabels[$status] ?? $status;
    }
}