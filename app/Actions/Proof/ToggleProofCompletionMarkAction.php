<?php

namespace App\Actions\Proof;

use App\Models\Proof;
use App\Models\ProofCompletionMark;
use App\Models\User;
use Illuminate\Support\Collection;

class ToggleProofCompletionMarkAction
{
    public function execute(Proof $proof, User $user): Collection
    {
        $this->exists($proof, $user) ?
            $this->delete($proof, $user) :
            $this->insert($proof, $user);

        return $proof->refresh()->completionMarks;
    }

    private function exists(Proof $proof, User $user): bool
    {
        return ProofCompletionMark::query()
            ->whereBelongsTo($proof)
            ->whereBelongsTo($user)
            ->exists();
    }

    private function insert(Proof $proof, User $user): void
    {
        $proof->completionMarks()->create([
            'user_id' => $user->id,
        ]);
    }

    private function delete(Proof $proof, User $user): void
    {
        ProofCompletionMark::query()
            ->whereBelongsTo($proof)
            ->whereBelongsTo($user)
            ->delete();
    }
}
