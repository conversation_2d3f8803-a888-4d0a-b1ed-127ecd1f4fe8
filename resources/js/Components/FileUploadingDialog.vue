<template>
  <v-dialog v-model="_loading" persistent width="300">
    <v-card v-if="form.hasErrors" color="error" dark>
      <v-card-title class="justify-center flex-column bg-red">
        <div>
          <v-icon style="font-size: 6em">mdi-alert</v-icon>
        </div>

        <div>
          アップロード失敗
        </div>
      </v-card-title>

      <v-card-text style="background-color: white;">
        <v-list light>
          <ul v-for="(messages, field) in errors" :key="field">
            <li>
              {{ field }}
              <ul>
                <li v-for="message in messages" :key="message">
                  {{ message }}
                </li>
              </ul>
            </li>
          </ul>
        </v-list>
      </v-card-text>

      <v-card-actions class="justify-center" style="background-color: white">
        <v-btn color="error" rounded class="mb-4" @click="_loading = false">
          閉じる
        </v-btn>
      </v-card-actions>
    </v-card>

    <v-card v-else color="primary" dark>
      <v-card-text class="pt-4">
        アップロード中

        <v-progress-linear
          v-model="percentage"
          height="12"
          color="light-blue"
          striped
          :clickable="false"
        >
          <template v-slot:default="{ value }">
            <strong style="font-size: x-small">
              {{ Math.ceil(value) }}%
            </strong>
          </template>
        </v-progress-linear>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'FileUploadingDialog',
  props: ['form', 'loading', 'text'],
  computed: {
    _loading: {
      get() {
        return this.loading;
      },
      set(value) {
        this.$emit('update:loading', value);
      },
    },
    percentage() {
      return this.form?.progress?.percentage || 0;
    },
    errors() {
      return this.form?.errors?.default || [];
    },
  },
};
</script>

<style lang="scss" scoped></style>
