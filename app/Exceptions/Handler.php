<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Support\Facades\Auth;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, \Throwable $e)
    {
        if ($e instanceof TokenMismatchException) {
            Auth::logout();

            return redirect()->route('aic.login_vue', [], 303)
                             ->with('snackbar', [
                                'color' => 'error',
                                'icon' => 'mdi-alert',
                                'title' => '失敗',
                                'message' => 'セッションの有効期限が切れました。再度ログインしてください。'
                            ]);
        }
        return parent::render($request, $e);
    }
}
