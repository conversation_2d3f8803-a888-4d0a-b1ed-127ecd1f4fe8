<template>
  <div class="speech-bubble-container" :style="{ left: position.x + 'px', top: position.y + 'px' }">
    <div class="speech-bubble" :class="{ 'expanded': hasSearchResults }">
      <!-- Close button -->
      <button class="close-button" @click="closeBubble" title="閉じる">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>

      <!-- Welcome message -->
      <div class="welcome-message">
        {{ welcomeMessage }}
      </div>

      <!-- Search input area -->
      <div class="search-input-area" :class="{ 'expanded': hasSearchResults }">
        <!-- Single textarea for both input and results -->
        <textarea
          v-model="displayText"
          @input="handleTypingStart"
          @keydown="handleTypingStart"
          @focus="handleFocus"
          @blur="handleBlur"
          @click="handleFocus"
          :readonly="hasSearchResults"
          class="search-input"
          :class="{ 'has-results': hasSearchResults }"
          ref="searchInput"
          rows="5"
        ></textarea>
      </div>

      <!-- Action buttons -->
      <div class="action-buttons">
        <button class="action-button clear-button" @click="clearText">
          クリア
        </button>
        <button class="action-button ok-button" @click="performSearch">
          検索
        </button>
      </div>

      <!-- Speech bubble tail -->
      <div class="bubble-tail"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SpeechBubble',
  props: {
    position: {
      type: Object,
      required: true
    },
    currentPage: {
      type: String,
      default: 'ホーム'
    }
  },
  emits: ['close', 'search-performed', 'search-completed', 'bubble-expanded', 'typing-started', 'typing-stopped', 'active-typing-started', 'active-typing-stopped', 'dimensions-changed'],
  data() {
    return {
      searchQuery: '',
      hasSearchResults: false,
      lastSearchQuery: '',
      searchResult: '',
      isTyping: false,
      isSearching: false,
      resizeObserver: null,
      isTextareaFocused: false,
      isActivelyTyping: false,
      typingTimeout: null
    };
  },
  computed: {
    displayText: {
      get() {
        if (this.hasSearchResults) {
          return this.searchResult;
        }
        return this.searchQuery;
      },
      set(value) {
        if (!this.hasSearchResults) {
          this.searchQuery = value;
        }
      }
    },
    welcomeMessage() {
      if (this.isSearching) {
        return 'ChatGPTを呼び出しています...';
      } else if (this.hasSearchResults) {
        return 'ChatGPTの回答を表示します。';
      } else {
        return '何について調べますか？';
      }
    }
  },
  watch: {
    currentPage() {
      this.resetSearch();
    }
  },
  mounted() {
    // Set up ResizeObserver to track bubble size changes
    this.setupResizeObserver();
    // Report initial dimensions
    this.$nextTick(() => {
      this.reportDimensions();
      // Delay auto-focus to allow getAttention animation to play first
      setTimeout(() => {
        if (this.$refs.searchInput && !this.hasSearchResults) {
          this.$refs.searchInput.focus();
        }
      }, 2100); // Wait for getAttention animation (2000ms) + small buffer
    });
  },
  methods: {
    setupResizeObserver() {
      // Set up ResizeObserver to track speech bubble size changes
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver((entries) => {
          for (let entry of entries) {
            if (entry.target.classList.contains('speech-bubble')) {
              // Throttle dimension reporting to avoid excessive updates
              this.$nextTick(() => {
                this.reportDimensions();
              });
            }
          }
        });

        // Start observing the speech bubble element
        this.$nextTick(() => {
          const speechBubble = this.$el.querySelector('.speech-bubble');
          if (speechBubble) {
            this.resizeObserver.observe(speechBubble);
          }
        });
      }
    },

    reportDimensions() {
      // Get actual dimensions of the speech bubble
      const speechBubble = this.$el.querySelector('.speech-bubble');
      if (speechBubble) {
        const rect = speechBubble.getBoundingClientRect();
        this.$emit('dimensions-changed', {
          width: rect.width,
          height: rect.height
        });
      }
    },

    clearText() {
      // Clear the search input and results
      this.searchQuery = '';
      this.hasSearchResults = false;
      this.lastSearchQuery = '';
      this.searchResult = '';
      this.isSearching = false;

      // Reset bubble state and report new dimensions
      this.$nextTick(() => {
        this.reportDimensions();
        // Focus the search input after clearing and make it editable again
        if (this.$refs.searchInput) {
          this.$refs.searchInput.focus();
          // Since we're focusing the textarea, start typing animation again
          this.isTextareaFocused = true;
          this.$nextTick(() => {
            this.startTypingAnimation();
          });
        }
      });
    },

    closeBubble() {
      // Close the speech bubble
      this.$emit('close');
    },

    scrollToSearchResults() {
      // Find the speech bubble element
      const speechBubble = this.$el.querySelector('.speech-bubble');
      if (speechBubble && this.hasSearchResults) {
        // Find the search result element
        const searchResult = speechBubble.querySelector('.search-result');
        if (searchResult) {
          // Scroll the speech bubble to show the search results
          searchResult.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest'
          });
        }
      }
    },

    async performSearch() {
      if (this.searchQuery.trim() && !this.hasSearchResults && !this.isSearching) {
        // Store the search query
        this.lastSearchQuery = this.searchQuery.trim();

        // Stop typing animation when search is performed
        this.stopTypingAnimation();

        // Set searching state
        this.isSearching = true;

        // Emit search event to parent component to trigger thinking behavior
        this.$emit('search-performed', {
          query: this.lastSearchQuery,
          page: this.currentPage,
          timestamp: new Date().toISOString(),
          result: null // Will be set after search completes
        });

        try {
          // Call ChatGPT Assistant API
          const response = await this.callChatGPTAssistant(this.lastSearchQuery, this.currentPage);

          if (response.success) {
            this.searchResult = response.response;

            // Handle function calls if any
            if (response.function_calls && response.function_calls.length > 0) {
              await this.handleFunctionCalls(response.function_calls);
            }
          } else {
            this.searchResult = response.response || 'すみません、回答を取得できませんでした。';
          }

          // Update states
          this.isSearching = false;
          this.hasSearchResults = true;

          // Emit search completed event with success status
          this.$emit('search-completed', {
            success: response.success,
            hasResponse: !!this.searchResult
          });

          // Scroll to top of textarea to show results and report new dimensions
          this.$nextTick(() => {
            if (this.$refs.searchInput) {
              this.$refs.searchInput.scrollTop = 0;
            }
            // Report dimensions after content change
            this.reportDimensions();
          });

        } catch (error) {
          console.error('ChatGPT Assistant Error:', error);
          this.searchResult = 'すみません、一時的にサービスが利用できません。しばらくしてからもう一度お試しください。';
          this.isSearching = false;
          this.hasSearchResults = true;

          // Emit search completed event even on error
          this.$emit('search-completed', {
            success: false,
            hasResponse: !!this.searchResult
          });

          this.$nextTick(() => {
            this.reportDimensions();
          });
        }
      }
    },

    generateSearchResponse(query, page) {
      // Generate contextual responses based on search query and current page
      const responses = {
        'ホーム': {
          'default': `ホームページでは、倫理確認システムとカードチェックの2つの主要機能にアクセスできます。どちらの機能についてお知りになりたいですか？`,
          'keywords': {
            // Specific responses for each navigation option
            '倫理確認システムへの移動方法': '倫理確認システムへのアクセス方法：1) ホームページで「倫理確認システム」セクションを探す、2) その下にある「移動する」ボタンをクリック、3) 倫理確認インターフェースに移動し、文書をアップロードして倫理的レビューとコンプライアンスチェックを行えます。',
            'How to navigate to Ethics Check system': '倫理確認システムへのアクセス方法：1) ホームページで「倫理確認システム」セクションを探す、2) その下にある「移動する」ボタンをクリック、3) 倫理確認インターフェースに移動し、文書をアップロードして倫理的レビューとコンプライアンスチェックを行えます。',
            'カードチェック機能へのアクセス方法': 'カードチェックへのアクセス方法：1) ホームページで「カードチェック」セクションを見つける、2) 「移動する」ボタンをクリック、3) カード仕様チェッカーに移動し、カードデザインとレイアウトをアップロードして仕様に対する自動検証を行えます。',
            'How to access Card Check functionality': 'カードチェックへのアクセス方法：1) ホームページで「カードチェック」セクションを見つける、2) 「移動する」ボタンをクリック、3) カード仕様チェッカーに移動し、カードデザインとレイアウトをアップロードして仕様に対する自動検証を行えます。',
            'メインダッシュボードの機能について': 'メインダッシュボードの提供機能：1) 倫理チェックとカードチェックシステムへのクイックアクセスタイル、2) 各主要機能のナビゲーションボタン、3) システムステータス表示、4) 最近のアクティビティ概要、5) ヘッダーエリアのユーザーアカウント情報。',
            'Understanding the main dashboard features': 'メインダッシュボードの提供機能：1) 倫理チェックとカードチェックシステムへのクイックアクセスタイル、2) 各主要機能のナビゲーションボタン、3) システムステータス表示、4) 最近のアクティビティ概要、5) ヘッダーエリアのユーザーアカウント情報。',
            'アプリケーションの使い始め方': '使い始め方：1) 2つの主要オプション（倫理チェックまたはカードチェック）から主要タスクを選択、2) 対応する「移動する」ボタンをクリック、3) アップロードと検証ワークフローに従う、4) いつでもアシスタント（イルカアイコン）を使用してヘルプを取得、5) 過去の提出については申請履歴を確認。',
            'Getting started with the application': '使い始め方：1) 2つの主要オプション（倫理チェックまたはカードチェック）から主要タスクを選択、2) 対応する「移動する」ボタンをクリック、3) アップロードと検証ワークフローに従う、4) いつでもアシスタント（イルカアイコン）を使用してヘルプを取得、5) 過去の提出については申請履歴を確認。',
            'システム概要と利用可能なツール': '利用可能なツール：1) 倫理確認システム - 倫理ガイドラインに対する文書チェック、2) カードチェックシステム - カード仕様とレイアウトの検証、3) 申請フォーム - リクエストの提出、4) 申請履歴 - 提出の追跡、5) AIアシスタント - リアルタイムヘルプとガイダンス。',
            'System overview and available tools': '利用可能なツール：1) 倫理確認システム - 倫理ガイドラインに対する文書チェック、2) カードチェックシステム - カード仕様とレイアウトの検証、3) 申請フォーム - リクエストの提出、4) 申請履歴 - 提出の追跡、5) AIアシスタント - リアルタイムヘルプとガイダンス。',
            '倫理': '倫理確認システムでは、製品や公表文書を倫理的観点からチェックできます。「移動する」ボタンをクリックしてアクセスしてください。',
            'カード': 'カードチェックでは、カード仕様と版下の整合性をチェックできます。「移動する」ボタンをクリックしてアクセスしてください。',
            '文書': '文書の保存や管理については、申請フォームから各種ファイルをアップロードして管理できます。',
            'save': 'To save documents, you can use the application form to upload and manage various file types.',
            'document': 'Document management is available through the application form where you can upload PDFs, Word files, and other formats.',
            'troubleshoot': 'For troubleshooting document issues, check the file format compatibility and ensure proper encoding settings.'
          }
        },
        '申請フォーム': {
          'default': `申請フォームでは、PDFや動画ファイルをアップロードして申請を行えます。どの項目についてお困りですか？`,
          'keywords': {
            // Specific responses for Application Form options
            'PDFファイルのアップロード方法': 'PDFファイルのアップロード方法：1) 「申請PDF・動画」セクションを見つける、2) PDFファイルをアップロードエリアにドラッグ&ドロップするか、クリックして参照、3) アップロード進行状況の完了を待つ、4) アップロードされたファイルリストにファイルが表示されることを確認、5) ファイルサイズが制限内で形式がサポートされていることを確認。',
            'How to upload PDF files': 'PDFファイルのアップロード方法：1) 「申請PDF・動画」セクションを見つける、2) PDFファイルをアップロードエリアにドラッグ&ドロップするか、クリックして参照、3) アップロード進行状況の完了を待つ、4) アップロードされたファイルリストにファイルが表示されることを確認、5) ファイルサイズが制限内で形式がサポートされていることを確認。',
            '動画ファイルのアップロード要件': '動画アップロード要件：1) サポート形式：MP4、AVI、MOV、WMV、2) 最大ファイルサイズ：通常500MB-1GB、3) 「申請PDF・動画」セクションにアップロード、4) AI分析のため良好な動画品質を確保、5) 利用可能な場合は関連メタデータを含める、6) 動画処理のため追加時間を許可。',
            'Video file upload requirements': '動画アップロード要件：1) サポート形式：MP4、AVI、MOV、WMV、2) 最大ファイルサイズ：通常500MB-1GB、3) 「申請PDF・動画」セクションにアップロード、4) AI分析のため良好な動画品質を確保、5) 利用可能な場合は関連メタデータを含める、6) 動画処理のため追加時間を許可。',
            '元素材ファイルのガイドライン': '元素材ガイドライン：1) 「元素材」セクションにオリジナルデザインファイルをアップロード、2) PSD、AI、INDD、またはその他のネイティブ形式ファイルを含める、3) これらのファイルはAIチェック精度を向上、4) 利用可能な最高解像度バージョンを提供、5) カスタムフォント使用時はフォントファイルを含める、6) 明確な命名規則でファイルを整理。',
            'Source material file guidelines': '元素材ガイドライン：1) 「元素材」セクションにオリジナルデザインファイルをアップロード、2) PSD、AI、INDD、またはその他のネイティブ形式ファイルを含める、3) これらのファイルはAIチェック精度を向上、4) 利用可能な最高解像度バージョンを提供、5) カスタムフォント使用時はフォントファイルを含める、6) 明確な命名規則でファイルを整理。',
            '必須フォーム項目の説明': '必須フォーム項目：1) 申請タイトルと説明、2) 適切なルーティングのためのカテゴリ選択、3) 締切/回答時間要件、4) フォローアップのための連絡先情報、5) 特別な指示や要件、6) 適切なセクションでのファイルアップロード、7) 利用規約の確認チェックボックス。',
            'Required form fields explanation': '必須フォーム項目：1) 申請タイトルと説明、2) 適切なルーティングのためのカテゴリ選択、3) 締切/回答時間要件、4) フォローアップのための連絡先情報、5) 特別な指示や要件、6) 適切なセクションでのファイルアップロード、7) 利用規約の確認チェックボックス。',
            '締切と回答時間の設定方法': '締切設定方法：1) 締切セクションの日付ピッカーを使用、2) 処理時間要件を考慮、3) 複雑なレビューのため追加時間を許可、4) 営業日計算を確認、5) タイムゾーン設定を確認、6) ファイルの複雑さとレビュー要件に基づいて現実的な期待を設定。',
            'Setting deadline and response times': '締切設定方法：1) 締切セクションの日付ピッカーを使用、2) 処理時間要件を考慮、3) 複雑なレビューのため追加時間を許可、4) 営業日計算を確認、5) タイムゾーン設定を確認、6) ファイルの複雑さとレビュー要件に基づいて現実的な期待を設定。',
            'PDF': 'PDFファイルは「申請PDF・動画」セクションにドラッグ&ドロップでアップロードできます。',
            '動画': '動画ファイルも「申請PDF・動画」セクションにアップロード可能です。',
            '元素材': '元素材ファイルは「元素材」セクションにアップロードしてください。AIチェック精度向上に役立ちます。',
            'upload': 'You can upload files by dragging and dropping them into the designated upload areas.',
            'video': 'Video files are supported in the PDF/Video upload section.',
            'material': 'Source material files can be uploaded to improve AI checking accuracy.'
          }
        },
        '申請履歴': {
          'default': `申請履歴では、過去の申請状況を確認できます。どのような操作をお探しですか？`,
          'keywords': {
            // Specific responses for Application History options
            '申請状況の確認方法': '申請状況の確認方法：1) 申請履歴ページを開く、2) 「承認待ち」「承認済み」「差し戻し」などを表示するステータス列を確認、3) 詳細なステータス情報については任意の申請行をクリック、4) 最終更新のタイムスタンプを確認、5) フィルターを使用して特定の申請を素早く見つける。',
            'How to check application status': '申請状況の確認方法：1) 申請履歴ページを開く、2) 「承認待ち」「承認済み」「差し戻し」などを表示するステータス列を確認、3) 詳細なステータス情報については任意の申請行をクリック、4) 最終更新のタイムスタンプを確認、5) フィルターを使用して特定の申請を素早く見つける。',
            '申請の修正と再提出方法': '修正と再提出方法：1) 「差し戻し」または「修正要求」ステータスの申請を見つける、2) 「修正」ボタンをクリック、3) ファイルまたはフォームデータに必要な変更を加える、4) すべての要件とフィードバックコメントを確認、5) 「再提出」をクリックして更新された申請を送信。',
            'Modifying and resubmitting applications': '修正と再提出方法：1) 「差し戻し」または「修正要求」ステータスの申請を見つける、2) 「修正」ボタンをクリック、3) ファイルまたはフォームデータに必要な変更を加える、4) すべての要件とフィードバックコメントを確認、5) 「再提出」をクリックして更新された申請を送信。',
            '承認済み文書のダウンロード方法': '承認済み文書のダウンロード方法：1) 「承認済み」ステータスで申請をフィルター、2) 承認された申請行をクリック、3) 「ダウンロード」ボタンまたはリンクを探す、4) 公式文書にはPDF形式を選択、5) 希望の場所にファイルを保存、6) ダウンロードが正常に完了したことを確認。',
            'Downloading approved documents': '承認済み文書のダウンロード方法：1) 「承認済み」ステータスで申請をフィルター、2) 承認された申請行をクリック、3) 「ダウンロード」ボタンまたはリンクを探す、4) 公式文書にはPDF形式を選択、5) 希望の場所にファイルを保存、6) ダウンロードが正常に完了したことを確認。',
            'ステータス別申請のフィルタリング': 'ステータス別フィルタリング方法：1) 履歴ページ上部のステータスドロップダウンフィルターを使用、2) 「全て」「承認待ち」「承認済み」「差し戻し」などのオプションから選択、3) 必要に応じて日付範囲フィルターを適用、4) 特定の申請には検索機能を使用、5) 日付、ステータス、または申請タイプで並び替え。',
            'Filtering applications by status': 'ステータス別フィルタリング方法：1) 履歴ページ上部のステータスドロップダウンフィルターを使用、2) 「全て」「承認待ち」「承認済み」「差し戻し」などのオプションから選択、3) 必要に応じて日付範囲フィルターを適用、4) 特定の申請には検索機能を使用、5) 日付、ステータス、または申請タイプで並び替え。',
            '申請履歴の検索方法': '申請履歴の検索方法：1) 検索ボックスを使用してキーワード、申請ID、またはファイル名を入力、2) 結果を絞り込むために日付範囲フィルターを適用、3) ステータスフィルターと検索語を組み合わせ、4) 関連性、日付、またはステータスで結果を並び替え、5) 利用可能な場合は高度な検索オプションを使用、6) 必要に応じて検索結果をエクスポート。',
            'Searching through application history': '申請履歴の検索方法：1) 検索ボックスを使用してキーワード、申請ID、またはファイル名を入力、2) 結果を絞り込むために日付範囲フィルターを適用、3) ステータスフィルターと検索語を組み合わせ、4) 関連性、日付、またはステータスで結果を並び替え、5) 利用可能な場合は高度な検索オプションを使用、6) 必要に応じて検索結果をエクスポート。',
            '確認': '申請状況は一覧で確認でき、ステータス別にフィルタリングも可能です。',
            '修正': '承認待ちや差し戻しされた申請は修正して再提出できます。',
            'ダウンロード': '承認済みの申請書類はPDF形式でダウンロードできます。',
            'history': 'You can view all past applications and filter them by status.',
            'download': 'Approved applications can be downloaded in PDF format.',
            'status': 'Application status can be checked and filtered in the history view.'
          }
        },
        'カードチェック': {
          'default': `カードチェックでは、カード仕様と版下の整合性を確認できます。どの機能についてお知りになりたいですか？`,
          'keywords': {
            // Specific responses for Card Check options
            'カード仕様チェックの開始方法': 'カード仕様チェックの開始方法：1) カードデザインファイル（PDF、AI、PSD形式）をアップロード、2) 対応する仕様書をアップロード、3) チェックパラメータと検証ルールを選択、4) 「チェック開始」をクリック、5) 自動分析の完了を待つ、6) 生成された比較レポートを確認。',
            'Starting a card specification check': 'カード仕様チェックの開始方法：1) カードデザインファイル（PDF、AI、PSD形式）をアップロード、2) 対応する仕様書をアップロード、3) チェックパラメータと検証ルールを選択、4) 「チェック開始」をクリック、5) 自動分析の完了を待つ、6) 生成された比較レポートを確認。',
            'チェック結果の理解': 'チェック結果の内容：1) 仕様適合スコア（一致率）、2) 発見された不一致の詳細リスト、3) 問題エリアを示すビジュアルオーバーレイ、4) 重要度レベル（重要、警告、情報）、5) 修正の推奨事項、6) 修正前後の比較ビュー、7) 結果共有のためのエクスポートオプション。',
            'Understanding check results': 'チェック結果の内容：1) 仕様適合スコア（一致率）、2) 発見された不一致の詳細リスト、3) 問題エリアを示すビジュアルオーバーレイ、4) 重要度レベル（重要、警告、情報）、5) 修正の推奨事項、6) 修正前後の比較ビュー、7) 結果共有のためのエクスポートオプション。',
            'チェック中に見つかったエラーの修正': 'エラー修正方法：1) 説明付きの詳細エラーリストを確認、2) ビジュアル指標を使用して問題エリアを特定、3) 寸法、色、フォント、位置の問題を確認、4) デザインソフトウェアで修正を実行、5) 修正されたファイルを再アップロード、6) 修正を確認するため再度チェックを実行、7) すべての重要なエラーが解決されるまで繰り返し。',
            'Fixing errors found during checks': 'エラー修正方法：1) 説明付きの詳細エラーリストを確認、2) ビジュアル指標を使用して問題エリアを特定、3) 寸法、色、フォント、位置の問題を確認、4) デザインソフトウェアで修正を実行、5) 修正されたファイルを再アップロード、6) 修正を確認するため再度チェックを実行、7) すべての重要なエラーが解決されるまで繰り返し。',
            'チェック履歴とレポートの確認': 'チェック履歴の確認方法：1) 「チェック履歴」セクションにアクセス、2) 日付、プロジェクト、またはステータスで過去のチェックを参照、3) 任意のエントリをクリックして詳細結果を表示、4) 以前のレポートをPDF形式でダウンロード、5) 異なるバージョン間で結果を比較、6) 時間の経過とともに改善を追跡。',
            'Viewing check history and reports': 'チェック履歴の確認方法：1) 「チェック履歴」セクションにアクセス、2) 日付、プロジェクト、またはステータスで過去のチェックを参照、3) 任意のエントリをクリックして詳細結果を表示、4) 以前のレポートをPDF形式でダウンロード、5) 異なるバージョン間で結果を比較、6) 時間の経過とともに改善を追跡。',
            'カードチェッカーツールの使用ガイド': 'カードチェッカーツールの機能：1) 自動仕様検証、2) ビジュアル差分検出、3) 色精度検証、4) フォントとタイポグラフィチェック、5) 寸法と位置分析、6) 複数カードのバッチ処理、7) カスタムルール設定、8) デザインワークフローとの統合。',
            'Card checker tool usage guide': 'カードチェッカーツールの機能：1) 自動仕様検証、2) ビジュアル差分検出、3) 色精度検証、4) フォントとタイポグラフィチェック、5) 寸法と位置分析、6) 複数カードのバッチ処理、7) カスタムルール設定、8) デザインワークフローとの統合。',
            'チェック': 'カードチェックを開始するには、対象ファイルを選択して「チェック開始」ボタンをクリックしてください。',
            'エラー': 'エラーが発生した場合は、エラーメッセージを確認して該当箇所を修正してください。',
            '結果': 'チェック結果は画面に表示され、レポートとして保存することも可能です。',
            'check': 'To start card checking, select the target files and click the "Start Check" button.',
            'error': 'If errors occur, review the error messages and correct the indicated issues.',
            'result': 'Check results are displayed on screen and can be saved as reports.'
          }
        },
        '倫理確認システム': {
          'default': `倫理確認システムでは、製品や公表文書を倫理的観点からチェックできます。どの手順についてお困りですか？`,
          'keywords': {
            // Specific responses for Ethics Verification options
            '倫理確認の実行方法': '倫理確認の実行方法：1) 文書または製品情報をアップロード、2) 適切な倫理フレームワーク（企業、医療、研究など）を選択、3) 倫理アンケートを完了、4) コンテキストと背景情報を提供、5) 自動および手動レビューのため提出、6) 詳細な倫理コンプライアンスレポートを受信。',
            'How to perform ethics verification': '倫理確認の実行方法：1) 文書または製品情報をアップロード、2) 適切な倫理フレームワーク（企業、医療、研究など）を選択、3) 倫理アンケートを完了、4) コンテキストと背景情報を提供、5) 自動および手動レビューのため提出、6) 詳細な倫理コンプライアンスレポートを受信。',
            '文書倫理チェックプロセス': '文書倫理チェックの内容：1) 偏見、差別、有害な言語のコンテンツ分析、2) 文化的感受性レビュー、3) 法的コンプライアンス検証、4) ファクトチェックと正確性検証、5) プライバシーとデータ保護評価、6) アクセシビリティと包括性評価、7) 最終倫理スコアと推奨事項。',
            'Document ethics checking process': '文書倫理チェックの内容：1) 偏見、差別、有害な言語のコンテンツ分析、2) 文化的感受性レビュー、3) 法的コンプライアンス検証、4) ファクトチェックと正確性検証、5) プライバシーとデータ保護評価、6) アクセシビリティと包括性評価、7) 最終倫理スコアと推奨事項。',
            '倫理チェック基準の理解': '倫理チェック基準：1) 人権コンプライアンス、2) 非差別原則、3) 文化的感受性と尊重、4) プライバシーとデータ保護、5) 真実性と正確性、6) 環境責任、7) 社会的影響評価、8) 法的および規制コンプライアンス。',
            'Understanding ethics check criteria': '倫理チェック基準：1) 人権コンプライアンス、2) 非差別原則、3) 文化的感受性と尊重、4) プライバシーとデータ保護、5) 真実性と正確性、6) 環境責任、7) 社会的影響評価、8) 法的および規制コンプライアンス。',
            '倫理チェック結果の解釈': '倫理結果の表示内容：1) 総合倫理スコア（0-100）、2) カテゴリ別評価、3) 説明付きのフラグ付きコンテンツ、4) リスクレベル指標（低、中、高）、5) 改善のための具体的推奨事項、6) 関連基準とのコンプライアンス状況、7) 解決のためのアクション項目。',
            'Interpreting ethics check results': '倫理結果の表示内容：1) 総合倫理スコア（0-100）、2) カテゴリ別評価、3) 説明付きのフラグ付きコンテンツ、4) リスクレベル指標（低、中、高）、5) 改善のための具体的推奨事項、6) 関連基準とのコンプライアンス状況、7) 解決のためのアクション項目。',
            '倫理確認ワークフロー': '倫理ワークフローの手順：1) 初期文書アップロードと分類、2) 自動コンテンツスキャンと分析、3) 複雑な問題の専門家による手動レビュー、4) 必要に応じてステークホルダー協議、5) 最終倫理評価とスコアリング、6) レポート生成と推奨事項、7) 必要に応じてフォローアップと再検証。',
            'Ethics verification workflow': '倫理ワークフローの手順：1) 初期文書アップロードと分類、2) 自動コンテンツスキャンと分析、3) 複雑な問題の専門家による手動レビュー、4) 必要に応じてステークホルダー協議、5) 最終倫理評価とスコアリング、6) レポート生成と推奨事項、7) 必要に応じてフォローアップと再検証。',
            '利用開始ガイド': '倫理確認システムの利用開始には、アカウント要件の確認、必要権限の取得、基本知識の習得、準備書類の整備が必要です。初回利用時は段階的に進めることを推奨します。',
            '申請プロセス': '申請プロセスは5段階：1)申請準備、2)申請作成、3)自動チェック、4)専門家レビュー、5)結果通知。通常案件で3-5営業日、緊急案件は24時間以内の対応が可能です。',
            '文書ガイドライン': 'ファイル要件：PDF、Word、PowerPoint、画像ファイル対応。単一ファイル最大50MB、複数ファイル合計200MB。高解像度（300dpi以上）推奨。',
            'よくある問題': '頻出問題：差別的表現、誇大広告、プライバシー侵害、文化的配慮不足。事前チェック、専門家相談、過去事例参照、継続学習で予防可能です。',
            '倫理': '倫理チェックでは、製品の倫理的妥当性を多角的に確認します。',
            '文書': '公表文書のチェックでは、内容の適切性を確認します。',
            '手順': '必要書類をアップロードし、チェックリストに従って確認を進めてください。',
            'マニュアル': 'AIC倫理確認システムの詳細マニュアルをご確認いただけます。システム概要、利用開始ガイド、申請プロセス、評価基準、よくある質問など、包括的な情報を提供しています。',
            '倫理確認マニュアル': 'メンバー向けの詳細な倫理確認システムマニュアルです。申請方法、評価基準、トラブルシューティングなど、実務に必要な情報を網羅しています。',
            '申請方法': '倫理確認の申請は、システム概要の理解→必要資料の準備→申請書作成→ファイルアップロード→提出の順で進めます。詳細な手順はマニュアルをご確認ください。',
            '評価基準': '倫理確認では、人権・ダイバーシティ、文化的配慮、プライバシー・安全性、法的コンプライアンス、社会的責任の5つの観点から評価します。',
            'トラブルシューティング': 'よくある問題として、ファイルアップロード失敗、チェック結果表示の遅延、予想より低いスコアなどがあります。各問題の解決方法をマニュアルで確認できます。',
            'ethics': 'The ethics system checks products and documents from ethical perspectives.',
            'document': 'Document ethics checking verifies content appropriateness.',
            'process': 'Upload required documents and follow the checklist for verification.'
          }
        }
      };

      const pageResponses = responses[page] || responses['ホーム'];

      // Check for keyword matches
      for (const [keyword, response] of Object.entries(pageResponses.keywords || {})) {
        if (query.toLowerCase().includes(keyword.toLowerCase())) {
          return response;
        }
      }

      // Return default response for the page
      return pageResponses.default;
    },

    resetSearch() {
      // Reset search state to default
      this.searchQuery = '';
      this.hasSearchResults = false;
      this.lastSearchQuery = '';
      this.searchResult = '';
      this.isSearching = false;

      // Emit event to parent to reset bubble dimensions
      this.$emit('bubble-expanded', {
        expanded: false,
        width: 320,
        height: 120
      });
    },

    handleFocus() {
      // When textarea gets focus, start typing animation
      if (!this.hasSearchResults) {
        this.isTextareaFocused = true;

        // Use nextTick to ensure DOM is updated and then start animation
        this.$nextTick(() => {
          this.startTypingAnimation();
        });
      }
    },

    handleBlur() {
      // When textarea loses focus, stop typing animation
      this.isTextareaFocused = false;
      this.stopTypingAnimation();
    },

    handleTypingStart() {
      // This method is called on input/keydown events
      // If textarea is focused, ensure typing animation is running
      if (this.isTextareaFocused && !this.hasSearchResults) {
        this.startActiveTyping();
        this.startTypingAnimation();
      }
    },

    startActiveTyping() {
      // User is actively typing - use normal speed animation
      if (!this.isActivelyTyping) {
        this.isActivelyTyping = true;
        this.$emit('active-typing-started');
      }

      // Clear any existing timeout
      if (this.typingTimeout) {
        clearTimeout(this.typingTimeout);
      }

      // Set timeout to detect when user stops typing (500ms of inactivity)
      this.typingTimeout = setTimeout(() => {
        this.stopActiveTyping();
      }, 500);
    },

    stopActiveTyping() {
      // User stopped actively typing - switch to slow animation
      if (this.isActivelyTyping) {
        this.isActivelyTyping = false;
        this.$emit('active-typing-stopped');
      }

      // Clear timeout
      if (this.typingTimeout) {
        clearTimeout(this.typingTimeout);
        this.typingTimeout = null;
      }
    },

    startTypingAnimation() {
      // Start typing animation if not showing results and not already typing
      if (!this.hasSearchResults && !this.isTyping) {
        this.isTyping = true;
        this.$emit('typing-started');
      }
    },

    stopTypingAnimation() {
      // Stop typing animation
      if (this.isTyping) {
        this.isTyping = false;
        this.$emit('typing-stopped');
      }
      // Also stop active typing
      this.stopActiveTyping();
    },

    handleTypingStop() {
      // This method is called when clearing or searching
      this.stopTypingAnimation();
    },

    async callChatGPTAssistant(query, currentPage) {
      try {
        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
          throw new Error('CSRF token not found. Please refresh the page.');
        }

        const response = await fetch('/api/assistant/query', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
          },
          credentials: 'same-origin', // Include session cookies
          body: JSON.stringify({
            query: query,
            current_page: currentPage,
            context: {
              timestamp: new Date().toISOString(),
              user_agent: navigator.userAgent,
              page_url: window.location.href
            }
          })
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response body:', errorText);
          throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        return data;

      } catch (error) {
        console.error('ChatGPT API call failed:', error);
        throw error;
      }
    },

    async handleFunctionCalls(functionCalls) {
      for (const functionCall of functionCalls) {
        try {
          await this.executeFunctionCall(functionCall);
        } catch (error) {
          console.error('Function call execution failed:', error);
        }
      }
    },

    async executeFunctionCall(functionCall) {
      const { name, result } = functionCall;

      switch (name) {
        case 'getCurrentPageInfo':
          if (result.success) {
            console.log('Current page info:', result);
            // This function provides page context - no specific action needed
          }
          break;

        case 'getSystemStatus':
          if (result.success) {
            console.log('System status:', result);
            // This function provides system status - no specific action needed
          }
          break;

        case 'navigateToPage':
          if (result.success && result.action === 'navigate') {
            // Emit navigation event to parent component
            this.$emit('navigate-requested', {
              page: result.page,
              route: result.route,
              url: result.url
            });

            // Optionally navigate directly
            if (result.url) {
              window.location.href = result.url;
            }
          }
          break;

        case 'getApplicationHistory':
          if (result.success) {
            // Emit event to show application history
            this.$emit('show-application-history', result.applications);
          }
          break;

        case 'getFileUploadGuidelines':
          if (result.success) {
            // Emit event to show file guidelines
            this.$emit('show-file-guidelines', result);
          }
          break;

        case 'checkEthicsGuidelines':
          if (result.success) {
            // Emit event to show ethics guidelines
            this.$emit('show-ethics-guidelines', result);
          }
          break;

        case 'getCardCheckInstructions':
          if (result.success) {
            // Emit event to show card check instructions
            this.$emit('show-card-instructions', result);
          }
          break;

        case 'getEthicsManual':
          if (result.success) {
            // Emit event to show ethics manual
            this.$emit('show-ethics-manual', result);
          }
          break;

        default:
          console.log('Unknown function call:', name, result);
      }
    },

    getAuthToken() {
      // Try to get token from meta tag or localStorage
      const metaToken = document.querySelector('meta[name="api-token"]');
      if (metaToken) {
        return metaToken.getAttribute('content');
      }

      // Fallback to session token or other auth method
      return localStorage.getItem('auth_token') || '';
    }
  },

  beforeUnmount() {
    // Clean up ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    // Clean up typing timeout
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  }
};
</script>

<style scoped>
.speech-bubble-container {
  position: fixed;
  z-index: 10000;
  pointer-events: none;
}

.speech-bubble {
  position: relative;
  background: #FFFACD; /* Light yellow/cream background */
  border: 2px solid #D4AF37; /* Golden border */
  border-radius: 12px;
  padding: 16px;
  width: 340px; /*335.2 */
  height: auto;
  min-height: 250px;
  max-height: 700px;
  overflow-y: scroll;/*auto;*/
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
  display: flex;
  flex-direction: column;
  overflow: visible;
  font-family: 'Roboto, sans-serif';/*'Inter', 'SF Pro Display', 'Segoe UI', sans-serif;*/
  transition: min-height 0.3s ease, max-height 0.3s ease;

  /* Hide scrollbar for webkit browsers
  scrollbar-width: none; /* Firefox 
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

/* .speech-bubble::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
/*} */

.speech-bubble.expanded {
  min-height: 250px;
  max-height: 450px;
}

/* Close Button Styles */
.close-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
  color: #666;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.close-button:hover {
  background: rgba(255, 255, 255, 1);
  color: #404040;;
  transform: scale(1.05);
}

.close-button:active {
  transform: scale(0.95);
}

/* Remove complex animations and pseudo-elements */

.welcome-message {
  color: #404040;;
  font-size: 18.72px;
  font-weight: 600;
  margin-bottom: 8px;
  padding-bottom: 4px;
  text-align: left;
  font-family: 'Roboto, sans-serif';/*'Inter', 'SF Pro Display', 'Segoe UI', sans-serif;*/
  line-height: 1.3;
  position: relative;
  flex-shrink: 0;
}

/* Search Input Area Styles */
/* .search-input-area {
  margin: 4px 0 2px 0;
  order: 2;
  position: relative;
  flex-shrink: 0;
  z-index: 1;
  width: 100%;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  transition: min-height 0.3s ease;
}

.search-input-area.expanded {
  min-height: 250px;
} */

/* Search Results Display (inside input area) */
.search-results-display {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: 100px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.search-results-display::-webkit-scrollbar {
  display: none;
}

.search-results-header {
  font-size: 18.72px;
  font-weight: 600;
  color: #404040;;
  margin-bottom: 12px;
  font-family: 'Roboto, sans-serif';/*'Inter', 'SF Pro Display', 'Segoe UI', sans-serif;*/
}

.search-result {
  font-size: 18.72px;
  line-height: 1.6;
  color: #404040;;
  font-family: 'Roboto, sans-serif';/*'Inter', 'SF Pro Display', 'Segoe UI', sans-serif;*/
  font-weight: 400;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 15px;
  font-family: 'Roboto, sans-serif';
  font-weight: 400;
  background: rgba(255, 255, 255, 0.9);
  color: #404040;
  min-height: 180px;
  max-height: 400px;
  resize: vertical;/*none;*/
  outline: none;
  overflow-y: auto;
  box-sizing: border-box;
  line-height: 1.4;
  flex: 1;
  display: block;

  /* Show the scrollbar */
  scrollbar-width: auto;         /* Firefox */
  -ms-overflow-style: auto;      /* IE */
}

.search-input::-webkit-scrollbar {
  display: block;        /* Show scroll in Chrome/Safari */
  width: 8px;
}

.search-input::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.3);
  border-radius: 4px;
}

.search-input:focus {
  outline: none;
  border-color: #999;
  box-shadow: 0 0 0 2px rgba(153, 153, 153, 0.2);
}

/* .search-input::placeholder {
  color: #999;
  font-weight: 400;
} */

.search-input.has-results {
  background: rgba(255, 255, 255, 0.9);
  border-color: #ccc;
  color: #404040;
  cursor: default;
  user-select: text;
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 250px;
  max-height: 450px;
}

.search-input.has-results:focus {
  border-color: #999;
  box-shadow: 0 0 0 2px rgba(153, 153, 153, 0.2);
}

/* Action Buttons Styles */
.action-buttons {
  display: flex;
  justify-content: flex-end; /* Position buttons to the right */
  gap: 6px;
  margin-top: 8px;
  margin-bottom: 0px;
  padding-top: 4px;
  order: 3;
  flex-shrink: 0;
  z-index: 1;
  width: 100%;
}

.action-button {
  padding: 6px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background: #FFFACD; /* Match speech bubble yellow background */
  color: #404040;
  font-size: 18.72px;
  font-family: 'Roboto, sans-serif';/*'Inter', 'SF Pro Display', 'Segoe UI', sans-serif;*/
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  flex: 1;
  min-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background: #F5F5DC; /* Slightly darker yellow on hover */
  border-color: #999;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.action-button:active {
  transform: translateY(0px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.clear-button {
  flex: 1;
  max-width: 120px;
}

.ok-button {
  flex: 1;
  max-width: 120px;
  background: #FFFACD; /* Match speech bubble yellow background */
  color: #404040;
  border-color: #ccc;
}

.ok-button:hover {
  background: #F5F5DC; /* Slightly darker yellow on hover */
  border-color: #999;
}

.bubble-tail {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-top: 20px solid #F5F5DC;
  z-index: 10;
}

/* Add stroke to bubble tail using pseudo-element */
.bubble-tail::before {
  content: '';
  position: absolute;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 14px solid transparent;
  border-right: 14px solid transparent;
  border-top: 22px solid #F5F5DC;
  z-index: -1;
}
</style>