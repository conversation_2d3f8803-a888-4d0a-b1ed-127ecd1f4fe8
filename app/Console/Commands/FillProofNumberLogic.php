<?php

namespace App\Console\Commands;

use App\Models\Proof;
use App\Models\ProofNumber;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FillProofNumberLogic extends Command
{
    /**
     * 申請番号追加のデプロイ時・ローカルにて申請番号の追加時１回のみ実行するコマンド
     */

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:fill-proof-number-logic';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This Command fill proofs proof_number column';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('start fill proof_number');
        $ids = $this->getProofIds();
        // idsをループさせ、Proofからid１つ毎にidが5桁未満なら0埋めをしてBNXP-00001の形にし、Proofのproof_numberに格納する
        DB::beginTransaction();
        try {
            foreach($ids as $id) {
                $proof = Proof::find($id);
                if ($proof) {
                    $user = User::find($proof->user_id);
                    if ($user) {
                        $companyDomain = User::getCompanyDomain($user);
                        $this->info('company_domain: ' . $companyDomain);
                    } else {
                        continue;
                    }
                    if ($companyDomain) {
                        $proofNumber = ProofNumber::getProofNumber($companyDomain);
                        $this->info('Proof ID: ' . $id . ' proof_number: ' . $proofNumber);
                        $proof->proof_number = $proofNumber;
                        $proof->save();
                    } else {
                        $this->error('Proof ID: ' . $id . ' company_code not found');
                        throw new \Exception('company_code not found');
                    }
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
        // foreach($ids as $id) {
        //     $proof = Proof::find($id);
        //     if ($proof) {
        //         $requestUser = User::find($proof->user_id);
        //         if ($requestUser) {
        //             $companyDomain = User::getCompanyDomain($requestUser);
        //             $this->info('company_domain: ' . $companyDomain);
        //         } else {
        //             continue;
        //         }
        //         $proofNumber = ProofNumber::getProofNumber($companyDomain);
        //         $this->info('Proof ID: ' . $id . ' proof_number: ' . $proofNumber);
        //     }
        // }
        $this->info('end fill proof_number');
        return 0;
    }

    private function getProofIds () {
        // Proofsのcreated_atが2025-04-01 00:00:00以降のidを取得する
        $proofs = Proof::where('created_at', '>=', '2025-04-01 00:00:00')
            ->where('proof_number', null)
            ->orderBy('id', 'asc')
            ->get();
        // 取得したProofsのidを配列で返す
        return $proofs->pluck('id')->toArray();
    }
}
