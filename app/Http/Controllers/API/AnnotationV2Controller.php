<?php

namespace App\Http\Controllers\API;

use App\Actions\Annotations\AddAnnotationsExtractAndMergeAction;
use App\Actions\Annotations\DeleteAnnotationsExtractAndMergeAction;
use App\Actions\Annotations\ModifyAnnotationsExtractAndMergeAction;
use App\Data\AnnotationSyncData;
use App\Http\Controllers\Controller;
use App\Models\ProofFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AnnotationV2Controller extends Controller
{
    /// TESTING FUNCTION (delete later)
    public function syncTest(Request $request, ProofFile $proofFile){
        $start = microtime(true);
        
        // DB::beginTransaction();
        // try {

        Log::info("DB操作");
        $annotations = $request->input('annotations');
        Log::info("入力配列", [$annotations]);
        
        $xmlResults = [];
        if (!empty($annotations)) {
            foreach ($annotations as $index => $anno) {
                Log::info("関数入力", [$anno]);
                #$annoJson = json_encode($anno);
                $xmlResult = self::convertJsonToXml($anno);
                Log::info("関数出力", [$xmlResult]);
                $xmlResults[$index] = $xmlResult;
            }
            $annotations = $xmlResults;
            Log::info("出力配列", [$annotations]);
        } else {
            $annotations = null;
            Log::info("proof_file", [$annotations]);
        }


        // $proofFile->update(['annotations' => $jsonResults]);
        $proofFile->update(['annotations' => $annotations]);
        //     DB::transaction(function () use (
        //         $request,
        //         $proofFile
        //     ) {
        //         $annotations = $request->input('annotations');
        //         $action = $request->input('action');
        //         $currentAnnotations = collect($proofFile->annotations);
                
        //         // アクションタイプに基づいて処理
        //         if ($action === 'add') {
        //             // 新しいアノテーションを追加
        //             $currentAnnotations->push($annotations);
        //         } else if ($action === 'edit') {
        //             // 既存のアノテーションを更新
        //             $index = $currentAnnotations->search(function ($item) use ($annotations) {
        //                 return isset($item['id']) && isset($annotations['id']) && $item['id'] == $annotations['id'];
        //             });
                    
        //             if ($index !== false) {
        //                 $currentAnnotations[$index] = $annotations;
        //             }
        //         } else if ($action === 'delete') {
        //             // アノテーションを削除
        //             $currentAnnotations = $currentAnnotations->filter(function ($item) use ($annotations) {
        //                 return !isset($item['id']) || !isset($annotation['id']) || $item['id'] != $annotations['id'];
        //             });
        //         }
                
        //         // 配列が空の場合はNULLとして保存
        //         $annotationsToSave = $currentAnnotations->isEmpty() ? null : $currentAnnotations->values()->all();
        //         $proofFile->update(['annotations' => $annotationsToSave]);
        //     });
        //     DB::commit();
        // } catch (\Throwable $th) {
        //     Log::info('syncTest', [$th]);
        //     DB::rollBack();
        //     throw $th;
        // }
    
        // $end = microtime(true);
    
        // return response()->json([
        //     'xfdf' => "", //$data->xfdf->asXML(),
        //     'action' => $request->input('action'), // アクションを返す
        //     'info' => "", //$data->info,
        //     'time' => $end - $start
        // ]);
    }

    public function sync(
        AddAnnotationsExtractAndMergeAction $addAction,
        ModifyAnnotationsExtractAndMergeAction $modifyAction,
        DeleteAnnotationsExtractAndMergeAction $deleteAction,
        AnnotationSyncData $data,
        ProofFile $proofFile
    ) {
        $start = microtime(true);

        try {
            DB::transaction(function () use (
                $addAction,
                $modifyAction,
                $deleteAction,
                $data,
                $proofFile
            ) {
                $annotations = collect($proofFile->annotations)
                                ->pipeThrough([
                                    fn (Collection $annotations) => $annotations->toArray(),
                                    fn (array $annotations) =>
                                        $addAction->execute($data->xfdf, $annotations),
                                    fn (array $annotations) =>
                                        $modifyAction->execute($data->xfdf, $annotations),
                                    fn (array $annotations) =>
                                        $deleteAction->execute($data->xfdf, $annotations),
                                ]);

                $proofFile->update(['annotations' => $annotations]);
            });
        } catch (\Throwable $th) {
            throw $th;
        }

        $end = microtime(true);

        return response()->json([
            'xfdf' => $data->xfdf->asXML(),
            'action' => $data->action,
            'info' => $data->info,
            'time' => $end - $start
        ]);
    }

    function convertJsonToXml($json_str) {
        
        #$json_obj = json_decode($json_str);
#
        #// JSON解析エラーをチェック
        #if (json_last_error() !== JSON_ERROR_NONE) {
        #    die('JSON解析エラー: ' . json_last_error_msg());
        #}
    #
        #// アノテーションデータを取得
        #$annotation = $json_obj->annotation;
        #
        #// 親コメントを特定する
        #$parentComment = null;
        #$childComments = [];
        #
        #
        #foreach ($annotation->comments as $comment) {
        #    
        #    if ($comment->id == $comment->parent_id) {
        #        $parentComment = $comment;
        #    } else if (isset($comment->parent_id)) {
        #        $childComments[] = $comment;
        #    }
        #}
        #
        #// 親コメントが見つからない場合は最初のコメントを使用
        #if (!$parentComment && count($annotation->comments) > 0) {
        #    $parentComment = $annotation->comments[0];
        #}
        #
        #// 結果のXML文字列
        #$result = '';
        #
        #// 親コメントを処理
        #if ($parentComment) {
        #    // position(x,y)とsize(width,height)からrect属性を計算
        #    $x = $annotation->position->x;
        #    $y = $annotation->position->y;
        #    $width = $annotation->size->width;
        #    $height = $annotation->size->height;
        #    $rect = "$x,$y," . ($x + $width) . "," . ($y + $height);
        #    
        #    // color属性を設定
        #    $colorMap = [
        #        'red' => '#E44234',
        #        'blue' => '#1A4FFF',
        #        'green' => '#00B050',
        #        'yellow' => '#FFFF00',
        #        'orange' => '#FF9C00',
        #        'purple' => '#7030A0',
        #        'pink' => '#FF00FF',
        #        'magenta' => '#FF00FF',
        #        'cyan' => '#00FFFF',
        #        'brown' => '#A52A2A',
        #        'gray' => '#808080',
        #        'grey' => '#808080',
        #        'black' => '#000000',
        #        'white' => '#FFFFFF',
        #        'lightblue' => '#ADD8E6',
        #        'lightgreen' => '#90EE90',
        #        'lightyellow' => '#FFFFE0',
        #        'darkblue' => '#00008B',
        #        'darkgreen' => '#006400',
        #        'darkred' => '#8B0000'
        #    ];
        #    $color = isset($colorMap[$annotation->color]) ? $colorMap[$annotation->color] : $annotation->color;
        #    
        #    // date属性とcreationdate属性を設定
        #    $timestamp = $parentComment->timestamp;
        #    $date = str_replace('Z', "+09'00'", date('YmdHis', strtotime($timestamp)));
        #    $date = 'D:' . $date;
        #    
        #    // コメントテキスト取得
        #    $commentText = $parentComment->text ? $parentComment->text : "";
        #    
        #    // 直接文字列としてXMLを構築（注：最後は</square>ではなく"で終わる）
        #    $result = "\"<square page=\"" . $annotation->page . "\" \n";
        #    $result .= "         rect=\"" . $rect . "\" \n";
        #    $result .= "         color=\"" . $color . "\" \n";
        #    $result .= "         title=\"" . $parentComment->author . "\" \n";
        #    $result .= "         subject=\"" . ucfirst($annotation->type) . "\" \n";
        #    $result .= "         date=\"" . $date . "\" \n";
        #    $result .= "         creationdate=\"" . $date . "\"><trn-custom-data bytes=\"{&quot;trn-mention&quot;:&quot;{\\&quot;contents\\&quot;:\\&quot;" . $commentText . "\\\\n\\&quot;,\\&quot;ids\\&quot;:[]}&quot;}\"/><contents>" . $commentText . "\n</contents><contents-richtext><body><p><span>" . $commentText . "\n</span></p></body></contents-richtext>\"";
        #    
        #    // 子コメントを処理
        #    foreach ($childComments as $childComment) {
        #        // 子コメントのタイムスタンプを処理
        #        $childTimestamp = $childComment->timestamp;
        #        $childDate = str_replace('Z', "+09'00'", date('YmdHis', strtotime($childTimestamp)));
        #        $childDate = 'D:' . $childDate;
        #        
        #        $childText = $childComment->text ? $childComment->text : "";
        #        
        #        // 子コメント用の文字列を追加（注：最後は</square>"で閉じる）
        #        $result .= "\n         creationdate=\"" . $childDate . "\"><trn-custom-data bytes=\"{&quot;trn-mention&quot;:&quot;{\\&quot;contents\\&quot;:\\&quot;" . $childText . "\\\\n\\&quot;,\\&quot;ids\\&quot;:[]}&quot;}\"/><contents>" . $childText . "\n</contents><contents-richtext><body><p><span>" . $childText . "\n</span></p></body></contents-richtext></square>\"";
        #    }
        #}
        #
        #return $result;

        #dummy
        $result = <<<XML
        "<square page=\"0\" 
                 rect=\"125.1,42.3,170.1,73.2\" 
                 color=\"#E44234\" 
                 title=\"Admin01 System\" 
                 subject=\"Rectangle\" 
                 date=\"D:20250316124254+09'00'\" 
                 creationdate=\"D:20250316124254+09'00'\"><trn-custom-data bytes=\"{&quot;trn-mention&quot;:&quot;{\\&quot;contents\\&quot;:\\&quot;comment1\\\\n\\&quot;,\\&quot;ids\\&quot;:[]}&quot;}\"/><contents>comment1\n</contents><contents-richtext><body><p><span>comment1\n</span></p></body></contents-richtext>\"
                 creationdate=\"D:20250316124256+09'00'\"><trn-custom-data bytes=\"{&quot;trn-mention&quot;:&quot;{\\&quot;contents\\&quot;:\\&quot;comment2\\\\n\\&quot;,\\&quot;ids\\&quot;:[]}&quot;}\"/><contents>comment2\n</contents><contents-richtext><body><p><span>comment2\n</span></p></body></contents-richtext></square>"
        XML;
        return $result;
    }
}
