<template>
  <v-snackbar
    v-if="$page.props.snackbar"
    v-model="$page.props.snackbar"
    :key="$page.props.snackbar.message"
    :color="$page.props.snackbar.color"
    multi-line
    timeout="1500"
    top
    app
  >
    <v-layout align-center pr-4>
      <v-icon class="pr-3" dark large>{{ $page.props.snackbar.icon }}</v-icon>
      <v-layout column>
        <div>
          <strong>{{ $page.props.snackbar.title }}</strong>
        </div>
        <div>{{ $page.props.snackbar.message }}</div>
      </v-layout>
    </v-layout>
  </v-snackbar>
</template>

<script>
export default {
  name: 'Snackbar',
};
</script>
