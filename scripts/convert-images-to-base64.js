#!/usr/bin/env node

/**
 * Image to Base64 Converter Script
 * 
 * This script converts character animation images to base64 format
 * and generates the embedded image data for the AIC Assistant.
 * 
 * Usage:
 * node scripts/convert-images-to-base64.js
 * 
 * Requirements:
 * - Node.js
 * - Images should be in public/images/kairu/ directory
 */

const fs = require('fs');
const path = require('path');

// Configuration
const IMAGE_BASE_PATH = path.join(__dirname, '../public/images/kairu');
const OUTPUT_FILE = path.join(__dirname, '../resources/js/Pages/Assistant/Data/EmbeddedImages.js');

// Character behaviors configuration (matches the Vue component)
const characterBehaviors = {
  idle: {
    folders: [
      { name: 'idle_01', startFrame: 63, frameCount: 20 }
    ]
  },
  thinking: {
    folders: [
      { name: 'thinking', startFrame: 82, frameCount: 22 }
    ]
  },
  getAttention: {
    folders: [
      { name: 'get_attention', startFrame: 34, frameCount: 29 }
    ]
  },
  goodbye: {
    folders: [
      { name: 'goodbye_01', startFrame: 343, frameCount: 19 }
    ]
  },
  writing: {
    folders: [
      { name: 'writing', startFrame: 0, frameCount: 21 }
    ]
  },
  congratulate: {
    folders: [
      { name: 'congratulate_01', startFrame: 0, frameCount: 34 }
    ]
  },
  congratulate2: {
    folders: [
      { name: 'congratulate_02', startFrame: 775, frameCount: 21 }
    ]
  },
  idle09: {
    folders: [
      { name: 'idle_09', startFrame: 276, frameCount: 19 }
    ]
  },
  idle10: {
    folders: [
      { name: 'idle_10', startFrame: 295, frameCount: 20 }
    ]
  },
  idle02: {
    folders: [
      { name: 'idle_02', startFrame: 83, frameCount: 39 }
    ]
  },
  idle03: {
    folders: [
      { name: 'idle_03', startFrame: 122, frameCount: 28 }
    ]
  }
};

/**
 * Convert image file to base64 data URL
 */
function imageToBase64(imagePath) {
  try {
    if (!fs.existsSync(imagePath)) {
      console.warn(` Image not found: ${imagePath}`);
      return null;
    }

    const imageBuffer = fs.readFileSync(imagePath);
    const base64String = imageBuffer.toString('base64');
    const mimeType = path.extname(imagePath).toLowerCase() === '.png' ? 'image/png' : 'image/jpeg';

    return `data:${mimeType};base64,${base64String}`;
  } catch (error) {
    console.error(`Error converting ${imagePath}:`, error.message);
    return null;
  }
}

/**
 * Generate the embedded images JavaScript file
 */
function generateEmbeddedImagesFile() {
  console.log(' Starting image conversion...');

  let fileContent = `/**
 * Embedded Character Images
 * 
 * This file contains all character animation images as base64 data URLs
 * to eliminate individual HTTP requests. Images are organized by behavior
 * and frame number.
 * 
 * Auto-generated by scripts/convert-images-to-base64.js
 * Generated on: ${new Date().toISOString()}
 */

// Base64 image data map
const embeddedImageData = new Map();

`;

  let totalImages = 0;
  let convertedImages = 0;

  // Convert base idle image (0000.png)
  console.log('Converting base idle image...');
  const baseImagePath = path.join(IMAGE_BASE_PATH, '0000.png');
  const baseImageData = imageToBase64(baseImagePath);

  if (baseImageData) {
    fileContent += `// Base idle image (0000.png)\n`;
    fileContent += `embeddedImageData.set('0000.png', '${baseImageData}');\n\n`;
    convertedImages++;
  } else {
    fileContent += `// Base idle image (0000.png) - NOT FOUND\n`;
    fileContent += `embeddedImageData.set('0000.png', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');\n\n`;
  }
  totalImages++;

  // Convert behavior images
  Object.entries(characterBehaviors).forEach(([behaviorName, behavior]) => {
    behavior.folders.forEach(folder => {
      console.log(`Converting ${folder.name} (${folder.frameCount} frames)...`);

      fileContent += `// ${folder.name} behavior images (frames ${folder.startFrame}-${folder.startFrame + folder.frameCount - 1}, ${folder.frameCount} frames)\n`;

      for (let frame = 0; frame < folder.frameCount; frame++) {
        const actualFrameNumber = folder.startFrame + frame;
        const frameNumber = String(actualFrameNumber).padStart(4, '0');
        const cacheKey = `${folder.name}/${frameNumber}.png`;
        const imagePath = path.join(IMAGE_BASE_PATH, folder.name, `${frameNumber}.png`);

        const imageData = imageToBase64(imagePath);

        if (imageData) {
          fileContent += `embeddedImageData.set('${cacheKey}', '${imageData}');\n`;
          convertedImages++;
        } else {
          fileContent += `// embeddedImageData.set('${cacheKey}', 'NOT_FOUND');\n`;
        }
        totalImages++;
      }

      fileContent += '\n';
    });
  });

  // Add utility functions
  fileContent += `/**
 * Get embedded image data by cache key
 * @param {string} cacheKey - The cache key for the image
 * @returns {string|null} - Base64 data URL or null if not found
 */
export function getEmbeddedImageData(cacheKey) {
  return embeddedImageData.get(cacheKey) || null;
}

/**
 * Get all embedded image keys
 * @returns {Array<string>} - Array of all cache keys
 */
export function getAllEmbeddedImageKeys() {
  return Array.from(embeddedImageData.keys());
}

/**
 * Get total number of embedded images
 * @returns {number} - Total count of embedded images
 */
export function getEmbeddedImageCount() {
  return embeddedImageData.size;
}

export default embeddedImageData;`;

  // Write the file
  try {
    fs.writeFileSync(OUTPUT_FILE, fileContent);
    console.log(`Generated embedded images file: ${OUTPUT_FILE}`);
    console.log(`Converted ${convertedImages}/${totalImages} images`);

    if (convertedImages < totalImages) {
      console.warn(`${totalImages - convertedImages} images were not found and will use placeholder data`);
    }
  } catch (error) {
    console.error('Error writing output file:', error.message);
    process.exit(1);
  }
}

// Check if image directory exists
if (!fs.existsSync(IMAGE_BASE_PATH)) {
  console.error(`Image directory not found: ${IMAGE_BASE_PATH}`);
  console.log('Please ensure the character images are in the correct directory.');
  process.exit(1);
}

// Run the conversion
generateEmbeddedImagesFile();
console.log(' Image conversion completed!');
