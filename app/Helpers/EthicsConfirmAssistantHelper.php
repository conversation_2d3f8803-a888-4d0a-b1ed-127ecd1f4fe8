<?php

namespace App\Helpers;

use App\Models\Proof;
use Illuminate\Support\Str;

// 画像アラートAI用質問データヘルパークラス
class EthicsConfirmAssistantHelper
{
    /**
     * 不自然な文章チェッカー結果を保存するS3場所
     */
    public static function unnaturalTextCheckCacheFilename(Proof $proof): string
    {
        $dir = config('filesystems.disks.s3.unnatural_text_check_response_dir');

        return Str::of($dir)
            ->append("/{$proof->uuid}")
            ->append("/{$proof->id}.json");
    }

    /**
     * @param Proof $proof,
     * @param string $baseName ファイル名(xxxx.pdf)
     * @param string $startPath ファイルの保存先ディレクトリ名
     */
    public static function getRequestFormFilePath(Proof $proof, string $baseName, string $startPath = '受付'): string
    {
        return ProofFileManager::mergePaths(
            config('filesystems.disks.s3.proof_path'),
            $proof->uuid,
            "/{$startPath}",
            $baseName
        );
    }
}