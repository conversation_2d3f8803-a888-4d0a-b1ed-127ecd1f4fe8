<?php

namespace App\Actions\Proof;

use App\Data\Admin\Proof\ProofReservationUpsertData;
use App\Models\ProofReservationLimit;

class UpsertProofReservationAction
{
    public function execute(ProofReservationUpsertData $data): ProofReservationLimit
    {
        return ProofReservationLimit::updateOrCreate([
            'date' => $data->date->format('Y-m-d'),
        ], [
            'limit' => $data->limit
        ]);
    }
}
