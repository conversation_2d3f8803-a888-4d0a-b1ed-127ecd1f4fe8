<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'last_name' => ['required', 'string', 'max:255'],
            'first_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8'],
            'user_type_id' => ['required', 'exists:user_types,id'],
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    protected function create(array $data)
    {
        return User::create([
            'last_name' => $data['last_name'],
            'first_name' => $data['first_name'],
            'email' => $data['email'],
            'password' => bcrypt($data['password']),
            'user_type_id' => $data['user_type_id'],
        ]);
    }

    public function register()
    {
        try {
            $this->validator(request()->all())->validate();
        
            event(new Registered($user = $this->create(request()->all())));

            if ($response = $this->registered(request(), $user)) {
                return $response;
            }

            return redirect()->route('admin.users.index', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => $user->name . 'さんのアカウントが生成しました。'
                             ]);
        } catch (\Throwable $th) {
            return redirect()->back()
                             ->with([
                                'snackbar' =>  [
                                    'color' => 'error',
                                    'icon' => 'mdi-alert-circle',
                                    'title' => '失敗',
                                    'message' => 'エラーが発生しました。'
                                ],
                                'errors' => $th->errors(),
                            ]);
        }
    }

    public function registered(Request $request, User $user)
    {
        return (new ResetPasswordController())->sendEmail($user);
    }
}
