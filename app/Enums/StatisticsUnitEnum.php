<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static General()
 * @method static static Legal()
 */
final class StatisticsUnitEnum extends Enum
{
    const Day       =   'days';
    const Month     =   'months';
    const Year      =   'years';

    public static function getDescription($value): string
    {
        return match ($value) {
            self::Day       => '日',
            self::Month     => '月',
            self::Year      => '年',
            default => parent::getDescription($value)
        };
    }

    public function toArray()
    {
        return $this;
    }

    public function dateformat(): string
    {
        return match ($this->value) {
            self::Day    => 'Y-m-d',
            self::Month  => 'Y-m',
            self::Year   => 'Y'
        };
    }
}
