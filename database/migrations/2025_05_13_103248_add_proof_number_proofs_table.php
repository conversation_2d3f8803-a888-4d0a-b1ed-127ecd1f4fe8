<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProofNumberProofsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::table('proofs', function (Blueprint $table) {
            //
            $table->string('proof_number')->default(null)->nullable()->after('id')->comment('申請番号');

            // proof_numberのユニーク制約を追加
            $table->unique('proof_number', 'proof_number_unique')->comment('申請番号のユニーク制約');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
        Schema::table('proofs', function (Blueprint $table) {
            //
            // proof_numberのユニーク制約を削除
            $table->dropUnique('proof_number_unique');
            // proof_numberカラムを削除
            $table->dropColumn('proof_number');
        });
    }
}
