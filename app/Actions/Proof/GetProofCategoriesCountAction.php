<?php

namespace App\Actions\Proof;

use App\Data\ProofCategoryCountData;
use App\Models\Category;
use App\Models\Proof;
use App\Models\ProofsCategories;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class GetProofCategoriesCountAction
{
    /** @var Collection<ProofCategoryCountData> */
    private Collection $values;

    /**
     * @param array<string, Closure>|null $filters
     * @param string $dateFormat
     * @param Closure|null $then
     *
     */
    public function execute(?array $filters = null, string $dateFormat = ''): self
    {
        $tables = [
            'p' => Proof::table(),
            'bc' => Category::table(),
            'pivot' => ProofsCategories::table()
        ];

        $this->values = ProofsCategories::query()
                ->join($tables['p'], "{$tables['p']}.id", '=', "{$tables['pivot']}.proof_id")
                ->join($tables['bc'], "{$tables['bc']}.id", '=', "{$tables['pivot']}.category_id")
                ->select([
                    "{$tables['bc']}.name as name",
                    DB::raw("DATE_FORMAT({$tables['p']}.created_at, '{$dateFormat}') as `date`"),
                    DB::raw('COUNT(*) as `count`')
                ])
                ->when($filters, function (Builder $query) use ($filters) {
                    foreach ($filters as $key => $closure) {
                        $query->whereHas($key, $closure);
                    }
                })
                ->groupBy('name', 'date')
                ->get()
                ->map(fn ($output) => ProofCategoryCountData::from([
                    'name' => $output->name,
                    'count' => $output->count,
                    'date' => $this->parseDate($output->date, $dateFormat)
                ]));

        return $this;
    }

    public function thenReturn(): Collection
    {
        return $this->values;
    }

    public function then(Closure $then): mixed
    {
        return $then($this->values);
    }

    private function parseDate(?string $date, string $dateFormat): array
    {
        [$year, $month, $day] = (function ($date, $dateFormat) {
            $initValues = collect([null, null, null]);

            // dateFormatが空の場合は初期値を返す
            if(empty($dateFormat)) {
                return $initValues->toArray();
            }

            $splitedFormat = Str::of($dateFormat)->replace('%', '')->explode('-');
            $splitedDate = Str::of($date)->explode('-')->map(fn ($value) => (int) $value);

            return $initValues
                ->zip($splitedFormat, $splitedDate)
                ->map(function ($value) {
                    [$initValue, $format, $date] = $value;

                    return match ($format) {
                        'Y', 'm', 'd' => $date,
                        default => $initValue
                    };
                })
                ->toArray();
        })($date, $dateFormat);

        return [
            'year' => $year,
            'month' => $month,
            'day' => $day
        ];
    }
}
