<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\ProofFile;
use Exception;
use Illuminate\Support\Facades\DB;

class AnnotationController extends Controller
{
    public function index(ProofFile $proofFile)
    {
        return response()->json([
            'annotations' => $proofFile->annotations
        ]);
    }
    
    
    public function store(ProofFile $proofFile)
    {
        $annotation = request()->all();
        
        if (is_null($annotation['bodyValue'])) {
            $annotation['bodyValue'] = '';
        }
        
        DB::beginTransaction();
        try {
            if ($this->hasAnnotation($proofFile->annotations, $annotation)) {
                throw new Exception("Invalid value of Annotations {$annotation['id']} is duplicate", 400);
            }

            $proofFile->annotations = collect($proofFile->annotations)
                                        ->merge([$annotation])
                                        ->toArray();
        } catch (\Throwable $th) {
            DB::rollBack();
            return;
        }

        $proofFile->save();

        DB::commit();

        return response()->json([
            'annotation' => $annotation
        ]);
    }

    public function update(ProofFile $proofFile)
    {
        $annotation = request()->all();
        
        if (is_null($annotation['bodyValue'])) {
            $annotation['bodyValue'] = '';
        }

        DB::beginTransaction();
        try {
            $index = collect($proofFile->annotations)
                    ->search(fn ($antt) => $antt['id'] == $annotation['id']);

            if ($index === false) {
                throw new Exception("Invalid value of Annotations {$annotation['id']} is not found", 400);
            }

            
            $proofFile->annotations = collect($proofFile->annotations)
                                        ->replace([$index => $annotation])
                                        ->toArray();
        } catch (\Throwable $th) {
            DB::rollBack();
            return;
        }
        
        $proofFile->save();

        DB::commit();

        return response()->json([
            'annotation' => $annotation
        ]);
    }

    public function destroy(ProofFile $proofFile)
    {
        $annotation = request()->all();

        DB::beginTransaction();
        try {
            $proofFile->annotations = collect($proofFile->annotations)
                                        ->reject(
                                            fn ($antt) =>
                                            $antt['id'] == $annotation['id'] ||
                                            $antt['target']['source'] == $annotation['id']
                                        )
                                        ->values();
        } catch (\Throwable $th) {
            DB::rollBack();
        }

        $proofFile->save();

        DB::commit();

        return response()->json([
            'annotation' => $annotation
        ]);
    }

    private function hasAnnotation($annotations, $annotation)
    {
        return collect($annotations)
            ->pluck('id')
            ->contains($annotation['id']);
    }
}
