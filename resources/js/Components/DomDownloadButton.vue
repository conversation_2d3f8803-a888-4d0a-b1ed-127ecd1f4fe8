<template>
  <v-tooltip left :color="status.color">
    <template v-slot:activator="{ on, attrs }">
      <v-btn
        icon
        :loading="loading"
        v-bind="attrs"
        v-on="on"
        :class="btnClass"
        @click="handleDownload"
        :color="color"
      >
        <v-icon>
          mdi-download
        </v-icon>
      </v-btn>
    </template>
    <span>{{ status.message }}</span>
  </v-tooltip>
</template>

<script>
import domtoimage from 'dom-to-image';

const Status = {
  init: {
    message: 'ダウンロード',
    color: 'gray',
  },
  success: {
    message: 'ダウンロードしました',
    color: 'success',
  },
  error: {
    message: 'ダウンロードに失敗しました',
    color: 'red',
  },
};

export default {
  name: 'DomDownloadButton',
  props: {
    domId: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      required: false,
      default: '#757575',
    },
    scale: {
      type: Number,
      required: false,
      default: 1,
    },
    top: {
      type: Boolean,
      required: false,
      default: false,
    },
    right: {
      type: Boolean,
      required: false,
      default: false,
    },
    bottom: {
      type: Boolean,
      required: false,
      default: false,
    },
    left: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      status: Status.init,
      btnClass: {
        top: this.top,
        right: this.right,
        bottom: this.bottom,
        left: this.left,
      },
    };
  },
  methods: {
    changeToSuccess() {
      this.loading = false;
      this.status = Status.success;
    },
    changeToError() {
      this.loading = false;
      this.status = Status.error;
    },
    changeToInit() {
      this.loading = false;
      this.status = Status.init;
    },
    download(blob) {
      const url = URL.createObjectURL(blob);

      const aTag = document.createElement('a');

      aTag.href = url;
      aTag.download = 'calendar.png';
      aTag.click();
    },
    handleDownload() {
      const initAfterAt = 2000; //  ms

      const dom = document.getElementById(this.domId);

      if (dom) {
        this.loading = true;

        domtoimage
          .toBlob(dom, {
            bgcolor: 'white',
            width: dom.clientWidth * this.scale,
            height: dom.clientHeight * this.scale,
            style: {
              transform: `scale(${this.scale})`,
              transformOrigin: 'top left',
            },
          })
          .then(this.download)
          .then(this.changeToSuccess)
          .catch(this.changeToError)
          .finally(() => setTimeout(this.changeToInit, initAfterAt));
      } else {
        alert('DOMが見つかりません');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.bottom {
  position: absolute;
  bottom: 0;
  padding-bottom: 8px;
}

.left {
  position: absolute;
  left: 0;
  padding-left: 8px;
}

.top {
  position: absolute;
  top: 0;
  padding-top: 8px;
}
.right {
  position: absolute;
  right: 0;
  padding-right: 8px;
}
</style>
