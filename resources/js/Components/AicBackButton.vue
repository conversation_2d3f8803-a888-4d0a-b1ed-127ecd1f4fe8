<template>
  <v-btn class="back-button mt-2 mb-4 mx-4" color="rgba(96,96,96,1)" dark @click="back">
    <v-icon left dark>mdi-arrow-left</v-icon>
    {{ text }}
  </v-btn>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      default: '戻る',
    },
  },
  methods: {
    back() {
      const url = this.$page.props.urlPrev;

      new URL(url).pathname == '/app/app-key'
        ? history.back()
        : this.$inertia.visit(url);
    },
  },
};
</script>

<style scoped>
.back-button {
  font-size: 14px;
  font-weight: bold;
  height: 40px !important;
  width: 120px;
  z-index: 1;
}
</style>
