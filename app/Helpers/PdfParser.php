<?php

namespace App\Helpers;

use League\Flysystem\AwsS3v3\AwsS3Adapter;
use League\Flysystem\Filesystem;
use Aws\S3\S3Client;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Spatie\PdfToText\Pdf as pdfToText;      // PDFファイルの本文テキスト抽出用
use Smalot\PdfParser\Parser;   // PDFファイルの他の情報取得
use Smalot\PdfParser\Document; // PDFファイルの他の情報取得
use Smalot\PdfParser\XObject\Image as PdfParserImage;
use Imagick;

class PdfParser
{
    const FORMAT_TYPE_PNG = "png";

    /**
     * S3 Bucket & Key
     */
    public string $bucket;
    public string $key;

    /**
     * tempファイル用
     */
    public $tempFileStorage;
    public string $tempFileName;
    public string $tempFilePath;
    public Document $pdf;

    public function __construct(string $bucket, string $key)
    {
        $this->bucket = $bucket;
        $this->key = $key;

        $this->tempFileStorage = Storage::disk('temp');
        $this->tempFileName = Str::uuid()->toString();
        $this->tempFilePath = $this->tempFileStorage->path($this->tempFileName);
    }

    public function parse()
    {
        $this->downloadPDF($this->bucket, $this->key);
        $this->pdf = (new Parser())->parseFile($this->tempFilePath);

        $info = [
            "title" => $this->getTitle(),
            "content" => $this->extractText(),
            "category" => $this->getCategory(),
            "keywords" => $this->getKeywords(),
            "annotations" => $this->getAnnotations(),
            "annotation_authors" => $this->getAnnotationAuthors(),
            "page_count" => $this->getPageCount(),
            "created" => $this->getCreated(),
            "thumbnail" => $this->getThumbnail(),
            "bucket" => $this->bucket,
            "key" => $this->key,
        ];

        $this->deletePDF($this->tempFileName);

        return $info;
    }

    public function parseForImage()
    {
        $this->downloadPDF($this->bucket, $this->key);

        try {
            // TODO:一部のpdfで502エラーが発生する原因を調査すること
            $parser = new Parser();
            $this->pdf = $parser->parseFile($this->tempFilePath);
        } catch (Exception $e) {
            Log::debug($e->getMessage());
            throw $e;
        }

        $info = [
            "title" => $this->getTitle(),
            "content" => $this->extractText(),
            "category" => $this->getCategory(),
            "keywords" => $this->getKeywords(),
            "annotations" => $this->getAnnotations(),
            "annotation_authors" => $this->getAnnotationAuthors(),
            "page_count" => $this->getPageCount(),
            "created" => $this->getCreated(),
            "thumbnail" => $this->getThumbnail(),
            "bucket" => $this->bucket,
            "key" => $this->key,
            "images" => $this->getImages(),
        ];

        $this->deletePDF($this->tempFileName);

        return $info;
    }
    
    // ページ毎に解析する
    public function parseByPage()
    {
        $this->downloadPDF($this->bucket, $this->key);

        try {
            // TODO:一部のpdfで502エラーが発生する原因を調査すること
            $parser = new Parser();
            $this->pdf = $parser->parseFile($this->tempFilePath);
        } catch (Exception $e) {
            Log::debug($e->getMessage());
            throw $e;
        }
        
        $pageCount = $this->getPageCount();

        $contents = array();
        for ($i = 0; $i < $pageCount; $i++) {
            $text = $this->extractTextByPage($i + 1);
            array_push($contents, $text);
        }

        $info = [
            "title" => $this->getTitle(),
            "content" => $contents,
            "category" => $this->getCategory(),
            "keywords" => $this->getKeywords(),
            "annotations" => $this->getAnnotations(),
            "annotation_authors" => $this->getAnnotationAuthors(),
            "page_count" => $pageCount,
            "created" => $this->getCreated(),
            "thumbnail" => $this->getThumbnail(),
            "bucket" => $this->bucket,
            "key" => $this->key,
        ];

        $this->deletePDF($this->tempFileName);

        return $info;
    }
    
    private function downloadPDF(string $bucket, string $key)
    {
        $fileSystem = $this->initFileSystem($bucket);
        $contents = $fileSystem->readStream($key);

        $this->tempFileStorage->put($this->tempFileName, $contents);
    }

    private function deletePDF($fileName)
    {
        $this->tempFileStorage->delete($fileName);
    }

    public function extractText()
    {
        try {
            $text = (new pdfToText())
                    ->setPdf($this->tempFilePath)
                    ->text();

            $text = $this->preprocessText($text);

            return $text;
        } catch (\Throwable $th) {
            return '';
        }
    }

    // ページ毎にテキストを取得する
    public function extractTextByPage($page)
    {
        try {
            $text = (new pdfToText())
                    ->setPdf($this->tempFilePath)
                    ->setOptions(["f {$page}", "l {$page}"])
                    ->text();

            $text = $this->preprocessText($text);
            $text = $this->eraseTcpdText($text);

            return $text;
        } catch (\Throwable $th) {
            return '';
        }
    }

    // テキスト末尾のTCPDFライセンスを消す
    public function eraseTcpdText(string $text)
    {
        return preg_replace("/Powered[ ]*by[ ]*TCPDF[ ]*\(www\.tcpdf\.org\)$/", "", $text);
    }
    
    private function getTitle()
    {
        try {
            $splited = explode('/', $this->key);

            return last($splited);
        } catch (\Throwable $th) {
            return '';
        }
    }

    private function getCategory()
    {
        try {
            return Arr::get($this->pdf->getDetails(), 'Subject', '');
        } catch (\Throwable $th) {
            return '';
        }
    }

    private function getKeywords()
    {
        $delim = ",";

        try {
            $keywordString = Arr::get($this->pdf->getDetails(), 'Keywords', '');

            return $keywordString ? explode($delim, $keywordString) : [];
        } catch (\Throwable $th) {
            return [];
        }
    }

    private function getAnnotations()
    {
        try {
            $annotations = $this->pdf->getObjectsByType('Annot');

            return collect($annotations)
                    ->filter(fn ($obj) => $obj->has('Contents'))
                    ->map(fn ($obj) => $obj->get('Contents')->__toString())
                    ->map(fn ($annotation) => $this->preprocessText($annotation))
                    // ->map(fn ($annotation) => $this->mb_convert_encoding($annotation, 'UTF-8'))
                    // ->map(fn ($annotation) => preg_replace('/\x02/', "", $annotation))
                    ->values()
                    ->toArray();
        } catch (\Throwable $th) {
            return [];
        }
    }

    private function getAnnotationAuthors()
    {
        try {
            $annotations = $this->pdf->getObjectsByType('Annot', 'FreeText');

            return collect($annotations)
                    ->filter(fn ($obj) => $obj->has('T'))
                    ->map(fn ($obj) => $obj->get('T')->__toString())
                    ->map(fn ($annotation) => $this->preprocessText($annotation))
                    ->unique()
                    ->values()
                    ->toArray();
        } catch (\Throwable $th) {
            return [];
        }
    }

    private function getPageCount()
    {
        try {
            return Arr::get($this->pdf->getDetails(), 'Pages', 0);
        } catch (\Throwable $th) {
            return 0;
        }
    }

    private function getCreated()
    {
        try {
            $created = Arr::get($this->pdf->getDetails(), 'CreationDate', null);
            if (is_null($created)) {
                return $created;
            }

            return Carbon::parse($created)->format("Y-m-d\TH:m:s\Z");
        } catch (\Throwable $th) {
            return null;
        }
    }

    private function getThumbnail()
    {
        try {
            $im = new \Imagick();
            $im->readImage($this->tempFilePath . '[0]'); // [0] => First page
            $im->setImageFormat(self::FORMAT_TYPE_PNG);
            $im->resizeImage(150, 200, 1, 0); // width: 150, height 200

            return base64_encode($im->getImageBlob());
        } catch (\Throwable $th) {
            return '';
        }
    }

    /*
     * 画像オブジェクトから画像フォーマットを取得する
     */
    public function getImageFormat(PdfParserImage $image_obj) {
        $type = $image_obj->getHeader()->getDetails();
        if ($type['Subtype'] !== 'Image') {
            return null;
        }

        if (!array_key_exists("Filter", $type)) {
            return null;
        }

        if ($type['Filter'] === 'DCTDecode') {
            return "jpg";
        }
        else if ($type['Filter'] === 'FlateDecode') {
            // 実際にはpng以外も含まれているようなので、現在調査中
            return "png";
        }
        else {
            return null;
        }
    }

    private function getImages()
    {
        try {
            $objects = $this->pdf->getObjects();
            $images = [];
            foreach ($objects  as $obj) {
                if (!($obj instanceof PdfParserImage)) {
                    continue;
                }
                
                // TODO: 画像タイプによって追加するか判断すること
                $format = $this->getImageFormat($obj);
                $is_add = false;
                switch ($format) {
                case "jpg":
                    $is_add = true;
                    break;
                }

                if ($is_add) {
                    array_push($images, $obj);
                }
            }
            
            return $images;
        } catch (\Throwable $th) {
            return [];
        }
    }

    private function initFileSystem(string $bucket): Filesystem
    {
        $client = new S3Client([
            'credentials' => [
                'key'    => env('AWS_ACCESS_KEY_ID'),
                'secret' => env('AWS_SECRET_ACCESS_KEY'),
            ],
            'region' => env('AWS_DEFAULT_REGION'),
            'version' => 'latest',
        ]);

        $adapter = new AwsS3Adapter($client, $bucket);

        return new Filesystem($adapter);
    }

    private function preprocessText(string $text)
    {
        $badChar = [
            chr(0), chr(1), chr(2), chr(3), chr(4), chr(5), chr(6), chr(7), chr(8), chr(9), chr(10),
            chr(11), chr(12), chr(13), chr(14), chr(15), chr(16), chr(17), chr(18), chr(19), chr(20),
            chr(21), chr(22), chr(23), chr(24), chr(25), chr(26), chr(27), chr(28), chr(29), chr(30),
            chr(31),
            // non-printing characters
            chr(127)
        ];

        $text = mb_convert_encoding($text, 'UTF-8');
        $text = str_replace($badChar, '', $text);
        $text = preg_replace('/[\f]/', "", $text);

        return $text;
    }
}
