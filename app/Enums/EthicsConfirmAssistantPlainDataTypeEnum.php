<?php

namespace App\Enums;

use App\Data\EthicsConfirmAssistantPlainImageData;
use App\Data\EthicsConfirmAssistantPlainTextData;
use BenSampo\Enum\Enum;

/**
 * @method static static Text()
 * @method static static Image()
 */
final class EthicsConfirmAssistantPlainDataTypeEnum extends Enum
{
    public const Text = 'text';
    public const Image = 'image';

    public function convertApiInputData(string $value, string $description)
    {
        $type = $this->value;

        return match ($type) {
            self::Text => new EthicsConfirmAssistantPlainTextData($this, $value, $description),
            self::Image => new EthicsConfirmAssistantPlainImageData($this, $value, $description),
        };
    }
}
