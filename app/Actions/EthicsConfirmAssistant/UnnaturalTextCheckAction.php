<?php

namespace App\Actions\EthicsConfirmAssistant;

use App\Data\RequestFormPublishDateData;
use App\Http\API\EthicsConfirmAssistant;
use App\Models\Proof;
use Exception;
use Illuminate\Support\Facades\Storage;

class UnnaturalTextCheckAction
{
    public function __construct(
        public EthicsConfirmAssistant $api
    ) {
    }

    public function execute(
        Proof $proof,
        RequestFormPublishDateData $publishDate
    ): array {
        if($proofFile = $proof->notPlainFileChecked()->first()) {
            if (!Storage::disk('s3')->exists($proofFile->path)) {
                throw new Exception("File not exist: {$proofFile->path}");
            }

            return $this->api->unnaturalTextCheck($proofFile, $publishDate)->json();
        }

        throw new Exception("Shared ProofFile not exist: {$proof->id}");
    }
}