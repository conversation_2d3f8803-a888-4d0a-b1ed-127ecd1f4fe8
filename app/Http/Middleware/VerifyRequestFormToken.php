<?php

namespace App\Http\Middleware;

use App\Actions\GenerateRequestFormTokenAction;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class VerifyRequestFormToken
{
    public function __construct(
        private GenerateRequestFormTokenAction $generateRequestFormTokenAction
    ) {
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next

     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        [$email, $token] = [$request->input('email'), $request->input('token')];

        if($this->isNotVerifiedInputs($email, $token)) {
            throw new BadRequestHttpException('Invalid Data');
        }

        if($this->isNotVerifiedToken($email, $token)) {
            throw new AccessDeniedHttpException('Invalid Token');
        }

        return $next($request);

    }

    private function isNotVerifiedInputs(?string $email, ?string $token): bool
    {
        $validator = Validator::make([
            'email' => $email,
            'token' => $token,
        ], [
            'email' => [
                'required',
                'string',
                'email',
            ],
            'token' => [
                'required',
                'string',
            ],
        ]);

        return $validator->fails();
    }

    private function isNotVerifiedToken(string $email, string $token): bool
    {
        return $this->generateRequestFormTokenAction->execute($email) !== $token;
    }
}
