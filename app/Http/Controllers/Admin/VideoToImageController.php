<?php

namespace App\Http\Controllers\Admin;

use App\Actions\ExtractAndUploadImagesFromS3VideoAction;
use App\Actions\UploadVideoAction;
use App\Enums\UplodableVideoTypeEnum;
use App\Enums\VideoToImageDownloadTypeEnum as DownloadTypeEnum;
use App\Enums\VideoToImageIntervalEnum as IntervalEnum;
use App\Enums\VideoToImageUploadTypeEnum as UploadTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\VideoToImageController\ExtractRequest;
use App\Http\Requests\Admin\VideoToImageController\ShowRequest;
use App\Models\ProofFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;

class VideoToImageController extends Controller
{
    public function index()
    {
        return Inertia::render("Admin/Util/VideoToImage/AicIndex", [
            'uplodable_video_types' => array_values(UplodableVideoTypeEnum::getInstances()),
            'upload_types' => array_values(UploadTypeEnum::getInstances()),
            'interval_types' => array_values(IntervalEnum::getInstances()),
            'extractable_videos' => ProofFile::whereVideos()
                                            ->latest()
                                            ->cursor()
                                            ->map->s3_video_format
                                            ->toArray(),
        ]);
    }

    public function show(ShowRequest $request)
    {
        $data = $request->data();

        return Inertia::render("Admin/Util/VideoToImage/AicShow", [
            "video_path" => $data->video_path,
            "images_dir" => $data->images_dir,
            "time" => $data->time,
            'interval' => $data->interval_type,
            'bucket' => $data->bucket,
        ]);
    }

    public function extracts(
        ExtractRequest $request,
        UploadVideoAction $upload,
        ExtractAndUploadImagesFromS3VideoAction $extract,
    ) {
        $start = microtime(true);

        try {
            $data = $request->data();

            $upload->execute($data->upload);
            $extract->execute($data->extract);
        } catch (\Throwable $th) {
            Log::error($th->getMessage());

            return redirect()->back()->with([
                'snackbar' =>  [
                    'color' => 'error',
                    'icon' => 'mdi-alert-circle',
                    'title' => '失敗',
                    'message' => $th->getMessage()
                ],
            ]);
        }

        $end = microtime(true);

        return redirect()->route('admin.utils.video-to-image.show', [
            'time' => number_format(floatval($end) - floatval($start), 2),
            'video_path' => $data->extract->video,
            'images_dir' => $data->extract->toDir,
            'interval_type' => $data->extract->interval_type->value,
            'bucket' => config('filesystems.disks.s3.bucket'),
        ]);
    }
}
