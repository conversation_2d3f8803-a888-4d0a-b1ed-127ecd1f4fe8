<?php

namespace App\Helpers;

use Closure;
use Illuminate\Support\Collection;
use SimpleXMLElement;

class XmlHelper
{
    /**
     * XML map
     *
     * @param SimpleXMLElement $xml
     * @param Closure $callback
     * @return array
     */
    public static function map(SimpleXMLElement $iter, Closure $iteratee): array
    {
        return  collect((array)$iter)
                ->flatten()
                ->map(fn ($value) => $iteratee($value))
                ->toArray();
    }

    public static function find(
        array|SimpleXMLElement $iter,
        Closure $predicate,
    ): SimpleXMLElement|null {
        return collect((array)$iter)
                ->flatten()
                ->when(is_array($iter), function (Collection $iter) {
                    return $iter->map(
                        fn (string $value) => simplexml_load_string($value)
                    );
                })
                ->first(
                    fn (SimpleXMLElement $value) => $predicate($value)
                );
    }

    public static function getName(string|SimpleXMLElement $value): string|null
    {
        $value = is_string($value) ? simplexml_load_string($value) : $value;

        return $value['name']?->__toString();
    }

    public static function equalName(
        string|SimpleXMLElement $value,
        string|SimpleXMLElement $other
    ): bool {
        $value = self::getName($value);
        $other = self::getName($other);

        return
            is_string($value) &&
            is_string($other) &&
            $value === $other;
    }
}
