<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static General()
 * @method static static Legal()
 */
final class VideoToImageIntervalEnum extends Enum
{
    const EVERY_QUARTER_SEC   =   0.25;
    const EVERY_HALF_SEC      =   0.5;
    const EVERY_1_SEC         =   1.0;
    const EVERY_3_SEC         =   3.0;
    const EVERY_5_SEC         =   5.0;

    public static function getDescription($value): string
    {
        return $value . '秒';
    }

    public function toArray()
    {
        return $this;
    }
}
