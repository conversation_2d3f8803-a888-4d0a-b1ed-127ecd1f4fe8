<template>
  <div>
    <v-switch
      label="自分が未チェック"
      hide-details
      dense
      :input-value="this.filter.unCheckedOnly"
      @change="change"
    />
  </div>
</template>

<script>
import { filterSerialize as serialize } from '../../lib/utils';

export default {
  name: 'NotMyCompletionProof',
  props: ['filter', 'target'],
  methods: {
    reset() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: false,
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },
    submit() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: true,
        },
        page: 1,
      };

      newFilter.checkedOnly = false;

      console.log(newFilter);
      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },
    change(active) {
      active ? this.submit() : this.reset();
    },
  },
};
</script>