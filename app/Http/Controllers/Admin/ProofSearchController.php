<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ProofCloudSearchManager;
use App\Helpers\ProofFileManager;
use App\Http\Controllers\Controller;
use App\Models\ProofCloudSearch;
use App\Models\ProofFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Inertia\Inertia;

class ProofSearchController extends Controller
{
    protected $perPage;

    public function __construct()
    {
        $this->perPage = config('awscloudsearch.search.options.size');
    }

    /**
     * PDF検索リスト
     */
    public function index()
    {
        $query = request()->q;
        $page = request()->page;
        $filterQuery = request()->except(['q', 'page', '_token']);

        $response = ProofCloudSearchManager::search($query, $page, $filterQuery);

        $found = Arr::get($response, 'hits.found', 0);
        $facets = $this->filterFacets(Arr::get($response, 'facets', []));

        $items = Arr::get($response, 'hits.hit', []);
        $items = collect($items)->map(fn ($hit) => $this->putThumbnail($hit));

        $paginator = $this->paginate($items, $this->perPage, $found, $page)
                          ->appends(request()->except(['page', '_token']));

        return Inertia::render('Admin/Proof/Search/Index', [
            "query" => $query,
            'paginator' => $paginator,
            'found' => $found,
            "facets" => $facets
        ]);
    }

    /**
     * PDFビュアー
     */
    public function viewer()
    {
        $proofFile = ProofFile::ofCloudSearchId(request()->id)->first();

        return Inertia::render(
            ProofFileManager::ViewerComponent($proofFile),
            [
                'user' => request()->user(),
                'proofFile' => $proofFile,
                'isReadOnly' => true
            ]
        );
    }

    /**
     * PDF検索サジェスト
     */
    public function suggest()
    {
        $query = request()->q;

        $response = ProofCloudSearchManager::suggest($query);

        $suggestions = Arr::get($response, 'suggest.suggestions', []);
        $suggestions = collect($suggestions)
                        ->map(fn ($suggestion) => $suggestion['suggestion'])
                        ->toArray();

        return $suggestions;
    }

    /**
     * データがあるファセットのみ取得
     *
     * @param  array $facets
     * @return Collection
     */
    private function filterFacets(array $facets)
    {
        return collect($facets)
                ->filter(fn ($facet) => !empty($facet['buckets']));
    }

    /**
     * DBからサムネを取得してarrayに格納
     *
     * @param  array $hit
     * @return array
     */
    private function putThumbnail(array $hit)
    {
        $thumbnail = '';

        if (array_key_exists('id', $hit)) {
            $model = ProofCloudSearch::ofCloudSearchId($hit['id'])->first();
            $thumbnail = $model->thumbnail ?? '';
        }

        $hit['thumbnail'] =  $thumbnail;

        return $hit;
    }

    /**
     * CloudSearch用カスタマイズページネーション
     *
     * @param  array|Collection $items
     * @param  int $perPage
     * @param  int $total
     * @param  int|null $page
     * @param  array $options
     * @return LengthAwarePaginator
     */
    private function paginate(array|Collection $items, int $perPage, int $total, int|null $page = null, array $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);

        return new LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $page,
            $options
        );
    }
}
