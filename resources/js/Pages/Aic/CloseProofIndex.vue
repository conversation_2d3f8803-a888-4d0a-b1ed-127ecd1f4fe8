<template>
  <div>
    <v-dialog v-model="drawer" persistent max-width="600px" class="reset-password-dialog" style="overflow-x: hidden !important;">
      <v-card>
        <v-card-text class="white--text py-4 mb-5" max-width="600px" style="width: -webkit-fill-available; background-color:rgb(76, 175, 80); ">
          <span class="closed-pdf-head">
            バンダイナムコアミューズメントと
            <br>
            バンダイナムコアミューズメントラボの皆様へ
          </span>
        </v-card-text>
        <v-card-text class="dialog-body" style="font-size: 16px !important; color: rgba(0,0,0,.87); overflow-x: hidden !important;" max-width="600px">
          <span>
            現在この機能は使用できません。
          </span>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="secondary" @click="dashboard()">
            ダッシュボードへ
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import NewLayout from '@/Layouts/ClosedPdfLayout';
import  AicTextCopyButton  from '@/Components/AicTextCopyButton.vue';
export default {
  name: 'ClosedPage',
  layout: NewLayout,
  props: {
  },
  components: {
    AicTextCopyButton,
  },
  data() {
    return {
      drawer: true,
      mailAddress: '<EMAIL>',
    };
  },
  methods: {
    windowsClose() {
      this.drawer = false;
      console.log('windowsClose');
      console.log(window.history.length);
      window.close();
    },
    dashboard() {
      this.$inertia.get(this.route(this.$page.props.user.type.value + '.dashboard.index'));
    },
  },
}
</script>

<style lang="scss">
.closed-pdf-head {
  font-size: 20px !important;
  background-color: rgb(76, 175, 80) !important;
  font-weight: 400;
}

.dialog-body {
  font-size: 16px !important;
  display: flex;
  color: rgba(0,0,0,.87);
  width: -webkit-fill-available;
  overflow: hidden !important;
}
</style>