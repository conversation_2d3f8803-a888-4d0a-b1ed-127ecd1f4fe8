<template>
  <div>
    <v-switch
      label="自分がスペシャルサポーター"
      hide-details
      dense
      :input-value="this.filter.my_special_sup_only"
      @change="change"
      :disabled="isAdmin()"
    />
  </div>
</template>

<script>
import { filterSerialize as serialize } from '../../lib/utils';

export default {
  name: 'MySpecialSupporterProof',
  props: ['filter', 'target', 'user'],
  methods: {
    reset() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: false,
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },
    submit() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: true,
        },
        page: 1,
      };

      console.log("MySpecialSupporterProof submit");
      console.log(newFilter);
      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },
    change(active) {
      active ? this.submit() : this.reset();
    },
    isAdmin(){
      return this.user.user_type_id == 1 ? true : false;
    },
  },
};
</script>