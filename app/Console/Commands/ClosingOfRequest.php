<?php

namespace App\Console\Commands;

use App\Models\Holiday;
use Carbon\CarbonImmutable;
use Closure;
use Illuminate\Console\Command;
use Illuminate\Pipeline\Pipeline;

class ClosingOfRequest extends Command
{
    const HOLIDAY_NAME = '【自動】受付終了';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'closing-of-request {date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'ClosingOfRequest: 特定の時間に祝日設定を入れる';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $current = $this->convertCarbon($this->argument('date'));

        if(Holiday::hasHoliday($current)->doesntExist()) {
            $this->info("登録開始: 「{$current->format('Y-m-d')}」の受付終了を開始。");

            do {
                list($current, $alreadyExists) = $this->getOrCreateHoliday($current);

                // 祝日が既に登録済みの場合は、次の平日を探す
            } while ($alreadyExists);

            $this->info("登録終了: 「{$current->format('Y-m-d')}」に祝日を登録しました。");
        }

        return 0;
    }

    private function getOrCreateHoliday(CarbonImmutable $date)
    {
        $getNextDate = fn (CarbonImmutable $date, Closure $next) => $next($date->addWeekday());
        $getOrCreate = function (CarbonImmutable $nextDate) {
            $model = Holiday::firstOrCreate(
                ['date' => $nextDate],
                [
                    'name' => self::HOLIDAY_NAME,
                    'is_manual' => false
                ]
            );

            return [$nextDate, !$model->wasRecentlyCreated];
        };

        // Next Week Day
        return  app(Pipeline::class)
                ->send($date)
                ->through([
                    $getNextDate,
                    $getOrCreate
                ])
                ->thenReturn();
    }

    private function convertCarbon(string|CarbonImmutable $date): CarbonImmutable
    {
        if(is_string($date)) {
            try {
                $date = CarbonImmutable::parse($date);
            } catch (\Throwable $th) {
                $date =  CarbonImmutable::now();
            }
        }

        return $date->startOfDay();
    }
}
