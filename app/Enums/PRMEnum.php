<?php

namespace App\Enums;

use App\Http\API\PRM\GeneralPRM;
use App\Http\API\PRM\LegalPRM;
use App\Http\API\PRM\UnnaturalTextCheckerPRM;
use App\Http\API\PRM\ImageAlertPRM;
use BenSampo\Enum\Enum;

/**
 * @method static static General()
 * @method static static Legal()
 */
final class PRMEnum extends Enum
{
    public string $prm;

    const UnnaturalTextChecker = 'unnatural_text_checker';
    const ImageAlert = 'image_alert';
    const General  =   'general';
    const Legal    =   'legal';

    public function __construct(mixed $enumValue)
    {
        parent::__construct($enumValue);

        $this->prm = static::getAPI($enumValue);
    }

    public static function getDescription($value): string
    {
        return match ($value) {
            self::General => 'セット事例',
            self::Legal => '関連法律事例',
            self::UnnaturalTextChecker => 'テキストチェック',
            self::ImageAlert => '画像アラート',
            default => parent::getDescription($value)
        };
    }

    public static function getAPI(mixed $value): string
    {
        return match ($value) {
            self::General => GeneralPRM::class,
            self::Legal => LegalPRM::class,
            self::UnnaturalTextChecker => UnnaturalTextCheckerPRM::class,
            self::ImageAlert => ImageAlertPRM::class,
        };
    }

    public function toArray()
    {
        return $this;
    }
}
