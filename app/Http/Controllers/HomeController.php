<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class HomeController extends Controller
{
    public function __invoke()
    {

        // /download にアクセスした場合そのまま通す
        if( request()->route()->getName() == 'download' ){
            Log::info('is request download');
        }

        if(auth()->user()->type_value == 'viewer'){
            return redirect()->route('viewer.archive');
        }

        $userType = auth()->user()->type_value;
        if($userType == 'requester'){
            return redirect()->route("requestform.history");
        }else if($userType == 'admin' || $userType == 'member'){
            return redirect()->route("{$userType}.dashboard.index");
        }else{
            return abort(403);
        }
    }
}
