<?php

namespace App\Data;

use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use <PERSON><PERSON>\LaravelData\Data;

class UpdateProofFromAdminData extends Data
{
    public function __construct(
        /** @var array<int> */
        public array $ips,

        public int $categoryId,

        public Carbon $externalDeadlineAt,
        public Carbon $internalDeadlineAt,

        public array $comments,
    ) {}

    public static function fromRequest(Request $request)
    {
        return new self(
            ips: $request->input('ips', []),
            categoryId: $request->input('category_id', ''),
            externalDeadlineAt: Carbon::createFromTimeString($request->input('external_deadline_at')),
            internalDeadlineAt: Carbon::createFromTimeString($request->input('internal_deadline_at')),
            comments: $request->input('comments', []),
        );
    }
};
