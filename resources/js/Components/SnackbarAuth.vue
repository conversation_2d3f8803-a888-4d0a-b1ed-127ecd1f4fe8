<template>
  <v-snackbar
    v-model="show"
    :color="snackbar.color"
    multi-line
    timeout="1500"
    top
    app
  >
    <v-layout align-center pr-4>
      <v-icon class="pr-3" dark large>{{ snackbar.icon }}</v-icon>
      <v-layout column>
        <div>
          <strong>{{ snackbar.title }}</strong>
        </div>
        <div>{{ snackbar.message }}</div>
      </v-layout>
    </v-layout>
  </v-snackbar>
</template>

<script>
export default {
  name: 'Snackbar',
  props: ['snackbar'],
  data() {
    return {
      show: false,
    };
  },
  created() {
    if (this.snackbar) {
      this.show = true;
    }
  },
};
</script>
