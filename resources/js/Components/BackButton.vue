<template>
  <v-btn class="ma-2" color="grey darken-1" dark @click="back">
    <v-icon left dark>mdi-arrow-left</v-icon>
    {{ text }}
  </v-btn>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      default: '戻る',
    },
  },
  methods: {
    back() {
      const url = this.$page.props.urlPrev;

      new URL(url).pathname == '/app/app-key'
        ? history.back()
        : this.$inertia.visit(url);
    },
  },
};
</script>
