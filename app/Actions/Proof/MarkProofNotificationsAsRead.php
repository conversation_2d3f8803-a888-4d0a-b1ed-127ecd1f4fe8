<?php

namespace App\Actions\Proof;

use App\Models\Proof;
use App\Models\User;
use Illuminate\Support\Collection;

class MarkProofNotificationsAsRead
{
    public function execute(array|Collection $users, Proof $proof)
    {
        return collect($users)->each(fn (User $user) => $this->markAsRead($user, $proof));
    }

    private function markAsRead(User $user, Proof $proof)
    {
        $user
            ->unreadNotifications
            ->where('data.proof_id', $proof->id)
            ->each
            ->markAsRead();
    }
}
