<template>
  <v-dialog v-if="_dialog" v-model="_dialog" scrollable width="600">
    <v-card>
      <v-card-title>{{ file.name }}</v-card-title>
      <v-divider></v-divider>

      <v-card-text
        style="height: 450px; padding-top: 20px;"
        v-html="getErrorMessage(file)"
      />

      <v-divider></v-divider>
      <v-card-actions class="justify-end">
        <v-btn color="primary" variant="text" @click="_dialog = false">
          OK
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    file: {
      type: Object,
      required: true,
    },
    dialog: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    _dialog: {
      get() {
        return this.dialog;
      },
      set(value) {
        this.$emit('update:dialog', value);
      },
    },
  },
  methods: {
    getErrorMessage(file) {
      switch (file.extension) {
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
          return `
            <p>
                ${file.extension}ファイルが選択されています。<br/>
                PDFファイルに変換してアップロードしてください。
            </p>

            <p>ヒント</p>

            <ul>
                <li>画像をMicrosoft フォトなどで表示し「印刷」を実行します。</li>
                <li>「プリンター」を”Microsoft Print to PDF”とすることでPDFファイルとして保存することができます。</li>
            </ul>
        `;
        case 'ai':
          return `
            <p>
                ${file.extension}ファイルが選択されています。<br/>
                PDFファイルに変換してアップロードしてください。
            </p>

            <p>ヒント</p>

            <ul>
                <li>Illustratorで「別名で保存」を実行し、「ファイルの種類」で“Adobe PDF”を選択してください。</li>
                <li>Adobe PDF プリセットで”[最小ファイルサイズ]”を選択して「Illustratorの編集機能を保持」の”✓をはずして”の保存をお願いします。</li>
            </ul>
        `;
        case 'xlsx':
        case 'xls':
        case 'xltx':
        case 'csv':
        case 'xlsb':
        case 'ods':
        case 'xlsm':
        case 'pptx':
        case 'ppt':
        case 'ppsx':
        case 'pps':
        case 'potm':
        case 'pot':
        case 'potx':
        case 'odp':
        case 'ppsm':
        case 'pptm':
        case 'thmx':
        case 'docx':
        case 'doc':
        case 'docm':
        case 'dot':
        case 'dotm':
        case 'odt':
        case 'dotx':
          return `
            <p>
                ${file.extension}ファイルが選択されています。<br/>
                PDFファイルに変換してアップロードしてください。
            </p>

            <p>ヒント</p>

            <ul>
                <li>「名前を付けて保存」を実行し、ファイルの種類を”PDF(*.pdf)”として保存してください。</li>
            </ul>
        `;
        default:
          return file.error;
      }
    },
  },
};
</script>
