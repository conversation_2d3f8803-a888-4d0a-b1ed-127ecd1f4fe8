<?php

namespace App\Console\Commands;

use App\Models\Proof;
use App\Models\ProofNumber;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FillProofNumberBNAM extends Command
{
  /**
   * 申請番号追加のデプロイ時・ローカルにて申請番号の追加時１回のみ実行するコマンド
   */

  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'command:fill-proof-number-bnam';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'This Command fill proofs proof_number column all bnam';

  /**
   * Create a new command instance.
   *
   * @return void
   */
  public function __construct()
  {
    parent::__construct();
  }

  /**
   * Execute the console command.
   *
   * @return int
   */
  public function handle()
  {
    $this->info('start fill proof_number');
    $ids = $this->getProofIds();
    // idsをループさせ、Proofからid１つ毎にidが5桁未満なら0埋めをしてBNAM-00001の形にし、Proofのproof_numberに格納する
    DB::beginTransaction();
    try {
      foreach($ids as $id) {
        $proof = Proof::find($id);
        if ($proof) {
          $proof_number = $proof->id;
          if (strlen($proof_number) < 5) {
            $proof_number = str_pad($proof_number, 5, '0', STR_PAD_LEFT);
          }
          $proof->proof_number = 'BNAM-' . $proof_number;
          $proof->save();
        }
      }
      DB::commit();
    } catch (\Exception $e) {
      DB::rollBack();
      $this->error('Error: ' . $e->getMessage());
      return 1;
    }
    $this->info('end fill proof_number');
    return 0;
  }

  private function getProofIds()
  {
    // created_atが2025-03-31 23:59:59までのProofのidを取得する
    $proofs = Proof::where('created_at', '<=', '2025-03-31 23:59:59')
      ->where('proof_number', null)
      ->get();
    // 取得したProofのidを配列で返す
    return $proofs->pluck('id')->toArray();
  }
}
