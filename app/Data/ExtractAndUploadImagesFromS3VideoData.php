<?php

namespace App\Data;

use App\Enums\VideoToImageIntervalEnum as IntervalEnum;
use Spa<PERSON>\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Transformers\EnumTransformer;
use Spatie\LaravelData\Data;

class ExtractAndUploadImagesFromS3VideoData extends Data
{
    public function __construct(
        public string $video,
        #[WithTransformer(EnumTransformer::class)]
        public IntervalEnum $interval_type,
        public string $disk,
        public string $toDir,
        public int $initProgress = 40,
    ) {
    }
}
