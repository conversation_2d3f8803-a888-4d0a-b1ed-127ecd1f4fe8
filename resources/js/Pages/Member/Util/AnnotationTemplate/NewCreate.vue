<template>
  <div>
    <h1 style="color: rgba(64, 64, 64, 1); height: 100px;" class="d-flex mx-7">
      注釈テンプレート登録
    </h1>
    <div class="mx-3 mb-9">
      <BackButton />
    </div>
    <ValidationObserver ref="observer">
      <v-dialog v-model="loading" persistent width="300">
        <v-card color="primary" dark>
          <v-card-text>
            注釈テンプレート生成中
            <v-progress-linear
              indeterminate
              color="white"
              class="mb-0"
            ></v-progress-linear>
          </v-card-text>
        </v-card>
      </v-dialog>

      <!-- Body -->
      <v-form>
        <!-- 申請名 -->
        <ValidationProvider
          v-slot="{ errors }"
          rules="required"
          name="annotation"
        >
          <v-textarea
            v-model="form.annotation"
            label="注釈"
            name="annotation"
            class="mx-7 annotation-textarea"
            outlined
            auto-grow
            :error-messages="errors"
          />
        </ValidationProvider>
      </v-form>

      <!-- Footer -->
      <div class="pa-3 d-flex flex-wrap justify-end">
        <v-spacer></v-spacer>
        <v-btn class="mt-2 mb-4 mx-4 create-button" color="primary" @click="submit" :disabled="loading">
          <v-icon left dark>mdi-comment-plus</v-icon>
          登録
        </v-btn>
      </div>
    </ValidationObserver>
  </div>
</template>

<script>
import Layout from '@/Layouts/NewLayout';
import BackButton from '@/Components/AicBackButton';
import { ValidationObserver, ValidationProvider } from 'vee-validate';

export default {
  name: 'AdminUtilsAnnotationTemplateCreate',
  layout: Layout,
  components: {
    BackButton,
    ValidationObserver,
    ValidationProvider,
  },

  data() {
    return {
      form: {
        annotation: '',
      },
      loading: false,
    };
  },
  methods: {
    async submit() {
      if (await this.$refs.observer.validate()) {
        this.loading = true;

        const form = this.$inertia.form(this.form);
        const path = this.route('member.utils.annotation-templates.upsert');

        form.post(path, {
          onError: errors => {
            this.loading = false;
            this.$refs.observer.setErrors(errors['default']);
          },
        });
      }
    },
  },
};
</script>
<style scoped lang="scss">
.create-button {
  font-size: 14px;
  font-weight: bold;
  height: 40px !important;
  width: 120px;
  z-index: 1;
}

.annotation-textarea ::v-deep(textarea) {
  font-size: 14px;
  height: 240px !important;
  width: 100%;
  z-index: 1;
}
</style>
