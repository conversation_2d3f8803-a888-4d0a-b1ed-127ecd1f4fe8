<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Cartridge\UpsertCartridgeAction;
use App\Actions\Cartridge\UpsertEnvCartridgeAction;
use App\Enums\CartridgeElementEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CartridgeController\DeleteEnvRequest;
use App\Http\Requests\Admin\CartridgeController\StoreRequest;
use App\Http\Requests\Admin\CartridgeController\UpdateEnvRequest;
use App\Http\Requests\Admin\CartridgeController\UpdateRequest;
use App\Models\Cartridge;
use App\Models\EnvCartridge;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CartridgeController extends Controller
{
    public function index()
    {
        $paginator = Cartridge::with('envCartridges')
                    ->filter(request()->all())
                    ->orderBy("id")
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

        // Cartridgeのenv_cartridge_idsをpaginatorの各要素に追加
        $paginator->getCollection()->transform(function ($cartridge) {
            $cartridge->app_envs = $cartridge->getAppEnvsAttribute();
            $cartridge->is_using = $cartridge->hasEnvCartridge();
            return $cartridge;
        });

        return Inertia::render('Admin/Cartridge/Index', [
            'paginator' => $paginator,
        ]);
    }

    public function show(Cartridge $cartridge)
    {
        $baseAi = CartridgeElementEnum::getBaseAiProperties();
        $cartridge->elements = $cartridge->elements ?? [];
        $formProps = CartridgeElementEnum::getFormProps();
        // cartridgeのenv_cartridgesを取得
        $cartridge->load('envCartridges');
        $cartridge->is_using = $cartridge->hasEnvCartridge();
        $env = config('app.env');

        return Inertia::render('Admin/Cartridge/Show', [
            'cartridge' => $cartridge,
            'baseAi' => $baseAi,
            'formProps' => $formProps,
            'appEnv' => $env,
            'defaultEmailToAddress' => config('mail.request-form.to.address'),
        ]);
    }

    public function create()
    {
        $baseAi = CartridgeElementEnum::getBaseAiProperties();
        $formProps = CartridgeElementEnum::getFormProps();
        $cartriges = Cartridge::with('envCartridges')
                    ->filter(request()->all())
                    ->orderBy("id")->get();
        $cartriges = $cartriges->all(); // arrayに変換

        foreach ($cartriges as $cartridge) {
            // cartridgeのenv_cartridgesを取得
            $cartridge->load('envCartridges');
            $cartridge->is_using = $cartridge->hasEnvCartridge();
        }

        $env = config('app.env');

        return Inertia::render('Admin/Cartridge/Create', [
            'baseAi' => $baseAi,
            'formProps' => $formProps,
            'cartridges' => $cartriges,
            'appEnv' => $env,
            'defaultEmailToAddress' => config('mail.request-form.to.address'),
        ]);
    }

    public function store(StoreRequest $request, UpsertCartridgeAction $action)
    {
        $data = $request->data();

        try {
            DB::beginTransaction();

            $action->execute($data);

            DB::commit();

            return redirect()->route('admin.cartridges.create', [], self::HTTP_STATUS_SEE_OTHER)

                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => $data->name . 'を作成しました。'
                             ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function update(UpdateRequest $request, UpsertCartridgeAction $action)
    {
        $data = $request->data();

        try {
            DB::beginTransaction();

            $action->execute($data);

            DB::commit();

            return redirect()->route('admin.cartridges.create', [], self::HTTP_STATUS_SEE_OTHER)

                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => sprintf("「%s」を更新しました。", $data->name)
                             ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function updateEnv(UpdateEnvRequest $request, UpsertEnvCartridgeAction $action)
    {
        $data = $request->data();

        try {
            DB::beginTransaction();

            $action->execute($data);

            DB::commit();

            return redirect()->route('admin.cartridges.create', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => sprintf("現環境（%s）に設定を適用しました。", $data->app_env)
                             ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
  
    public function destroy(Cartridge $cartridge)
    {
        try {
            DB::beginTransaction();

            $cartridge->delete();

            DB::commit();

            return redirect()->route('admin.cartridges.create', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => sprintf("「%s」を削除しました。", $cartridge->name)
                             ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function destroyEnv(DeleteEnvRequest $request,)
    {
        $data = $request->data();
        try {
            DB::beginTransaction();

            EnvCartridge::where('app_env', $data->app_env)->delete();

            DB::commit();

            return redirect()->route('admin.cartridges.create', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => sprintf("現環境（%s）のカートリッジ設定を解除しました。", $data->app_env)
                             ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
