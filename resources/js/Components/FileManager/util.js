const TreeModel = require('tree-model');
const Path = require('path');

export const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

export class FileHandler {
  constructor(languages) {
    /**
     * Treeモデルインスタンス
     * https://github.com/joaonuno/tree-model-js
     */
    this.tree = new TreeModel();

    /**
     * ルートディレクトリー
     */
    this.root = this.tree.parse({
      name: 'ROOT',
      path: '/',
      files: [],
    });

    /**
     * 現在ディレクトリー
     */
    this.current = this.root;

    /**
     * アップロードレビュ用ファイルリスト
     */
    this.uploadedFiles = [];

    /**
     * アップロード許可ファイルタイプ
     */
    this.accepts = [
      // MS
      //   '.doc',
      //   '.docx',
      //   '.xls',
      //   '.xlsx',
      //   '.ppt',
      //   '.pptx',

      // 画像
      //   '.jpg',
      //   '.jpeg',
      //   '.png',
      //   '.bmp',
      //   '.gif',

      // テキスト
      //   '.txt',
      //   '.csv',

      // PDF
      '.pdf',

      // 動画
      '.mp4',
      '.wmv',
      //   '.mov',
      '.avi',
      //   '.mgp',
      //   '.mpg',
      '.mpeg',
      '.flv',
      '.mkv',
      '.m4v',

      // CAD
      //   '.dwf',
      //   '.dxf',
      //   '.dwg',

      // Illustrator
      //   '.ai',
      //   '.psd',
      //   '.eps',
    ];

    this.MAX_FILE_COUNTS = 20; // 20個ファイル
    this.MAX_SIZE_PER_FILE = 50 * 1024 * 1024; // 50MB
    this.TOTAL_MAX_SIZE = 100 * 1024 * 1024; // 100MB
    this.languages = languages;

    // ディレクトリーパス
    this.ORIGIN_PATH = '受付';
    this.CONVERTED_PATH = 'PDF変換';
    this.SHARED_PATH = '作業用';
    this.CLOSED_PATH = '確認済';

    // 倫理確認ステータス(proof_statusesモデル)
    this.PROOF_STATUS_REQUESTING = 'requesting';
    this.PROOF_STATUS_REJECT = 'reject';
    this.PROOF_STATUS_ASSIGNMENT = 'assignment';
    this.PROOF_STATUS_PROGRESSING = 'progressing';
    this.PROOF_STATUS_CLOSED = 'closed';

    // プレーンファイルチェック用サフィックス
    this.PLAIN_FILE_CHECKED_SUFIX = '_gendoc.pdf';
  }

  init(userType, proof) {
    this.userType = userType;
    this.proof = proof;

    switch (userType) {
      case 'requester':
        this.createDir(this.ORIGIN_PATH);

        if (proof?.status.value == this.PROOF_STATUS_CLOSED) {
          this.createDir(this.CLOSED_PATH);
        } else if (proof?.origin == undefined) {
          this.changeActive('/' + this.ORIGIN_PATH);
        }
        break;
      case 'member':
        if (proof?.status.value != this.PROOF_STATUS_CLOSED) {
          this.createDir(this.SHARED_PATH);
        }
        if (proof?.status.value == this.PROOF_STATUS_CLOSED) {
          this.createDir(this.CLOSED_PATH);
        }
        break;
      case 'admin':
        this.createDir(this.ORIGIN_PATH);
        this.createDir(this.CONVERTED_PATH);

        if (proof?.status.value != this.PROOF_STATUS_CLOSED) {
          this.createDir(this.SHARED_PATH);
        }

        this.createDir(this.CLOSED_PATH);
        break;
      default:
        break;
    }

    if (proof) {
      this.initFiles(userType, proof);
    }
  }

  initFiles(userType, proof) {
    const initAddFile = files => {
      if (files == undefined || files == null) {
        return;
      }

      const s3DirRegex = `/proof/${proof.uuid}`;

      files.forEach(file => {
        const dir = file.dir.replace(s3DirRegex, '');

        this.createDir(dir);

        const node = this.findNode(dir);
        if (node) {
          node.model.files.push(file);
        } else {
          console.log('Not Found Directory: ', dir);
        }
      });
    };

    switch (userType) {
      case 'requester':
        initAddFile(proof.origin);
        if (proof?.status.value == this.PROOF_STATUS_CLOSED) {
          initAddFile(proof.closed);
        }
        break;
      case 'member':
        if (proof?.status.value == this.PROOF_STATUS_CLOSED) {
          initAddFile(proof.closed);
        } else {
          initAddFile(proof.shared);
        }
        break;
      case 'admin':
        initAddFile(proof.origin);
        initAddFile(proof.converted);
        if (proof?.status.value == this.PROOF_STATUS_CLOSED) {
          initAddFile(proof.closed);
        } else {
          initAddFile(proof.shared);
        }
        break;
      default:
        break;
    }
  }

  /**
   * ディレクトリー生成
   * @param {String} name
   */
  createDir(path) {
    if (!path || path === '/') {
      return;
    }

    let tempCurrent = this.current;

    path.split('/').forEach(dirName => {
      const path = Path.join(tempCurrent.model.path, dirName);
      const node = this.findNode(path);

      if (node) {
        tempCurrent = node;
      } else {
        const newNode = this.tree.parse({
          name: dirName,
          path,
          files: [],
        });

        tempCurrent.addChild(newNode);
        tempCurrent = newNode;
      }
    });
  }

  /**
   * 許可したファイルタイプか確認
   * @param {String} extension
   * @returns Boolean
   */
  isAcceptType(extension) {
    return this.accepts.includes(`.${extension.toLowerCase()}`);
  }

  /**
   * 許可してないファイルタイプか確認
   * @param {String} extension
   * @returns Boolean
   */
  isNotAcceptType(extension) {
    return !this.isAcceptType(extension);
  }

  hasFile(file) {
    return this.current.model.files.some(f => f.name == file.name);
  }

  getTotalSize(files) {
    return files.reduce((prev, current) => prev + current.size, 0);
  }
  /**
   * TODO フィルター追加必要
   * ファイルチェック
   * @param {Object} file
   * @returns Boolean
   */
  validateFile(fileInfo) {
    if (this.isToBeValidate()) {
      if (this.isNotAcceptType(fileInfo.extension)) {
        fileInfo.error = '有効なファイルではありません。';

        return false;
      }

      if (this.hasFile(fileInfo)) {
        fileInfo.error = '既にファイルが存在しています。';

        return false;
      }

      const PDF_EXTENSION = 'pdf';
      if (fileInfo.extension == PDF_EXTENSION) {
        if (this.MAX_SIZE_PER_FILE < fileInfo.size) {
          fileInfo.error =
            '添付ファイルのサイズが最大サイズを超えています。 (許容サイズ: ' +
            formatBytes(this.MAX_SIZE_PER_FILE) +
            'まで)';

          return false;
        }

        const files = this.getAllFiles().filter(
          file => file.extension == PDF_EXTENSION,
        );

        if (this.MAX_FILE_COUNTS < files.length + 1) {
          fileInfo.error = `添付ファイルの数がサーバーで許容されている最大数を超えています。 (許容ファイル数: ${this.MAX_FILE_COUNTS}まで)`;

          return false;
        }

        if (this.TOTAL_MAX_SIZE < this.getTotalSize([...files, fileInfo])) {
          fileInfo.error =
            '添付ファイルのサイズがサーバーで許容されている最大サイズを超えています。 (許容サイズ: ' +
            formatBytes(this.TOTAL_MAX_SIZE) +
            'まで)';

          return false;
        }
      }
    }

    return true;
  }

  /**
   * ファイルリストからファイルを追加
   * @param {File} file
   * @param {String} path
   */
  addFile(file, path) {
    const node = this.findNode(path);

    if (node) {
      const fileInfo = {
        name: file.name,
        size: file.size,
        path: path,
        extension: file.name
          .split('.')
          .pop()
          .toLocaleLowerCase(),
        last_modified: new Date(file.lastModified),
        file: file,
        proof_language_id: _.find(
          this.languages,
          _.matchesProperty('value', 'ja-JP'),
        ).id,
      };

      if (this.validateFile(fileInfo)) {
        node.model.files.push(fileInfo);
      }

      this.uploadedFiles.push({
        ...fileInfo,
        name: Path.join(path, file.name),
      });
    } else {
      console.log('Not Found Directory: ', path);
    }
  }

  removeDir(node) {
    node.drop();
  }

  removeFile(file) {
    this.current.model.files = this.current.model.files.filter(f => {
      return f.name !== file.name;
    });
  }

  // パスでNode検索
  findNode(path) {
    const node = this.root.first(node => node.model.path === path);

    return node;
  }

  hasNode(path) {
    return !!this.findNode(path);
  }

  hasNotNode(path) {
    return !this.findNode(path);
  }

  // ディレクトリー移動
  changeActive(path) {
    switch (path) {
      case undefined:
        this.current = this.current;
        break;
      case '/':
        this.current = this.root;
        break;
      default:
        this.current = this.findNode(path);
        break;
    }
  }

  getAllFiles() {
    const all = this.root.all();

    const files = all
      .filter(node => node.model.files.length !== 0)
      .map(node => node.model.files);

    return _.flatten(files);
  }

  getFiles(path) {
    const node = this.root.all(node => {
      return node.model.path.startsWith('/' + path);
    });

    const files = node
      .filter(node => node.model.files.length !== 0)
      .map(node => node.model.files);

    return _.flatten(files);
  }

  getTree() {
    const serializer = node => {
      return {
        ...node.model,
        children: node.hasChildren() ? node.children.map(serializer) : [],
      };
    };

    const tree = [this.root].map(serializer);

    return tree;
  }
  isToBeValidate() {
    return this.userType == 'requester' && this.proof === undefined;
  }

  /**
   * @param {string} name ファイル名
   * @returns
   */
  isPlainFileChecked(name) {
    return name.endsWith(this.PLAIN_FILE_CHECKED_SUFIX);
  }
}

export default {
  formatBytes,
  FileHandler,
};
