<?php

namespace App\Actions\Statistics;

use App\Enums\StatisticsUnitEnum;
use App\Models\ProofFile;
use Carbon\CarbonPeriod;
use Closure;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Stringable;

class GetAnnotationCountGroupByDateAction
{
    private string $dateColumn = 'date';
    private string $annotTypeColumn = 'annot_type';
    public string $drawingCount = 'drawing';
    public string $textCount = 'text';
    public string $totalCount = 'total';

    public function execute(
        Model|string $model,
        CarbonPeriod $range,
        string $dateformat = StatisticsUnitEnum::Day,
        ?Closure $callback = null,
    ): Collection {
        $fromAlias = 'datetimes';
        $fromTableDateColumn = "{$fromAlias}.{$this->dateColumn}";
        $fromTableAnnotTypeColumn = "{$fromAlias}.{$this->annotTypeColumn}";

        $dateFormatQuery = $this->convertDateFormatQuery(
            $fromTableDateColumn,
            $dateformat
        );

        $model = $this->resolveModel($model);

        Log::info(
            "GetAnnotationCountGroupByDateAction",
            [
                'model' => $model->getTable(),
                'dateformat' => $dateformat,
                'range' => [
                    'start' => $range->start,
                    'end' => $range->end,
                ],
                'threshold' => config('annotation.count.threshold'),
                'adobeAnnotCount' => $this->getAdobeAnnotCountQuery($model, $range)->toSql(),
                'expressAnnotCount' => $this->getExpressAnnotCountQuery($model, $range)->toSql(),
            ]
        );
        $adobeAnnotCount = $this->getAdobeAnnotCountQuery($model, $range);
        $expressAnnotCount = $this->getExpressAnnotCountQuery($model, $range);

        return DB::query()
            ->from($adobeAnnotCount->unionAll($expressAnnotCount), $fromAlias)
            ->select([
                DB::raw("{$dateFormatQuery} as {$this->dateColumn}"),
                DB::raw("COUNT(IF({$fromTableAnnotTypeColumn} = '{$this->drawingCount}', {$fromTableAnnotTypeColumn}, NULL)) as {$this->drawingCount}"),
                DB::raw("COUNT(IF({$fromTableAnnotTypeColumn} = '{$this->textCount}', {$fromTableAnnotTypeColumn}, NULL)) as {$this->textCount}"),
                DB::raw("COUNT({$fromTableDateColumn}) as {$this->totalCount}")
            ])
            ->whereRaw("DATE_FORMAT({$fromTableDateColumn}, '%Y-%m-%d %H:%i:%s') BETWEEN '{$range->start}' AND '{$range->end}'")
            ->groupByRaw($dateFormatQuery)
            ->when($callback, fn ($query) => $callback($query))
            ->get()
            ->mapWithkeys(fn ($item) =>[
                $item->{$this->dateColumn} => [
                    $this->drawingCount => $item->{$this->drawingCount},
                    $this->textCount => $item->{$this->textCount},
                    $this->totalCount => $item->{$this->totalCount},
                ]
            ]);
    }

    private function getAdobeAnnotCountQuery(Model $model, CarbonPeriod $range): QueryBuilder
    {
        return  DB::table($model->getTable())
            ->select([
                "jt.datetime as {$this->dateColumn}",
                DB::raw("IF(jt.bodyValue = '', '{$this->drawingCount}', '{$this->textCount}') as {$this->annotTypeColumn}"),
            ])
            ->crossJoin(DB::raw(
                "JSON_TABLE(annotations, '$[*]' COLUMNS (
                    datetime TIMESTAMP PATH '$.modified',
                    bodyValue LONGTEXT PATH '$.bodyValue'
                )) AS jt"
            ))
            ->where('proof_files.id', '<', config('annotation.count.threshold'))
            ->where('proof_files.type', ProofFile::TYPE_SHARED)
            // 注釈の最小日付を取得することは困難ですので、1年の期間をおいて計算します
            ->where('proof_files.created_at', '>=', $range->start->subYear())
            ->where('proof_files.updated_at', '<=', $range->end)
            ->whereBetween('jt.datetime', [$range->start, $range->end]);
    }

    private function getExpressAnnotCountQuery(Model $model, CarbonPeriod $range): QueryBuilder
    {
        return DB::table($model->getTable())
            ->select([
                DB::raw("REGEXP_SUBSTR(jt.annotation, '(\\\d{14})') as {$this->dateColumn}"),
                DB::raw(
                    "IF(
                        ExtractValue(REPLACE(jt.annotation, '\\\', ''), '//contents') = '',
                        '{$this->drawingCount}',
                        '{$this->textCount}'
                    ) as {$this->annotTypeColumn}"
                ),
            ])
            ->crossJoin(DB::raw(
                "JSON_TABLE(annotations, '$[*]' COLUMNS (
                    annotation LONGTEXT PATH '$'
                )) AS jt"
            ))
            ->where('proof_files.id', '>=', config('annotation.count.threshold'))
            ->where('proof_files.type', ProofFile::TYPE_SHARED)
            // 注釈の最小日付を取得することは困難ですので、1年の期間をおいて計算します
            ->where('proof_files.created_at', '>=', $range->start->subYear())
            ->where('proof_files.updated_at', '<=', $range->end);
    }

    private function resolveModel(Model|string $model): Model
    {
        return is_string($model) ? resolve($model) : $model;
    }

    private function convertDateFormatQuery(string $column, string $format): string
    {
        return Str::of($format)
            ->pipe(fn (Stringable $format) =>
                $format
                    ->explode('-')
                    ->map(fn (string $char) => "%" . $char)
                    ->implode('-'))
            ->pipe(fn (string $format) =>
                "DATE_FORMAT($column, '$format')");
    }
}
