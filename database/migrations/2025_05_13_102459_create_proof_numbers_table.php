<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProofNumbersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('proof_numbers', function (Blueprint $table) {
            $table->id();
            $table->string('company_domain')->default('')->nullable()->comment('会社識別ドメイン');
            $table->string('company_code')->default('')->nullable()->comment('申請番号前の会社コード');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('proof_numbers');
    }
}
