<?php

namespace App\Http\API\PRM;

use App\Http\API\PRM\Contracts\PRM;
use Exception;

/**
 * BAPIセット事例リコメンドAPI
 * Dockerベース
 */
class GeneralPRM extends PRM
{
    public function predict(string $text): array
    {
        $response = $this->client->post('/file-class/predict/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
            'text' => $text,
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "ファイル分類予測API: {$this->message}",
                    $this->code
                );
            }

            return $response->json();
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function predicts(array $text)
    {
        $response = $this->client->post('/file-class/predict-list/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
            'text' => $text,
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "ファイル分類予測API (json): {$this->message}",
                    $this->code
                );
            }

            return  $response->json();
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function annotationSamples(): array
    {
        $response = $this->client->post('/file-class/annotation-sample/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "注釈の代表例の取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['annotations'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function annotations(string|int $class_no): array
    {
        $response = $this->client->post('/file-class/annotations/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
            'class_no' => $class_no,
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "おすすめの注釈の取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['annotations'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function files(string|int $class_no)
    {
        $response = $this->client->post('/file-class/file-info-list/', [
                            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
                            'class_no' => $class_no,
                        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "おすすめの注釈の取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['file_list'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }
}
