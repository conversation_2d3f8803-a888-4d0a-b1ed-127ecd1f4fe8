<?php

namespace App\Http\Controllers\API\Statistics;

use App\Actions\Statistics\GetRequestedProofCountAction;
use App\Http\Controllers\Controller;

class RequestedProofCountController extends Controller
{
    public function __invoke(GetRequestedProofCountAction $action)
    {
        try {
            return $action->execute();
        } catch (\Throwable $th) {
            throw $th;
        }
    }
}
