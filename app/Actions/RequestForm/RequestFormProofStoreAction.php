<?php

namespace App\Actions\RequestForm;

use App\Actions\Proof\SyncProofCategories;
use App\Data\RequestFormStoreData;
use App\Models\Proof;
use App\Models\ProofNumber;
use App\Models\ProofStatus;
use App\Models\User;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\Log;

class RequestFormProofStoreAction
{
    public function __construct(
        private SyncProofCategories $syncProofCategories
    ) {}

    public function execute(RequestFormStoreData $data, User $user): Proof
    {
        Log::info('user: ', [$user]);
        $companyDomain = User::getCompanyDomain($user);
        Log::info('companyDomain: ', [$companyDomain]);
        $proofNumber = ProofNumber::getProofNumber($companyDomain);
        Log::info('proofNumber: ', [$proofNumber]);
        $proof = $user->proofs()->create([
            'proof_number' => $proofNumber,
            'uuid' => $data->uuid,
            'title' => $data->title,
            'external_deadline_at' => $data->externalDeadlineAt->format('Y-m-d H:i:s'),
            'internal_deadline_at' => $this->calculateInternalDeadlineAt($data->externalDeadlineAt),
            'proof_status_id' => ProofStatus::ofValue(ProofStatus::STATUS_REQUESTING)->first()->id,
            'publish_date_start' => $data->publishDate->start,
            'publish_date_end' => $data->publishDate->end,
            'supplement' => $data->supplement
        ]);
        Log::info('proof: ', [$proof]);
        $this->syncProofCategories->execute($proof, $data->ips, $data->categoryId);

        return $proof;
    }

    /**
     * 内部締切日を計算
     * 外部締切日の時間が「12:00 ~ 13:30」の場合、内部締切日の時間を「11:30」に設定
     * その以外の時間の場合、内部締切日の時間を外部締切日時間の30分前に設定
     *
     * @param CarbonImmutable $externalDeadlineAt
     * @return CarbonImmutable $internalDeadlineAt
     */
    private function calculateInternalDeadlineAt(CarbonImmutable $externalDeadlineAt): CarbonImmutable
    {
        if ($externalDeadlineAt->between(
            $externalDeadlineAt->copy()->setTime(12, 0),
            $externalDeadlineAt->copy()->setTime(13, 30)
        )) {
            return $externalDeadlineAt->copy()->setTime(11, 30);
        }

        return $externalDeadlineAt->subMinutes(30);
    }
}
