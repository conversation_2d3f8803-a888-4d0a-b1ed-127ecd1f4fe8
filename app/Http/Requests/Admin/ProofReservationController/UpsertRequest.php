<?php

namespace App\Http\Requests\Admin\ProofReservationController;

use App\Data\Admin\Proof\ProofReservationUpsertData;
use Illuminate\Foundation\Http\FormRequest;

class UpsertRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $minLimit = max($this->get('reserved', 1), 1);

        return [
            'date' => [
                'required',
                'string',
                'date_format:Y-m-d'
            ],
            'limit' => [
                "required",
                "numeric",
                "integer",
                "min:{$minLimit}"
            ]
        ];
    }

    public function data(): ProofReservationUpsertData
    {
        return ProofReservationUpsertData::from($this->validated());
    }

    public function attributes()
    {
        return [
            'date' => '日付',
            'limit' => 'キャパシティー数',
        ];
    }
}
