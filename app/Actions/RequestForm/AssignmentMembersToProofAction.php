<?php

namespace App\Actions\RequestForm;

use App\Models\Proof;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class AssignmentMembersToProofAction
{
    /**
     * @param Proof $proof
     */
    public function execute(Proof $proof): Collection
    {
        $members = User::query()
            ->ofUserType(User::USER_TYPE_MEMBER)
            ->where(function (Builder $query) use ($proof) {
                $query
                    ->orWhereHasIps($proof->ips->pluck('id')->toArray())
                    ->orWhereHasCategories($proof->category->id);
            })
            ->get();

        $proof->members()->sync($members);
        $proof->save();

        return $members;
    }
}
