<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Proof\UpsertBusinessCategoryAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProofBusinessCategoryController\StoreRequest;
use App\Http\Requests\Admin\ProofBusinessCategoryController\UpdateRequest;
use App\Http\Requests\Admin\ProofBusinessCategoryController\UpsertRequest;
use App\Models\ProofBusinessCategory;
use App\Models\ProofStatus;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ProofBusinessCategoryController extends Controller
{
    public function index()
    {
        $paginator = ProofBusinessCategory::filter(request()->all())
                    ->orderByDesc("id")
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

        return Inertia::render('Admin/Proof/BusinessCategory/Index', [
            'paginator' => $paginator,
        ]);
    }

    public function show(ProofBusinessCategory $category)
    {
        return Inertia::render('Admin/Proof/BusinessCategory/Show', [
            'category' => $category
        ]);
    }

    public function create()
    {
        return Inertia::render('Admin/Proof/BusinessCategory/Create', []);
    }

    public function store(StoreRequest $request, UpsertBusinessCategoryAction $action)
    {
        $data = $request->data();

        try {
            DB::beginTransaction();

            $action->execute($data);

            DB::commit();

            return redirect()->route('admin.proofs.business-category.index', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => $data->name . 'を作成しました。'
                             ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function update(UpdateRequest $request, UpsertBusinessCategoryAction $action)
    {
        $data = $request->data();

        try {
            DB::beginTransaction();

            $action->execute($data);

            DB::commit();

            return redirect()->route('admin.proofs.business-category.index', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => sprintf("「%s」を更新しました。", $data->name)
                             ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function destroy(ProofBusinessCategory $category)
    {
        try {
            DB::beginTransaction();

            $category->delete();
            $category->proofs()->detach();

            DB::commit();

            return redirect()->route('admin.proofs.business-category.index', [], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => sprintf("「%s」を削除しました。", $category->name)
                             ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
