<?php

namespace App\Actions\Annotations;

use App\Data\UnnaturalTextOcrData;
use App\Data\UnnaturalTextItemData;
use App\ValueObjects\Color;
use App\ValueObjects\Contracts\Annotation;
use App\ValueObjects\SquareAnnotation;
use App\ValueObjects\StickyNoteAnnotation;
use Illuminate\Support\Collection;
use Spatie\LaravelData\DataCollection;

class GenerateUnnaturalTextAnnotationsAction
{
    /**
     * 不自然テキストのアノテーションを生成
     * @var UnnaturalTextItemData[] $predicts
     *
     * @return array<string>
     */
    public function execute(DataCollection $predicts): array
    {
        return $predicts
            ->toCollection()
            ->flatMap(fn (UnnaturalTextItemData $item) => $this->getUnnaturalTextPredictedFromOcr($item))
            ->map(fn (Annotation $annotation) => $annotation->xml())
            ->flatten()
            ->toArray();
    }

    private function getUnnaturalTextPredictedFromOcr(UnnaturalTextItemData $item): Collection
    {
        $color = new Color(
            red: 255,
            green: 0,
            blue: 0
        );

        return $item->ocr
            ->toCollection()
            ->map(fn (UnnaturalTextOcrData $ocrData) => new SquareAnnotation(
                page: $item->page,
                pts: $ocrData->coords,
                comment: <<<COMMENT
                【AI】<訂正案>
                {$ocrData->suggestion}
                ---
                {$ocrData->reason}
                COMMENT,
                color: $color->css(),
            ));
    }
}
