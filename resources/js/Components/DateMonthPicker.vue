<template>
  <v-dialog ref="dialog" v-model="display" :width="dialogWidth" persistent>
    <template v-slot:activator="{ on }">
      <v-text-field
        v-model="_datemonth"
        :label="label"
        :disabled="disabled"
        v-bind="textFieldProps"
        v-on="on"
        readonly
        prepend-icon="mdi-calendar-blank"
      ></v-text-field>
    </template>
    <v-date-picker
      v-model="_datemonth"
      v-bind="datePickerProps"
      type="month"
      scrollable
      full-width
      locale="ja-jp"
    >
      <v-spacer></v-spacer>
      <v-btn color="grey lighten-1" text @click="clearHandler">
        終了
      </v-btn>
      <v-btn color="green darken-1" text @click="okHandler">
        確認
      </v-btn>
    </v-date-picker>
  </v-dialog>
</template>

<script>
const DEFAULT_DATE = null;
const DEFAULT_DIALOG_WIDTH = 340;
const DEFAULT_CLEAR_TEXT = 'CLEAR';
const DEFAULT_OK_TEXT = 'OK';

export default {
  name: 'v-date-month-picker',
  model: {
    prop: 'datemonth',
    event: 'input',
  },
  props: {
    label: {
      type: String,
      default: '',
    },
    datemonth: {
      type: [Date, String],
      default: DEFAULT_DATE,
    },
    disabled: {
      type: Boolean,
    },
    label: {
      type: String,
      default: '',
    },
    isTo: {
      type: Boolean,
    },
    dialogWidth: {
      type: Number,
      default: DEFAULT_DIALOG_WIDTH,
    },
    clearText: {
      type: String,
      default: DEFAULT_CLEAR_TEXT,
    },
    okText: {
      type: String,
      default: DEFAULT_OK_TEXT,
    },
    textFieldProps: {
      type: Object,
    },
    datePickerProps: {
      type: Object,
    },
  },
  computed: {
    _datemonth: {
      get() {
        return this.datemonth;
      },
      set(value) {
        this.$emit('update:datemonth', value);
      },
    },
  },
  data() {
    return {
      display: false,
    };
  },
  methods: {
    okHandler() {
      this.resetPicker();
      this.$emit('input');
    },
    clearHandler() {
      this.resetPicker();
      this._datemonth = DEFAULT_DATE;

      this.$emit('input');
    },
    resetPicker() {
      this.display = false;
    },
  },
};
</script>
