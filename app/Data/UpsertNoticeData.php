<?php

namespace App\Data;

use App\Models\UserType;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Spatie\LaravelData\Data;

class UpsertNoticeData extends Data
{
    public function __construct(
        public ?int $id,
        public string $title,
        public string $body,
        public bool $visible,
        public string $published_at,
        public string $closed_at,
        public ?UserType $user_type,
    ) {
    }

    public static function fromRequest(Request $request): static
    {
        Log::info('UpsertNoticeData::fromRequest');
        Log::info($request->all());
        return self::from([
            ...$request->all(),
            'user_type' => UserType::find($request->user_type_id),
            'published_at' => CarbonImmutable::createFromFormat('Y-m-d H:i:s', $request->published_at),
            'closed_at' => CarbonImmutable::createFromFormat('Y-m-d H:i:s', $request->closed_at),
        ]);
    }

    public static function rules(): array
    {
        return [
            'title' => [
                'required',
                'string',
            ],
            'body' => [
                'required',
                'string',
            ],
            'visible' => [
                'required',
                'boolean',
            ],
            'user_type_id' => [
                'required',
                Rule::exists(
                    (new UserType())->getTable(),
                    'id'
                )
            ],
            'published_at' => [
                'required',
                'date',
            ],
            'closed_at' => [
                'required',
                'date',
            ],
        ];
    }
}
