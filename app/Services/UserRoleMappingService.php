<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class UserRoleMappingService
{
    /**
     * Map user type to manual role for ChatGPT assistant
     */
    public static function mapUserTypeToManualRole(string $userType): string
    {
        // Only requester user type can access the admin manual
        $mapping = ($userType === 'requester') ? 'admin' : null;

        Log::info('User type to manual role mapping', [
            'user_type' => $userType,
            'manual_role' => $mapping,
            'access_granted' => $mapping !== null
        ]);

        return $mapping;
    }

    /**
     * Get all available user type to manual role mappings
     */
    public static function getAllMappings(): array
    {
        return [
            'requester' => 'admin',
            'member' => null,
            'admin' => null
        ];
    }

    /**
     * Get manual role for authenticated user
     */
    public static function getManualRoleForAuthenticatedUser(): ?string
    {
        if (!auth()->check()) {
            return null; // No access for unauthenticated users
        }

        $userType = auth()->user()->type_value;
        return self::mapUserTypeToManualRole($userType);
    }

    /**
     * Get user context for ChatGPT assistant
     */
    public static function getUserContextForAssistant(): array
    {
        if (!auth()->check()) {
            return [
                'user_type' => 'guest',
                'manual_role' => null,
                'has_manual_access' => false
            ];
        }

        $user = auth()->user();
        $userType = $user->type_value;
        $manualRole = self::mapUserTypeToManualRole($userType);

        return [
            'user_id' => $user->id,
            'user_type' => $userType,
            'manual_role' => $manualRole,
            'has_manual_access' => $manualRole !== null,
            'user_email' => $user->email,
            'user_name' => $user->name
        ];
    }

    /**
     * Validate if a manual role is valid
     */
    public static function isValidManualRole(?string $role): bool
    {
        return $role === 'admin';
    }

    /**
     * Check if user has access to manual
     */
    public static function hasManualAccess(string $userType): bool
    {
        return $userType === 'requester';
    }

    /**
     * Get manual file name for a given role
     */
    public static function getManualFileNameForRole(?string $role): ?string
    {
        // Only admin manual is available and only for requester users
        if ($role === 'admin') {
            return 'AIC_倫理確認システム_マニュアル（管理者）_20250602.md';
        }
        return null;
    }

    /**
     * Get user type display name in Japanese
     */
    public static function getUserTypeDisplayName(string $userType): string
    {
        $displayNames = [
            'requester' => '申請者',
            'member' => '事務局メンバー',
            'admin' => '管理者'
        ];

        return $displayNames[$userType] ?? '不明';
    }

    /**
     * Get manual role display name in Japanese
     */
    public static function getManualRoleDisplayName(string $manualRole): string
    {
        $displayNames = [
            'member' => 'メンバー向け',
            'supporter' => 'サポーター向け',
            'admin' => '管理者向け'
        ];

        return $displayNames[$manualRole] ?? '管理者向け';
    }
}