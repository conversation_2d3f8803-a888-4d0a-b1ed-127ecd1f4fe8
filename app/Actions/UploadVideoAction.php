<?php

namespace App\Actions;

use App\Data\UploadVideoData;
use App\Enums\VideoToImageUploadTypeEnum as UploadType;
use App\Events\VideoToImageProgressEvent;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use League\Flysystem\FileExistsException;

class UploadVideoAction
{
    const ProgressMessage = '動画保存中...';

    public function execute(UploadVideoData $data): string
    {
        Log::info('Upload Video', $data->toArray());

        // 無い場合はアップロードする
        if($this->hasNotFile($data->toPath, $data->disk)) {
            try {
                $this->uploadVideo(
                    $data->upload_type,
                    $data->video,
                    $data->toPath,
                    $data->disk,
                );
            } catch(FileExistsException $th) {
            } catch(Exception $th) {
                throw $th;
            }
        }

        return $data->toPath;
    }

    private function uploadVideo(
        UploadType $uploadType,
        string|UploadedFile $video,
        string $toPath,
        string $disk = 's3'
    ): void {
        $wasSucceed = match($uploadType->value) {
            UploadType::S3Key => $this->fromS3Key(
                $video,
                $toPath,
                $disk
            ),
            UploadType::File  => $this->fromUploadedFile(
                $video,
                pathinfo($toPath, PATHINFO_DIRNAME),
                $disk
            )
        };

        if(!$wasSucceed) {
            throw new Exception('upload fail');
        }
    }

    private function fromS3Key(
        string $from,
        string $to,
        string $disk,
    ): bool {
        return Storage::disk($disk)->copy($from, $to);
    }

    private function fromUploadedFile(
        UploadedFile $video,
        string $path,
        string $disk,
    ): bool {
        return Storage::putFileAs($path, $video, $video->getClientOriginalName(), ['disk' => $disk]);
    }

    private function hasNotFile(
        string $toPath,
        string $disk = 's3'
    ): bool {
        return !Storage::disk($disk)->exists($toPath);
    }
}
