<?php

namespace App\Data;

use App\Enums\VideoToImageUploadTypeEnum as UploadTypeEnum;
use Illuminate\Http\UploadedFile;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Transformers\EnumTransformer;

class UploadVideoData extends Data
{
    public function __construct(
        public UploadedFile|string $video,
        #[WithTransformer(EnumTransformer::class)]
        public UploadTypeEnum $upload_type,
        public string $disk,
        public string $toPath,
        public int $initProgress = 20,
    ) {
    }
}
