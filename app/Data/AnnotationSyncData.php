<?php

namespace App\Data;

use App\Rules\XML;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use SimpleXMLElement;
use <PERSON>tie\LaravelData\Data;

class AnnotationSyncData extends Data
{
    public function __construct(
        public SimpleXMLElement $xfdf,
        public string $action,
        public array $info,
    ) {
    }

    public static function fromRequest(Request $request): static
    {
        return new self(
            simplexml_load_string($request->xfdf),
            $request->action,
            $request->info
        );
    }

    public static function rules(): array
    {
        return [
            'xfdf' => [
                'required',
                'string',
                new XML
            ],
            'action' => [
                'required',
                'string',
                Rule::in(['add', 'modify', 'delete', 'convert'])
            ],
            'info' => [
                'nullable',
                'array',
            ],
            'info.*' => [
                'sometimes',
                'boolean',
            ],
        ];
    }
}
