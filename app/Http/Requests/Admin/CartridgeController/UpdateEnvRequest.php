<?php

namespace App\Http\Requests\Admin\CartridgeController;

use App\Data\EnvCartridgeData;
use Illuminate\Foundation\Http\FormRequest;

class UpdateEnvRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'app_env' => [
                "required",
                "string",
            ],
            'cartridge_id' => [
                'required',
                'numeric'
            ]
        ];
    }


    public function attributes()
    {
        return [
            'app_env' => '環境名',
            'cartridge_id' => 'カートリッジID',
        ];
    }

    public function data(): EnvCartridgeData
    {
        $result = $this->validated();
        return EnvCartridgeData::from($result);
    }

    public function messages()
    {
        return [
            'app_env.required' => ':attribute項目は必須です',
            'cartridge_id.required' => ':attribute項目は必須です',
        ];
    }
}
