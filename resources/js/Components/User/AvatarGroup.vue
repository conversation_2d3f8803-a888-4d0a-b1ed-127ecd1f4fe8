<template>
  <div>
    <span
      v-for="(user, index) in displayed.users"
      :key="user.id"
      class="avatar-group-item"
      :style="{ marginRight: `${margin}px` }"
    >
      <Avatar
        :color="getColor(index)"
        :size="avatarSize"
        :text="user.last_name"
        :tooltip-text="user.name"
        tooltip
        border
      >
        <template v-slot:tooltip>
          <span>{{ user.name }}</span>
        </template>
      </Avatar>
    </span>

    <span
      v-if="rest.hasRest"
      class="avatar-group-item"
      :style="{ marginRight: `${margin}px` }"
    >
      <v-menu top min-width="200px" rounded offset-y>
        <template v-slot:activator="{ on }">
          <v-btn icon v-on="on">
            <Avatar
              color="grey"
              :text="`+${rest.count}`"
              :size="avatarSize"
              border
            />
          </v-btn>
        </template>

        <v-card>
          <v-list>
            <v-subheader> 完了印ユーザー一覧 ({{ users.length }}) </v-subheader>

            <v-list-item v-for="(user, index) in users" :key="user.id">
              <v-list-item-avatar>
                <Avatar
                  :text="user.last_name"
                  :color="getColor(index)"
                  :size="avatarSize"
                />
              </v-list-item-avatar>

              <v-list-item-content>
                <v-list-item-title>{{ user.name }}</v-list-item-title>
                <v-list-item-subtitle>{{ user.email }}</v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </v-card>
      </v-menu>
    </span>
  </div>
</template>

<script>
import Avatar from './Avatar.vue';

export default {
  name: 'AvatarGroup',
  props: {
    users: {
      type: Array,
      required: true,
    },
    maxDisplay: {
      type: Number,
      default: 5,
    },
    margin: {
      type: Number,
      default: -16,
    },
    avatarSize: {
      type: Number,
      default: 42,
    },
  },
  components: {
    Avatar,
  },
  data() {
    return {
      colors: [
        'red',
        'pink',
        'purple',
        'deep-purple',
        'indigo',
        'primary',
        'light-blue',
        'cyan',
        'teal',
        'green',
        'light-green',
        'lime',
        'yellow',
        'amber',
        'orange',
        'deep-orange',
        'brown',
        'blue-grey',
      ],
    };
  },
  computed: {
    rest() {
      const users = this.users.slice(this.maxDisplay);

      return this.getUsersInfo(users);
    },
    displayed() {
      const users = this.users.slice(0, this.maxDisplay);

      return this.getUsersInfo(users);
    },
  },
  methods: {
    getUsersInfo(users) {
      return {
        users,
        count: users.length,
        hasRest: users.length > 0,
      };
    },
    getColor(index) {
      return this.colors[index % this.colors.length];
    },
  },
};
</script>

<style>
.avatar-group-item {
  position: relative;
}
</style>
