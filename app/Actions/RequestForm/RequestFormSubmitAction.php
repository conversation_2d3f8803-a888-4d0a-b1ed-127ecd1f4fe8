<?php

namespace App\Actions\RequestForm;

use App\Actions\BinaryToUploadedFileAction;
use App\Actions\Proof\CreateProofFileAndUploadAction;
use App\Actions\Proof\CreateRecommendBatchActionV2;
use App\Actions\Proof\GenerateNewVersionOfProofPdfAction;
use App\Actions\Proof\GenerateOldVersionOfProofPdfAction;
use App\Data\RequestFormFilesData;
use App\Data\RequestFormPublishDateData;
use App\Data\RequestFormStoreData;
use App\Helpers\ProofFileManager;
use App\Mail\ProofAssignmentedToNotRequiredUserMail;
use App\Mail\ProofRequestedConvertFailedAdminMail;
use App\Mail\ProofRequestedToAdminMail;
use App\Mail\ProofRequestedToNotRequiredUserMail;
use App\Mail\ProofRequestedToRequesterMail;
use App\Mail\ProofRequestHasVideoToAdminMail;
use App\Mail\ProofRequestHasVideoToRequesterMail;
use App\Models\Proof;
use App\Models\ProofFile;
use App\Models\User;
use Illuminate\Bus\Batch;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class RequestFormSubmitAction
{
    public function __construct(
        private RequestFormProofStoreAction $proofStoreAction,
        private CreateProofFileAndUploadAction $createProofFileAndUploadAction,
        private GenerateNewVersionOfProofPdfAction $generateNewProofPdfFileAction,
        private GenerateOldVersionOfProofPdfAction $generateOldProofPdfFileAction,
        private CreateRecommendBatchActionV2 $createRecommendBatchAction,
        private BinaryToUploadedFileAction $binaryToUploadedFileAction,
    ) {}

    public function execute(RequestFormStoreData $data, User $user): Proof
    {
        // 1. 倫理確認モデル生成
        Log::info("倫理確認モデル生成", [$data]);
        $proof = $this->proofStoreAction->execute($data, $user);

        // 2. 倫理確認ファイルモデル生成と保存(origin)
        Log::info("倫理確認ファイルモデル生成と保存(origin)", [$data->files]);
        $this->createOriginProofFiles($proof, $data->files);

        // 2-1. 動画の無い申請の場合は申請者へメール送信
        Log::info("動画の無い申請の場合は申請者へメール送信", [$data->files->hasVideos()]);
        if($data->files->hasVideos() == false) {
            Log::info("申請者へメール送信", [$proof]);
            Mail::to($proof->user)->send(new ProofRequestedToRequesterMail($proof));
        }

        // 3. ECAのAPIにリクエストを投げて新しいPDFを生成してからモデル生成と保存(converted)
        try{
            Log::info("ECAのAPIにリクエストを投げて新しいPDFを生成してからモデル生成と保存(converted)", [$data->files]);
            $this->createConvertedProofFiles(
                $proof,
                $data->files,
                $data->publishDate
                )->each(
                    // 4. 「converted」を「shared」にコピー
                    fn(ProofFile $proofFile) => $this->copyToShared($proof, $proofFile)
                );
        } catch (\Throwable $th) {
            Log::error($th->getMessage());

            // PDF変換に失敗した場合は、完了印でメンバーにあたるユーザーへメールを送信する
            $admins = User::ofUserType(User::USER_TYPE_ADMIN)->get();
            if ($admins->isNotEmpty()) {
                /**
                 * @var User $admins
                 */
                Log::info("管理者へ受付メール（PDF変換に失敗）", [$admins]);
                $admins->each(function ($admin) use ($proof) {
                    Log::info("管理者へ受付メール（PDF変換に失敗）", [$admin]);
                    Mail::to($admin)->send(new ProofRequestedConvertFailedAdminMail($proof, $admin));
                });
            }

            $errorMessage = [
                'message' => 'PDF変換に失敗しました。',
                'command' => 'createConvertedProofFiles',
                'code' => 200,
                'proof' => $proof,
            ];
            throw new \RuntimeException(
                json_encode($errorMessage, JSON_UNESCAPED_UNICODE)
            );
        }

        Log::info("ECAのAPIにリクエストを投げて新しいPDFを生成してからモデル生成と保存(converted)完了", [$data->files]);
        Log::info("動画が無ければリコメンドBatch生成", [$proof->id]);
        Log::info("動画が無ければリコメンドBatch生成", [$proof->title]);
        // 5. ビデオがある場合はリコメンドバッチを処理せず、メール送信のみ実行する
        $data->files->hasVideos() ?
            $this->handleIfHasVideo($proof) :
            $this->runRecommendBatch($proof, $data->files, $data->publishDate);

        return $proof->refresh();
    }

    private function createOriginProofFiles(Proof $proof, RequestFormFilesData $files): Collection
    {
        $startPath = config('filesystems.disks.s3.origin_dir');

        return $files
            ->toCollection()
            ->pluck('value')
            ->filter(fn($value) => $value instanceof UploadedFile)
            ->map(fn(UploadedFile $file) => $this->createProofFileAndUploadAction->execute(
                proof: $proof,
                file: $file,
                startPath: $startPath,
                type: ProofFile::TYPE_ORIGIN
            ));
    }

    private function createConvertedProofFiles(
        Proof $proof,
        RequestFormFilesData $files,
        RequestFormPublishDateData $publishDate
    ): Collection {
        return collect([])
            ->when($files->hasPdfs(), function (Collection $outputs) use ($proof, $files, $publishDate) {
                $proofFile = $this->generateOldProofPdfFileAction->execute(
                    $proof,
                    $files->pdfs,
                    $publishDate
                );

                return $outputs->push($proofFile);
            })
            ->when($files->hasGendocable(), function (Collection $outputs) use ($proof, $files, $publishDate) {
                $genDocProofFile = $this->generateNewProofPdfFileAction->execute(
                    $proof,
                    $publishDate,
                    $files,
                );

                return $outputs->push($genDocProofFile);
            });
    }

    private function copyToShared(
        Proof $proof,
        ProofFile $proofFile,
    ): ProofFile {
        $getFileBaseName = function (Proof $proof, ProofFile $proofFile): string {
            // $proof_id = str_pad($proof->id, 5, '0', STR_PAD_LEFT);
            $proof_no = $proof->proof_number;

            $fileBaseName = $proofFile->isPlainFileChecked ?
                // Plain Fileの場合は、ファイル名をそのまま使う
                $proofFile->name :
                // それ以外の場合は、Proofのタイトルをファイル名に使う
                Str::of($proof->title)
                ->prepend("{$proof_no}_")
                ->replaceMatches('/\//', '_')
                ->append(".{$proofFile->extension}")
                ->__toString();

            return $fileBaseName;
        };

        $file = $this->binaryToUploadedFileAction->execute(
            fileBaseName: $getFileBaseName($proof, $proofFile),
            binary: ProofFileManager::getContentFromS3($proofFile->path)
        );

        return $this->createProofFileAndUploadAction->execute(
            proof: $proof,
            file: $file,
            startPath: config('filesystems.disks.s3.shared_dir'),
            type: ProofFile::TYPE_SHARED
        );
    }

    private function runRecommendBatch(
        Proof $proof,
        RequestFormFilesData $files,
        RequestFormPublishDateData $publishDate
    ): void {
        Log::info("リコメンドBatch生成", [$proof->id]);
        Log::info("リコメンドBatch生成", [$proof->title]);
        $batch = $this->createRecommendBatchAction
            ->execute(
                proof: $proof,
                files: $files,
                publishDate: $publishDate,
            )
            ->then(function (Batch $batch) use ($proof) {
                Log::info("リコメンドBatch成功", [$proof->id]);
                Log::info("リコメンドBatch成功", [$proof->title]);

            })
            ->finally(function (Batch $batch) use ($proof) {

                Log::info("リコメンドBatch完了", [$proof->id]);
                Log::info("リコメンドBatch完了", [$proof->title]);

                Log::info("バッチ後の受付メール送信", [$proof->id]);
                if ($admins = User::ofUserType(User::USER_TYPE_ADMIN)->get()) {
                    Mail::to($admins)->send(new ProofRequestedToAdminMail($proof));
                }

                $userIps = User::query()->where(function (Builder $query) use ($proof) {
                    $query
                        ->orWhereHasIps($proof->ips->pluck('id')->toArray())
                        ->orWhereHasCategories($proof->category->id);
                })->get();
                $userIds = $userIps->map(function ($user) {
                    return $user->id;
                })->toArray();
                $mailList = User::where('user_type_id', User::USER_TYPE_MEMBER)->whereNotIn('id', $userIds)->get();

                Log::info("メールリスト事務局メンバー：", [$mailList]);
                if ($mailList->isNotEmpty()) {
                    $mailList->each(function ($user) use ($proof) {
                        Log::info("サポーター：", [$user]);
                        Log::info("ユーザータイプ", [$user->user_type_id]);
                        Mail::to($user)->send(new ProofRequestedToNotRequiredUserMail($proof, $user));
                    });
                }
                /**
                 * https://laravel.com/docs/8.x/queues
                 *
                 * Since batch callbacks are serialized and executed at a later time by the Laravel queue,
                 * you should not use the $this variable within the callbacks.
                 *
                 * 「$this」変数をコールバック内で使用できない為、直接クラス名を指定して実行する。
                 */
                (new AutoAssignmentMembersAction(new AssignmentMembersToProofAction))->execute($proof);

                /**
                 * https://tech-flag.atlassian.net/wiki/spaces/BAPI/pages/2054586369
                 *
                 * 依頼者が新規で倫理確認の申請をし、対象の申請のジョブがすべて完了した時
                 *（ジョブでエラーが起きていても送信する）
                 */
                // Mail::to($proof->user)->send(new ProofRequestedToRequesterMail($proof));
            })
            ->dispatch();

        $proof->update(['job_batch_id' => $batch->id]);
    }

    /**
     * 動画がある場合はリコメンドバッチを処理せず、メール送信のみ実行する
     * @param Proof $proof
     *
     * @return void
     */
    private function handleIfHasVideo(Proof $proof): void
    {
        Log::info("動画がある場合はリコメンドバッチを処理せず、メール送信のみ実行する");
        /**
         * 管理者へ受付メール（未了）
         */
        /**
        * @var User $admins
         */
        $admins = User::ofUserType(User::USER_TYPE_ADMIN)->get();
        if ($admins->isNotEmpty()) {
            /**
             * @var User $admins
             */
            Log::info("管理者へ受付メール（未了）", [$admins]);
            $admins->each(function ($admin) use ($proof) {
                Log::info("管理者へ受付メール（未了）", [$admin]);
                Log::info("ユーザータイプ", [$admin->user_type_id]);
                Mail::to($admin)->send(new ProofRequestHasVideoToAdminMail($proof, $admin));
            });
        }
    }

    private function mailSends(Proof $proof): void{
        if ($admins = User::ofUserType(User::USER_TYPE_ADMIN)->get()) {
            Mail::to($admins)->send(new ProofRequestedToAdminMail($proof));
        }

        $userIps = User::query()->where(function (Builder $query) use ($proof) {
            $query
                ->orWhereHasIps($proof->ips->pluck('id')->toArray())
                ->orWhereHasCategories($proof->category->id);
        })->get();
        $userIds = $userIps->map(function ($user) {
            return $user->id;
        })->toArray();
        $adminMailList = User::where('user_type_id', User::USER_TYPE_ADMIN)->whereNotIn('id', $userIds)->get();
        $mailList = User::where('user_type_id',User::USER_TYPE_MEMBER)->whereNotIn('id', $userIds)->get();
        Log::info("メールリスト管理者：", [$adminMailList]);
        if ($adminMailList->isNotEmpty()) {
            $adminMailList->each(function ($user) use ($proof) {
                Log::info("サポーター：", [$user]);
                Log::info("ユーザータイプ", [$user->user_type_id]);
                Mail::to($user)->send(new ProofRequestedToNotRequiredUserMail($proof, $user));
            });
        }
        Log::info("メールリスト事務局メンバー：", [$mailList]);
        if ($mailList->isNotEmpty()) {
            $mailList->each(function ($user) use ($proof) {
                Log::info("サポーター：", [$user]);
                Log::info("ユーザータイプ", [$user->user_type_id]);
                Mail::to($user)->send(new ProofRequestedToNotRequiredUserMail($proof, $user));
            });
        }
    }
}
