<?php

namespace App\Services\FunctionHandlers;

class GetCurrentPageInfoHandler
{
    /**
     * Get current page information and available features
     */
    public function execute(array $arguments): array
    {
        $page = $arguments['page'] ?? 'ホーム';

        return [
            'success' => true,
            'page' => $page,
            'features' => $this->getPageFeatures($page)
        ];
    }

    /**
     * Get features available on a specific page
     */
    private function getPageFeatures(string $page): array
    {
        $features = [
            'ホーム' => ['倫理確認システムへの移動', 'カードチェックへの移動', 'システム概要'],
            '倫理確認システム' => ['文書アップロード', '倫理チェック実行', '結果確認'],
            'カードチェック' => ['カードファイルアップロード', '仕様チェック', 'エラー修正'],
            '申請フォーム' => ['PDFアップロード', '動画アップロード', '元素材アップロード'],
            '申請履歴' => ['申請状況確認', '修正・再提出', 'ダウンロード']
        ];

        return $features[$page] ?? [];
    }
}