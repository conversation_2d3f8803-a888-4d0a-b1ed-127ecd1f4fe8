<?php

namespace App\Actions\Annotations;

use App\Data\ImageAlertDetectedObjectItemData;
use App\Data\ImageAlertPositiveItemData;
use App\Data\ImageAlertAlertedImageData;
use App\Data\PointData;
use App\ValueObjects\Color;
use App\ValueObjects\Contracts\Annotation;
use App\ValueObjects\Coordinate;
use App\ValueObjects\FreeTextAnnotation;
use App\ValueObjects\SquareAnnotation;
use App\ValueObjects\StickyNoteAnnotation;
use Illuminate\Support\Collection;
use Spatie\LaravelData\DataCollection;

class GenerateImageAlertAnnotationsAction
{
    /**
     * 画像アラートのアノテーションを生成
     * @var ImageAlertAlertedImageData[] $predicts
     *
     * @return array<string>
     */
    public function execute(DataCollection $alertedImages): array
    {
        $generateAlertedImageAnnotation = function (ImageAlertAlertedImageData $alertedImage) {
            $generatePositiveItemAnnotation = fn () =>
                fn (ImageAlertPositiveItemData $positiveItem) => $this->getPositiveItemAnnotation(
                    $alertedImage,
                    $positiveItem
                );

            return $alertedImage
                    ->positiveItems
                    ->toCollection()
                    ->map($generatePositiveItemAnnotation($alertedImage));
        };

        $toXml = fn (Annotation $annotation) => $annotation->xml();

        return $alertedImages
            ->toCollection()
            ->flatMap($generateAlertedImageAnnotation)
            ->flatten()
            ->map($toXml)
            ->toArray();
    }

    private function getPositiveItemAnnotation(
        ImageAlertAlertedImageData $alertedImage,
        ImageAlertPositiveItemData $positiveItem,
    ) {
        $color = $this->generateRandomColor();

        $generateDetectedObjectAnnotation = function (ImageAlertDetectedObjectItemData $detectedObject) use (
            $alertedImage,
            $positiveItem,
            $color
        ) {
            $square = $this->bboxAnnotation($alertedImage, $positiveItem, $detectedObject, $color);
            $freeText = $this->keywordAnnotation($alertedImage, $positiveItem, $detectedObject, $color);

            return $this->annotationGrouping(
                refAnnotation: $square,
                annotations: [$freeText]
            );
        };

        return $positiveItem
                ->detected_object_list
                ->toCollection()
                ->map($generateDetectedObjectAnnotation);
    }

    private function keywordAnnotation(
        ImageAlertAlertedImageData $alertedImage,
        ImageAlertPositiveItemData $positiveItem,
        ImageAlertDetectedObjectItemData $detectedObject,
        Color $color,
    ): FreeTextAnnotation {
        $pts = (function (
            string $text,
            Coordinate $pts,
        ) {
            $height = 16;
            $width = mb_strlen($text) * 7 + $height;

            return new Coordinate(
                leftTop: $pts->leftTop,
                rightTop: PointData::from([
                    'x' => $pts->leftTop->x + $width,
                    'y' => $pts->leftTop->y
                ]),
                rightBottom: PointData::from([
                    'x' => $pts->leftTop->x + $width,
                    'y' => $pts->leftTop->y + $height
                ]),
                leftBottom: PointData::from([
                    'x' => $pts->leftTop->x,
                    'y' => $pts->leftTop->y + $height
                ]),
            );
        })($positiveItem->keyword, $detectedObject->polygon);

        return new FreeTextAnnotation(
            page: $alertedImage->page,
            pts: $pts,
            comment: $positiveItem->keyword,
            backgroundColor: $color->css(),
            strokeColor: $color->css(),
            textColor: '#FFFFFF',
        );
    }

    private function bboxAnnotation(
        ImageAlertAlertedImageData $alertedImage,
        ImageAlertPositiveItemData $positiveItem,
        ImageAlertDetectedObjectItemData $detectedObject,
        Color $color,
        float $opacity = 1
    ): SquareAnnotation {
        return new SquareAnnotation(
            page: $alertedImage->page,
            pts: $detectedObject->polygon,
            comment: <<<COMMENT
            【AI】
            {$positiveItem->keyword}

            {$positiveItem->answer}
            COMMENT,
            color: $color->css(),
            opacity: $opacity
        );
    }

    /**
     * 注釈をグループ化
     */
    private function annotationGrouping(Annotation $refAnnotation, array|Collection $annotations): Collection
    {
        $applyGrouping = fn (Annotation $annotation) => $annotation->grouping($refAnnotation);

        return collect($annotations)
                ->map($applyGrouping)
                ->push($refAnnotation);
    }

    /**
     * ランダムカラーコードを生成
     *
     * @return Color
     */
    private function generateRandomColor(): Color
    {
        $MIN = 0;
        $MAX = 255;

        return new Color(
            red: random_int($MIN, $MAX),
            green: random_int($MIN, $MAX),
            blue: random_int($MIN, $MAX)
        );
    }
}
