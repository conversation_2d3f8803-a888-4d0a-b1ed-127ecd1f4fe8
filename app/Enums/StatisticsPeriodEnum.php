<?php

namespace App\Enums;

use App\Models\Proof;
use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;
use Carbon\Carbon;
use Carbon\CarbonPeriod;

/**
 * @method static static OptionOne()
 * @method static static OptionTwo()
 * @method static static OptionThree()
 */
final class StatisticsPeriodEnum extends Enum implements LocalizedEnum
{
    // const LatestWeek            = 'latest_week';
    // const LatestFourWeeks       = 'latest_four_weeks';
    // const LatestMonth           = 'latest_month';
    // const LatestThreeMonths     = 'latest_three_months';
    // const LatestSixMonths       = 'latest_six_months';
    // const LatestYear            = 'latest_year';
    // const LatestThreeYears      = 'latest_three_years';
    // const All                   = 'all';

    // public static function getDescription($value): string
    // {
    //     return match ($value) {
    //         self::LatestWeek        => '過去1週間',
    //         self::LatestFourWeeks   => '過去4週間',
    //         self::LatestMonth       => '過去1カ月間',
    //         self::LatestThreeMonths => '過去3カ月間',
    //         self::LatestSixMonths   => '過去6カ月間',
    //         self::LatestYear        => '過去1年間',
    //         self::LatestThreeYears  => '過去3年間',
    //         default => parent::getDescription($value)
    //     };
    // }

    // public function toArray()
    // {
    //     return $this;
    // }

    // public function range(...$attributes)
    // {

    //     return match ($this->value) {
    //         self::LatestWeek => CarbonPeriod::create(
    //             now()->subWeek()->addDay()->startOfDay(),
    //             now()->endOfDay(),
    //         ),

    //         self::LatestFourWeeks => CarbonPeriod::create(
    //             now()->subWeeks(4)->addDay()->startOfDay(),
    //             now()->endOfDay(),
    //         ),

    //         self::LatestMonth => CarbonPeriod::create(
    //             now()->subMonth()->addDay()->startOfDay(),
    //             now()->endOfDay(),
    //         ),

    //         self::LatestThreeMonths => CarbonPeriod::create(
    //             now()->subMonths(3)->addDay()->startOfDay(),
    //             now()->endOfDay(),
    //         ),

    //         self::LatestSixMonths => CarbonPeriod::create(
    //             now()->subMonths(6)->addDay()->startOfDay(),
    //             now()->endOfDay(),
    //         ),

    //         self::LatestYear => CarbonPeriod::create(
    //             now()->subYear()->addDay()->startOfDay(),
    //             now()->endOfDay(),
    //         ),

    //         self::LatestThreeYears => CarbonPeriod::create(
    //             now()->subYears(3)->addDay()->startOfDay(),
    //             now()->endOfDay(),
    //         ),
    //     };
    // }
}
