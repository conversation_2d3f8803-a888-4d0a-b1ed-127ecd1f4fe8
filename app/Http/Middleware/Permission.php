<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class Permission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $role)
    {
        if (strtolower(auth()->user()->type_value) !== strtolower($role)) {
            if (strtolower(auth()->user()->user_role) == 'admin_role') {
                Log::info('admin_roleの時');
                Log::info('user_roleの値: ' . auth()->user()->user_role);
                Log::info('requestのrouteの値: ' . $request->route()->getName());
                // admin.のルートは全て許可
                if (str_starts_with($request->route()->getName(), 'admin.') || $request->route()->getName() == 'requester.proofs.archive') {
                    return $next($request);
                }else{
                    abort(403);
                }
            } elseif (strtolower(auth()->user()->type_value) == 'member') {
            // サポーターの時
                   $request->route()->getName() == 'admin.utils.video-to-image.index'
                || $request->route()->getName() == 'admin.utils.video-to-image.show'
                || $request->route()->getName() == 'admin.utils.video-to-image.extracts'
                || $request->route()->getName() == 'admin.logs.batch'
                || $request->route()->getName() == 'requester.proofs.archive'
                ? null : abort(403);
            }elseif(strtolower(auth()->user()->type_value) == 'viewer'){
                $request->route()->getName() == 'requester.proofs.origin-download' ? null : abort(403);
            }else{
                abort(403);
            }
        }

        return $next($request);
    }
}
