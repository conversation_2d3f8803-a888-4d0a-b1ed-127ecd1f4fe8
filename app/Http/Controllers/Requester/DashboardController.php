<?php

namespace App\Http\Controllers\Requester;

use App\Actions\GetNoticeAction;
use App\Actions\Proof\GetProofCategoriesCountAction;
use App\Data\GetNoticeData;
use App\Data\ProofCategoryCountData;
use App\Http\Controllers\Controller;
use App\Models\Proof;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index(
        GetNoticeAction $getNoticeAction,
        GetProofCategoriesCountAction $categoriesCountAction
    ) {
        $notices = $getNoticeAction->execute(
            GetNoticeData::from(['user_type' => auth()->user()->type])
        );

        $proofs = Proof::with(['business_categories'])
            ->whereUserId(auth()->id())
            ->orderByDesc('id')
            ->limit(5)
            ->get();

        $categoriesCount = $categoriesCountAction
            ->execute(
                filters: [
                    'proof' => function (Builder $query) {
                        $query->where('proofs.user_id', auth()->id());
                    },
                ],
            )
            ->then(
                fn(Collection $values) =>
                $values->map(fn(ProofCategoryCountData $value) => [
                    ...$value->toArray(),
                    'label' => $value->name,
                ])
            );

        return Inertia::render('Requester/Dashboard/AicIndex', [
            'notices' => $notices,
            'proofs' => $proofs,
            'categoriesCount' => $categoriesCount
        ]);
    }
}
