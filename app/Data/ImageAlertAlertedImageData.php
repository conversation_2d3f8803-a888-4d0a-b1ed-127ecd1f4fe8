<?php

namespace App\Data;

use App\ValueObjects\Coordinate;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\DataCollection;

class ImageAlertAlertedImageData extends Data
{
    public function __construct(
        public PdfPageInfoData $page,
        public Coordinate $imageCoords,
        public string $image_path_s3,
        public int $img_width,
        public int $img_height,
        /** @var ImageAlertPositiveItemData[] */
        public DataCollection $positiveItems,
    ) {
    }

    public static function fromArray(array $alertedImage): static
    {
        $imageCoords = Coordinate::fromPoints($alertedImage['image_coords']);

        return new static(
            page: PdfPageInfoData::from([
                'num' => $alertedImage['page'],
                'height' => $alertedImage['page_height'],
                'width' => $alertedImage['page_width'],
                'scale' => 1,
            ]),
            imageCoords: $imageCoords,
            image_path_s3: $alertedImage['image_path_s3'],
            img_width: $alertedImage['img_width'],
            img_height: $alertedImage['img_height'],
            positiveItems: ImageAlertPositiveItemData::collection(
                collect($alertedImage['positive_items'])->map(
                    fn (array $positiveItem) => self::createPositiveItem($positiveItem, $imageCoords)
                )
            )
        );
    }

    private static function createPositiveItem(array $positiveItem, Coordinate $imageCoords)
    {
        return ImageAlertPositiveItemData::from([
            ...$positiveItem,
            'detected_object_list' => ImageAlertDetectedObjectItemData::collection(
                collect($positiveItem['detected_object_list'])->map(
                    fn (array $detectedObject) => self::createDetectedObject($detectedObject, $imageCoords)
                )
            )
        ]);
    }

    private static function createDetectedObject(array $detectedObject, Coordinate $imageCoords)
    {
        return ImageAlertDetectedObjectItemData::from([
            'text' => $detectedObject['text'],
            'polygon' => $imageCoords->calcWithRatios(
                Coordinate::fromPoints($detectedObject['polygon'])
            )
        ]);
    }
}
