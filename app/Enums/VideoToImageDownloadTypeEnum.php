<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static General()
 * @method static static Legal()
 */
final class VideoToImageDownloadTypeEnum extends Enum
{
    const Zip   =   'zip';
    const Pdf   =   'pdf';

    public static function getDescription($value): string
    {
        return match ($value) {
            self::Zip => '圧縮ファイル(zip)',
            self::Pdf => 'PDF',
            default => parent::getDescription($value)
        };
    }

    public function toArray()
    {
        return $this;
    }
}
