<template>
  <v-pagination
    class="my-3 pb-3"
    :value="paginator.current_page"
    :length="paginator.last_page"
    :total-visible="paginator.per_page"
    @input="link"
  />
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    paginator: {
      type: Object,
      required: true,
    },
  },
  methods: {
    link(page) {
      const url = new URL(location.href);
      url.searchParams.set('page', page);

      const path = url.href;

      this.$inertia.get(path);
    },
  },
};
</script>

<style></style>
