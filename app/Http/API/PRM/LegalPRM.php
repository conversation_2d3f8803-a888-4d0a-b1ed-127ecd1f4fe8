<?php

namespace App\Http\API\PRM;

use App\Http\API\PRM\Contracts\PRM;
use Exception;

/**
 * BAPI関連法律事例リコメンドAPI
 * Dockerベース
 */
class LegalPRM extends PRM
{
    public function predict(string $text): array
    {
        $response = $this->client->post('/file-legal-class/predict/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
            'text' => $text,
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "関連法律ファイル分類予測API: {$this->message}",
                    $this->code
                );
            }

            return $response->json();
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function annotationSamples(): array
    {
        $response = $this->client->post('/file-legal-class/annotation-sample/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "関連法律注釈の代表例の取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['annotations'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    public function annotations(string|int $class_no): array
    {
        $response = $this->client->post('/file-legal-class/annotations/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
            'class_no' => $class_no,
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "関連法律おすすめの注釈の取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['annotations'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }
}
