<?php

namespace App\Http\Controllers\Downloads;

use App\Actions\ClosedPdfDownloadAction;
use App\Data\ClosedPdfDownloadData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Download\ClosedPdfDownloadRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ClosedPdfDownloadController extends Controller
{
    public function download(
        Request $request,
        ClosedPdfDownloadAction $action
    ){
        Log::info('ClosedPdfDownloadController');
        Log::info($request->all());
        return  $action->execute(ClosedPdfDownloadData::fromRequest($request));
    }

}
