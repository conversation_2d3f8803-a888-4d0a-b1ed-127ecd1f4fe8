<?php

namespace App\Http\Requests\Admin\VideoToImageController;

use App\Data\ShowVideoToImagesData;
use Illuminate\Foundation\Http\FormRequest;
use App\Enums\VideoToImageIntervalEnum as IntervalEnum;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ShowRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'time' => [
                'nullable',
                'numeric'
            ],
            'video_path' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if(!Storage::disk('s3')->exists($value)) {
                        return $fail("{$attribute}: Not exists {$value} in S3");
                    }
                },
            ],
            'images_dir' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if(empty(Storage::disk('s3')->files($value))) {
                        return $fail("{$attribute}: {$value} is empty in S3");
                    }
                },
            ],
            'interval_type' => [
                'nullable',
                Rule::in(IntervalEnum::getValues())
            ],
            'bucket' => [
                'required',
                'string',
            ],
        ];
    }

    public function data(): ShowVideoToImagesData
    {
        return ShowVideoToImagesData::from([
            ...$this->all(),
            'interval_type' => IntervalEnum::fromValue((float)$this->interval_type),
        ]);
    }
}
