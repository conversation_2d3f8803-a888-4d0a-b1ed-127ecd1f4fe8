<?php

namespace App\Actions\Proof\Category;

use App\Data\UpsertIntellectualPropertyData;
use App\Models\IntellectualProperty;

class UpsertIntellectualPropertyAction
{
    public function execute(UpsertIntellectualPropertyData $data): IntellectualProperty
    {
        return IntellectualProperty::updateOrCreate([
            'id' => $data->id
        ], [
            'name' => $data->name,
            'parent_id' => $data->parent_id
        ]);
    }
}
