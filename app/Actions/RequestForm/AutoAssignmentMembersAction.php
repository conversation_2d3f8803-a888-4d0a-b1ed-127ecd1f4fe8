<?php

namespace App\Actions\RequestForm;

use App\Enums\ProofNotificationTypeEnum;
use App\Events\ProofAssignmented;
use App\Models\Proof;
use App\Models\ProofStatus;
use App\Models\User;
use App\Notifications\ProofNotification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Notification;

class AutoAssignmentMembersAction
{
    public function __construct(
        private AssignmentMembersToProofAction $assignmentMembersToProofAction
    ) {}
    /**
     * @param Proof $proof
     */
    public function execute(Proof $proof): Proof
    {
        $members = $this->assignmentMembersToProofAction->execute($proof);

        $proof->status()->associate(
            ProofStatus::ofValue(ProofStatus::STATUS_PROGRESSING)->first()
        );
        $proof->save();

        // アサイン完了イベント
        event(new ProofAssignmented($proof));

        // 事務局メンバーの未読追加
        Notification::send(
            $members,
            new ProofNotification($proof, ProofNotificationTypeEnum::New)
        );

        return $proof;
    }
}
