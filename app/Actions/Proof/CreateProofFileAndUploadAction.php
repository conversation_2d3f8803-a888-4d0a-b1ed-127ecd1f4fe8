<?php

namespace App\Actions\Proof;

use App\Helpers\EthicsConfirmAssistantHelper;
use App\Helpers\ProofFileManager;
use App\Models\Proof;
use App\Models\ProofFile;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

/**
 * ファイルをアップロードしてProofFileを生成する。
 */
class CreateProofFileAndUploadAction
{
    /**
     * @param Proof $proof
     * @param array|Collection<int, UploadedFile> $files
     * @param string $startPath
     * @param string $type \App\Models\ProofFileの中にある
     */
    public function execute(
        Proof $proof,
        UploadedFile $file,
        string $startPath,
        string $type = ProofFile::TYPE_ORIGIN
    ): ProofFile {
        $path = EthicsConfirmAssistantHelper::getRequestFormFilePath(
            $proof,
            $file->getClientOriginalName(),
            $startPath
        );

        ProofFileManager::uploadS3($path, $file->getContent());

        return $proof->files()->create([
            'name' => $file->getClientOriginalName(),
            'dir' => dirname($path),
            'extension' => $file->extension(),
            'size' => $file->getSize(),
            'type' => $type,
            'last_modified' => Carbon::createFromTimestamp($file->getCTime()),
        ]);
    }
}
