<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Proof\ChangeProofExternalDeadlineAt;
use App\Actions\Proof\ChangeProofInternalDeadlineAt;
use App\Actions\Proof\CreateRecommendBatchActionV2;
use App\Actions\Proof\MarkProofNotificationsAsRead;
use App\Actions\Proof\SyncProofCategories;
use App\Actions\Proof\SyncProofCategory;
use App\Actions\Proof\ToggleProofCompletionMarkAction;
use App\Actions\RequestForm\AssignmentMembersToProofAction;
use App\Actions\RequestForm\AutoAssignmentMembersAction;
use App\Data\RequestFormFilesData;
use App\Data\RequestFormPublishDateData;
use App\Enums\ProofNotificationTypeEnum;
use App\Enums\RequestFormFileTypeEnum;
use App\Events\ProofClosed;
use App\Events\ProofRejected;
use App\Helpers\ProofFileManager;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProofController\CloseRequest;
use App\Http\Requests\Admin\ProofController\ForceUploadRequest;
use App\Http\Requests\Admin\ProofController\UpdateRequest;
use App\Jobs\JobUploadCloudSearch;
use App\Mail\ProofAssignmentedToRequiredUserMail;
use App\Mail\ProofRequestedToAdminMail;
use App\Mail\ProofRequestedToRequesterMail;
use App\Models\AnnotationTemplate;
use App\Models\Category;
use App\Models\IntellectualProperty;
use App\Models\Proof;
use App\Models\ProofComment;
use App\Models\ProofFile;
use App\Models\ProofLanguage;
use App\Models\ProofStatus;
use App\Models\User;
use App\Models\ProofDeadLine;
use App\Notifications\ProofNotification;
use Exception;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Imagick;
use ZipArchive;
use App\Models\ProofsCategories;
use App\Models\Holiday;
use App\Models\ProofNumber;

class ProofController extends Controller
{
    public function __construct(
        private ChangeProofExternalDeadlineAt $changeProofExternalDeadlineAtAction,
        private ChangeProofInternalDeadlineAt $changeProofInternalDeadlineAtAction
    ) {}

    /**
     * 倫理確認申請リスト
     */
    public function index()
    {
        $paginator = Proof::filter(request()->all())
            ->with(['batch', 'completionMarks'])
            ->orderByDesc('created_at')
            ->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $paginator
            ->each(fn(Proof $proof) => $proof->append('capable_completion_mark_users'));

        $proofStatuses = ProofStatus::all();
        $requesters = User::has('proofs')->get();

        return Inertia::render('Admin/Proof/Index', [
            'paginator' => $paginator,
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'categories' => Category::all(),
            'proofStatuses' => $proofStatuses,
            'requesters' => $requesters
        ]);
    }

    /**
     * 新UI倫理確認申請リスト
     */
    public function newIndex()
    {
        Log::info('newIndex');
        $domain = User::getCompanyDomain(auth()->user());
        Log::info('domain', ['domain' => $domain]);
        $companyCode = ProofNumber::getCompanyCode($domain);
        if($companyCode != 'BNXP') {
            return Inertia::render('Aic/CloseProofIndex');
        }
        Log::info(request()->all());
        if (request()->has('proof_number') && request()->proof_number != null) {
            // proof_numberが5桁未満なら先頭から0を埋める
            $proof_number = request()->proof_number;
            if (strlen($proof_number) < 5) {
                $proof_number = str_pad($proof_number, 5, '0', STR_PAD_LEFT);
            }
            // company_codeを付ける
            $proof_number = request()->company_code . '-' . $proof_number;
            request()->merge(['proof_number' => $proof_number]);
        }

        $query = Proof::filter(request()->all())
            ->with(['batch', 'completionMarks'])
            ->orderByDesc('created_at');

        if (request()->has('checkedOnly') && request()->checkedOnly == "true") {
            $userId = auth()->id();
            $query->whereHas('completionMarks', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            });
        }

        if (request()->has('unCheckedOnly') && request()->unCheckedOnly == "true") {
            $userId = auth()->id();
            $query->whereDoesntHave('completionMarks', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            });
        }

        if (request()->has('my_special_sup_only') && request()->my_special_sup_only == "true") {
            Log::info('my_special_sup_only');
            $proof_ids = [];
            if(auth()->user()->ips->toArray() != null) {
                $ips = auth()->user()->ips->pluck('id')->toArray();
                $ips = IntellectualProperty::whereIn('id', $ips)->get();
                $proofIps = DB::table('proofs_intellectual_properties')
                    ->whereIn('intellectual_property_id', $ips->pluck('id')->toArray())
                    ->get();
                $proof_ids = $proofIps->pluck('proof_id')->toArray();
            }

            if(auth()->user()->categories->toArray() != null) {
                $categories = auth()->user()->categories->pluck('id')->toArray();
                $categories = Category::whereIn('id', $categories)->get();
                $proofCategories = ProofsCategories::whereIn('category_id', $categories->pluck('id')->toArray())->get();
                $proof_ids = array_merge($proof_ids, $proofCategories->pluck('proof_id')->toArray());
            }

            $query->whereIn('id', $proof_ids);
        }

        if(request()->has('xp_only') && request()->xp_only == "true") {
            // proofs.proof_numberがBNXP-から始まるものを取得
            $query->where('proof_number', 'like', 'BNXP-%');
        }

        if(request()->has('am_only') && request()->am_only == "true") {
            // proofs.proof_numberがBNAM-から始まるものを取得
            $query->where('proof_number', 'like', 'BNAM-%');
        }

        if(request()->has('al_only') && request()->al_only == "true") {
            // proofs.proof_numberがBNAL-から始まるものを取得
            $query->where('proof_number', 'like', 'BNAL-%');
        }

        Log::info('query', ['query' => $query->toSql()]);
        Log::info('query_bindings', ['query_bindings' => $query->getBindings()]);

        Log::info('page', ['page' => request()->except(['page', '_token'])]);

        $paginator = $query->take(self::CHECK_ITEM)->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $paginator
            ->each(fn(Proof $proof) => $proof->append('capable_completion_mark_users'));


        $proofStatuses = ProofStatus::where('value', '!=', ProofStatus::STATUS_REJECT)
            ->where('value', '!=', ProofStatus::STATUS_ASSIGNMENT)
            ->get();

        // 依頼中の文字をPDF化待ちに変更
        $proofStatuses = $proofStatuses->map(function ($proofStatus) {
            if ($proofStatus->value === ProofStatus::STATUS_REQUESTING) {
                $proofStatus->text = 'PDF化待ち';
            }
            return $proofStatus;
        });

        $requesters = User::has('proofs')->get();

        // 今月の休日を取得
        $today = now();
        $startOfMonth = $today->copy()->startOfMonth();
        $endOfMonth = $today->copy()->endOfMonth();
        $holidays = [];
        $holidays = Holiday::where('date', '>=', $startOfMonth)
            ->where('date', '<=', $endOfMonth)
            ->get();

        return Inertia::render('Admin/Proof/NewIndex', [
            'paginator' => $paginator,
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'categories' => Category::all(),
            'proofStatuses' => $proofStatuses,
            'requesters' => $requesters,
            'user' => auth()->user(),
            'holidays' => $holidays->toArray(),
            'companyCode' => ProofNumber::select('company_code')->get()->pluck('company_code')->toArray(),
        ]);
    }

    public function archive()
    {
        if (request()->has('proof_number') && request()->proof_number != null) {
            // proof_numberが5桁未満なら先頭から0を埋める
            $proof_number = request()->proof_number;
            if (strlen($proof_number) < 5) {
                $proof_number = str_pad($proof_number, 5, '0', STR_PAD_LEFT);
            }
            // company_codeを付ける
            $proof_number = request()->company_code . '-' . $proof_number;
            request()->merge(['proof_number' => $proof_number]);
        }

        $query = Proof::filter(request()->all())
            ->where('proof_status_id', ProofStatus::ofValue(ProofStatus::STATUS_CLOSED)->first()->id)
            ->orderByDesc('created_at');

        if (request()->has('checkedOnly') && request()->checkedOnly) {
            $userId = auth()->id();
            $query->whereHas('completionMarks', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            });
        }

        if (request()->has('xp_only') && request()->xp_only == "true") {
            // proofs.proof_numberがBNXP-から始まるものを取得
            $query->where('proof_number', 'like', 'BNXP-%');
        }

        if (request()->has('am_only') && request()->am_only == "true") {
            // proofs.proof_numberがBNAM-から始まるものを取得
            $query->where('proof_number', 'like', 'BNAM-%');
        }

        if (request()->has('al_only') && request()->al_only == "true") {
            // proofs.proof_numberがBNAL-から始まるものを取得
            $query->where('proof_number', 'like', 'BNAL-%');
        }

        $paginator = $query->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $paginator
            ->each(fn(Proof $proof) => $proof->append('capable_completion_mark_users'));


        $proofStatuses = ProofStatus::where('value', '!=', ProofStatus::STATUS_REJECT)
            ->where('value', '!=', ProofStatus::STATUS_ASSIGNMENT)
            ->get();
        $requesters = User::has('proofs')->get();

        $closedProofFiles = ProofFile::whereIn('type', [ProofFile::TYPE_CLOSED, ProofFile::TYPE_CONVERTED])->get();
        $paginator->each(function ($proof) use ($closedProofFiles) {
            // $proofのidをもつファイルでnameに_gendocがはいっていないものを取得
            $proof->convertFile = $closedProofFiles->filter(function ($closedProofFile) use ($proof) {
                return $closedProofFile->proof_id === $proof->id && strpos($closedProofFile->name, '_gendoc') === false && $closedProofFile->type === 'converted';
            })->values();

            $proof->closedFile = $closedProofFiles->filter(function ($closedProofFile) use ($proof) {
                return $closedProofFile->proof_id === $proof->id && $closedProofFile->type === 'closed';
            })->values();
        });

        return Inertia::render('Admin/Proof/Archive', [
            'paginator' => $paginator,
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'categories' => Category::all(),
            'proofStatuses' => $proofStatuses,
            'requesters' => $requesters,
            'user' => auth()->user(),
            'companyCode' => ProofNumber::select('company_code')->get()->pluck('company_code')->toArray(),
        ]);
    }


    public function show(
        Proof $proof,
        MarkProofNotificationsAsRead $markProofNotificationAsReadAction
    ) {
        $proof->load([
            'comments',
            'origin',
            'converted',
            'category',
            'completionMarks.user',
            'ips',
            'members'
        ]);

        $proof->status->value == ProofStatus::STATUS_CLOSED ?
            $proof->load('closed') :
            $proof->load('shared');

        $proof->append('uuid_display');
        $proof->append('capable_completion_mark_users');

        $markProofNotificationAsReadAction->execute([auth()->user()], $proof);

        return Inertia::render('Admin/Proof/Show', [
            'proof' => $proof,
            'categories' => Category::all(),
            'proofLanguages' => ProofLanguage::all(),
            'members' => User::member()->get(),
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'externalAllowedTime' => ProofDeadLine::getAllowedTime(true),
            'internalAllowedTime' => ProofDeadLine::getAllowedTime(false)
        ]);
    }

    public function newShow(
        Proof $proof,
        MarkProofNotificationsAsRead $markProofNotificationAsReadAction
    ) {
        $proof->load([
            'comments',
            'origin',
            'converted',
            'category',
            'completionMarks.user',
            'ips',
            'members',
            'batch'
        ]);

        $proof->status->value == ProofStatus::STATUS_CLOSED ?
            $proof->load('closed') :
            $proof->load('shared');

        $proof->append('uuid_display');
        $proof->append('capable_completion_mark_users');

        $proofFiles = [];
        if ($proof->status->value == ProofStatus::STATUS_CLOSED) {
            $proofFiles = ProofFile::where('proof_id', $proof->id)
                ->whereIn('extension', ['pdf'])
                ->whereIn('type', ['closed'])
                ->get()->toArray();
        } elseif ($proof->status->value == ProofStatus::STATUS_PROGRESSING) {
            $proofFiles = ProofFile::where('proof_id', $proof->id)
                ->whereIn('extension', ['pdf'])
                ->whereIn('type', ['shared'])
                ->get()->toArray();
        }

        if ($proof->status->value == ProofStatus::STATUS_REQUESTING) {
            $proofFiles = ProofFile::where('proof_id', $proof->id)
                ->whereIn('extension', ['mp4', 'm4v', 'wmv', 'avi', 'mpeg', 'mkv', 'pdf'])
                ->whereIn('type', ['origin'])
                ->get()->toArray();

            $gendocFile = ProofFile::where('proof_id', $proof->id)
                ->where('name', 'like', '%_gendoc%')
                ->get()->toArray();

            $proofFiles = array_merge($proofFiles, $gendocFile);
        }

        if (count($proofFiles) > 1) {
            // proofFilesのsharedを_gendocが付いていない、_gendocが付いているものに並び替える
            $proofFiles = array_merge(
                array_filter($proofFiles, function ($proofFile) {
                    return strpos($proofFile['name'], '_gendoc') === false;
                }),
                array_filter($proofFiles, function ($proofFile) {
                    return strpos($proofFile['name'], '_gendoc') !== false;
                })
            );
        }

        Log::info($proofFiles);


        $humanCount = [];
        $aiCount = [];
        foreach ($proofFiles as $key => $proofFile) {
            $humanCount[$proofFile['id']] = $this->annotationCount(ProofFile::find($proofFile['id']));
            $aiCount[$proofFile['id']] = $this->annotationCount(ProofFile::find($proofFile['id']));
        }

        $markProofNotificationAsReadAction->execute([auth()->user()], $proof);

        return Inertia::render('Admin/Proof/AicProofShow', [
            'proof' => $proof,
            'proofFiles' => $proofFiles,
            'categories' => Category::all(),
            'proofLanguages' => ProofLanguage::all(),
            'members' => User::member()->get(),
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'externalAllowedTime' => ProofDeadLine::getAllowedTime(true),
            'internalAllowedTime' => ProofDeadLine::getAllowedTime(false),
            'user' => auth()->user(),
            'humanCount' => $humanCount,
            'aiCount' => $aiCount,
            'mailFrom' => config('mail.from.address'),
            'specialMembers' => User::SPECIAL_USER_MAIL_LIST,
        ]);
    }

    public function annotationCount(ProofFile $proofFile)
    {
        $targetFile = ProofFile::where('id', $proofFile->id)->first();
        if (is_null($targetFile)) {
            return [
                'humanCount' => 0,
                'aiCount' => 0
            ];
        }

        $textCheck = 0;
        $dateCheck = 0;
        $imageCheck = 0;
        $aiCount = 0;
        $humanTextBox = 0;
        $humanBox = 0;
        $humanCount = 0;
        $humanOpenArrow = 0;
        $annotations = $targetFile->annotations;

        if (is_null($annotations)) {
            return [
                'humanCount' => 0,
                'aiCount' => 0
            ];
        }

        foreach ($annotations as $annotation) {
            if( strpos($annotation, 'title="Quality Guardiam System"')) {
                // <訂正案> 曜日の誤りの文字があるかどうかで判定
                if(strpos($annotation, '&lt;\\\\u8a02\\\\u6b63\\\\u6848&gt;\\\\n\\\\u66dc\\\\u65e5\\\\u306e\\\\u8aa4\\\\u308a:')){
                    $dateCheck++;
                }else if(strpos($annotation, 'trn-wrapped-text-lines')) {
                    // 【AI】<訂正案>の文字があるかどうかで判定
                    strpos($annotation, '\\&quot;\\\\u3010AI\\\\u3011&lt;\\\\u8a02\\\\u6b63\\\\u6848&gt;') ? $textCheck++ : $imageCheck++;
                }
            }else{
                if(strpos($annotation, 'inreplyto')){
                    continue;
                }
                if(strpos($annotation, '&quot;:&quot;[\\&quot;Text Box \\&quot;]&quot;')){
                    $humanTextBox++;
                }else{
                    if(strpos($annotation, 'head=\"OpenArrow\"')){
                        $humanOpenArrow++;
                    }else{
                        $humanBox++;
                    }
                }
            }
        }

        $aiCount = ($dateCheck + $textCheck) + $imageCheck / 2;
        $humanCount = $humanTextBox + $humanBox + $humanOpenArrow;

        return [
            'humanCount' => $humanCount,
            'aiCount' => $aiCount
        ];
    }


    public function update(
        UpdateRequest $request,
        Proof $proof,
        SyncProofCategories $syncProofCategories
    ) {
        $data = $request->data();

        try {
            DB::beginTransaction();

            if ($syncProofCategories->execute($proof, $data->ips, $data->categoryId, true)) {
                $proof->refresh();
            }

            ProofComment::createWithReplies($data->comments, $proof, true);

            $this->changeProofExternalDeadlineAtAction->execute(
                $proof,
                $data->externalDeadlineAt
            );

            $this->changeProofInternalDeadlineAtAction->execute(
                $proof,
                $data->internalDeadlineAt
            );

            $proof->save();

            DB::commit();

            return redirect()->route('admin.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $proof->title . 'を更新しました。'
                ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            return redirect()->route('admin.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => $th->getMessage()
                ]);
        }
    }

    public function reject(
        Proof $proof,
        MarkProofNotificationsAsRead $markProofNotificationAsReadAction
    ) {
        try {
            DB::beginTransaction();

            $status = ProofStatus::ofValue(ProofStatus::STATUS_REJECT)->first();
            if (is_null($status)) {
                throw new Exception("ProofStatus::STATUS_REJECTが存在しません");
            }

            $proof->update(['proof_status_id' => $status->id]);

            DB::commit();

            event(new ProofRejected($proof));

            $markProofNotificationAsReadAction->execute(
                User::admin()->member()->get(),
                $proof
            );

            Notification::send($proof->user, new ProofNotification($proof, ProofNotificationTypeEnum::Update,));

            return redirect()->route('admin.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $proof->title . 'を差戻しました。'
                ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            return redirect()->route('admin.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => $th->getMessage()
                ]);
        }
    }

    public function close(
        CloseRequest $request,
        Proof $proof,
        MarkProofNotificationsAsRead $markProofNotificationAsReadAction
    ) {
        Log::info('close');
        Log::info($request->file('files'));
        $validated = $request->validated();

        try {
            DB::beginTransaction();

            ProofComment::createWithReplies($validated['comments'], $proof);

            $uploadedFiles = $validated['files'];

            // S3へ保存
            $proofFiles = ProofFileManager::uploadFilesStore($uploadedFiles, $proof, ProofFile::TYPE_CLOSED);

            $status = ProofStatus::ofValue(ProofStatus::STATUS_CLOSED)->first();
            $proof->update(['proof_status_id' => $status->id]);

            DB::commit();

            JobUploadCloudSearch::dispatch($proofFiles->pluck('cloud_search_path'));

            event(new ProofClosed($proof));

            $markProofNotificationAsReadAction->execute(
                User::admin()->member()->get(),
                $proof
            );

            Notification::send($proof->user, new ProofNotification($proof, ProofNotificationTypeEnum::Update));

            return redirect()->route('admin.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $proof->title . 'を報告しました。'
                ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function newClose(
        CloseRequest $request,
        Proof $proof,
        MarkProofNotificationsAsRead $markProofNotificationAsReadAction,
        \App\Actions\PutClosedPdfTemporaryAction $putClosedPdfTemporaryAction
    ) {
        Log::info('newClose');
        Log::info($request->file('files'));
        $validated = $request->validated();

        try {
            DB::beginTransaction();

            ProofComment::createWithReplies($validated['comments'], $proof);

            $uploadedFiles = $validated['files'];

            // S3へ保存
            $proofFiles = ProofFileManager::uploadFilesStore($uploadedFiles, $proof, ProofFile::TYPE_CLOSED);
            $temporaryUrl = $putClosedPdfTemporaryAction->execute($proof->title, $proof->id);
            Log::info("temporaryUrl");
            Log::info($temporaryUrl);

            $status = ProofStatus::ofValue(ProofStatus::STATUS_CLOSED)->first();
            $proof->update(['proof_status_id' => $status->id]);

            DB::commit();

            JobUploadCloudSearch::dispatch($proofFiles->pluck('cloud_search_path'));

            event(new ProofClosed($proof, $temporaryUrl));

            $markProofNotificationAsReadAction->execute(
                User::admin()->member()->get(),
                $proof
            );

            Notification::send($proof->user, new ProofNotification($proof, ProofNotificationTypeEnum::Update));

            return redirect()->route('admin.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $proof->title . 'を報告しました。'
                ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function viewer(Proof $proof, ProofFile $proofFile)
    {
        return Inertia::render(
            ProofFileManager::ViewerComponent($proofFile),
            [
                'user' => request()->user(),
                'proof' => $proof,
                'proofFile' => $proofFile,
                'templates' => AnnotationTemplate::with('user')->get()
            ]
        );
    }

    public function forceUpload(
        ForceUploadRequest $request,
        Proof $proof,
        CreateRecommendBatchActionV2 $createRecommendBatchAction
    ) {
        $uploadedFile = $request->file('file');

        try {
            DB::beginTransaction();
            // 1. 作業用全部削除
            $proof->shared->each(function (ProofFile $proofFile) {
                if (!$proofFile->isPlainFileChecked && ProofFileManager::removeS3($proofFile->path)) {
                    $proofFile->delete();
                };
            });

            // 2. 新規作成
            $path = ProofFileManager::mergePaths(
                config('filesystems.disks.s3.proof_path'),
                $proof->uuid,
                config('filesystems.disks.s3.shared_dir'),
                $uploadedFile->getClientOriginalName()
            );

            $result = ProofFileManager::uploadS3($path, file_get_contents($uploadedFile));
            if (!$result) {
                throw new Exception("File Upload to S3 Failed");
            }

            ProofFile::create([
                'name' => $uploadedFile->getClientOriginalName(),
                'extension' => $uploadedFile->guessClientExtension(),
                'size' => $uploadedFile->getSize(),
                'dir' => dirname($path),
                'type' => ProofFile::TYPE_SHARED,
                'last_modified' => now(),
                'proof_id' => $proof->id,
                'proof_language_id' => null,
            ]);

            // 3. バッチ作成
            $batch = $createRecommendBatchAction->execute(
                proof: $proof,
                files: new RequestFormFilesData([
                    [
                        'value' => $uploadedFile,
                        'comment' => '',
                        'type' => RequestFormFileTypeEnum::Pdf,
                    ]
                ]),
                publishDate: new RequestFormPublishDateData(
                    start: $proof->publish_date_start?->format('Y-m'),
                    end: $proof->publish_date_end?->format('Y-m')
                )
            )->then(function (Batch $batch) use ($proof) {
                /**
                 * 依頼者が新規で倫理確認の申請をし、対象の申請のジョブがすべて完了した時
                 */
                if ($admins = User::ofUserType(User::USER_TYPE_ADMIN)->get()) {
                    Mail::to($admins)->send(new ProofRequestedToAdminMail($proof));
                }
            })->finally(function (Batch $batch) use ($proof) {
                /**
                 * https://laravel.com/docs/8.x/queues
                 *
                 * Since batch callbacks are serialized and executed at a later time by the Laravel queue,
                 * you should not use the $this variable within the callbacks.
                 *
                 * 「$this」変数をコールバック内で使用できない為、直接クラス名を指定して実行する。
                 */
                (new AutoAssignmentMembersAction())->execute($proof);

                /**
                 * https://tech-flag.atlassian.net/wiki/spaces/BAPI/pages/2054586369
                 *
                 * 依頼者が新規で倫理確認の申請をし、対象の申請のジョブがすべて完了した時
                 *（ジョブでエラーが起きていても送信する）
                 */
                Mail::to($proof->user)->send(new ProofRequestedToRequesterMail($proof));
            })->dispatch();

            $proof->update(['job_batch_id' => $batch->id]);

            DB::commit();

            return redirect()
                ->route('admin.proofs.index', ['id' => $proof->id])
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $uploadedFile->getClientOriginalName() . 'ファイルをアップロードしました。'
                ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            return redirect()
                ->back()
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => $th->getMessage()
                ]);
        }
    }

    public function newForceUpload(
        ForceUploadRequest $request,
        Proof $proof,
        CreateRecommendBatchActionV2 $createRecommendBatchAction
    ) {
        $uploadedFile = $request->file('file');

        try {
            DB::beginTransaction();
            // 1. 作業用全部削除
            $proof->shared->each(function (ProofFile $proofFile) {
                if (!$proofFile->isPlainFileChecked && ProofFileManager::removeS3($proofFile->path)) {
                    $proofFile->delete();
                };
            });

            // 2. 新規作成
            $path = ProofFileManager::mergePaths(
                config('filesystems.disks.s3.proof_path'),
                $proof->uuid,
                config('filesystems.disks.s3.shared_dir'),
                $uploadedFile->getClientOriginalName()
            );

            $result = ProofFileManager::uploadS3($path, file_get_contents($uploadedFile));
            if (!$result) {
                throw new Exception("File Upload to S3 Failed");
            }

            ProofFile::create([
                'name' => $uploadedFile->getClientOriginalName(),
                'extension' => $uploadedFile->guessClientExtension(),
                'size' => $uploadedFile->getSize(),
                'dir' => dirname($path),
                'type' => ProofFile::TYPE_SHARED,
                'last_modified' => now(),
                'proof_id' => $proof->id,
                'proof_language_id' => null,
            ]);

            // 3. バッチ作成
            $batch = $createRecommendBatchAction->execute(
                proof: $proof,
                files: new RequestFormFilesData([
                    [
                        'value' => $uploadedFile,
                        'comment' => '',
                        'type' => RequestFormFileTypeEnum::Pdf,
                    ]
                ]),
                publishDate: new RequestFormPublishDateData(
                    start: $proof->publish_date_start?->format('Y-m'),
                    end: $proof->publish_date_end?->format('Y-m')
                )
            )->then(function (Batch $batch) use ($proof) {
                /**
                 * 依頼者が新規で倫理確認の申請をし、対象の申請のジョブがすべて完了した時
                 */
                if ($admins = User::ofUserType(User::USER_TYPE_ADMIN)->get()) {
                    Mail::to($admins)->send(new ProofRequestedToAdminMail($proof));
                }
            })->finally(function (Batch $batch) use ($proof) {
                /**
                 * https://laravel.com/docs/8.x/queues
                 *
                 * Since batch callbacks are serialized and executed at a later time by the Laravel queue,
                 * you should not use the $this variable within the callbacks.
                 *
                 * 「$this」変数をコールバック内で使用できない為、直接クラス名を指定して実行する。
                 */
                (new AutoAssignmentMembersAction(new AssignmentMembersToProofAction))->execute($proof);

                /**
                 * https://tech-flag.atlassian.net/wiki/spaces/BAPI/pages/2054586369
                 *
                 * 依頼者が新規で倫理確認の申請をし、対象の申請のジョブがすべて完了した時
                 *（ジョブでエラーが起きていても送信する）
                 */
                Mail::to($proof->user)->send(new ProofRequestedToRequesterMail($proof));
            })->dispatch();

            $proof->update(['job_batch_id' => $batch->id]);

            DB::commit();

            return redirect()
                ->route('admin.proofs.index', ['id' => $proof->id])
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $uploadedFile->getClientOriginalName() . 'ファイルをアップロードしました。'
                ]);
        } catch (\Throwable $th) {
            DB::rollBack();

            return redirect()
                ->back()
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => $th->getMessage()
                ]);
        }
    }

    /**
     * API(Axios)
     * 完了印トグル(追加/削除)
     */
    public function completionMark(
        Proof $proof,
        User $user,
        ToggleProofCompletionMarkAction $action
    ) {
        try {
            $completionMarks = $action->execute(
                $proof,
                $user,
            );
        } catch (\Throwable $th) {
            return [
                'snackbar' => [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => $th->getMessage()
                ]
            ];
        }

        return [
            'completion_marks' => $completionMarks->load('user'),
            'snackbar' => [
                'color' => 'success',
                'icon' => 'mdi-check-circle',
                'title' => '',
                'message' => '完了印を更新しました。',
                'actionTextColor' => 'black'
            ]
        ];
    }

    public function closedOriginFileZipDownload(Proof $proof)
    {
        Log::info('closedOriginFileZipDownload');
        Log::info($proof->id);
        
        $proofFiles = ProofFile::where('proof_id', $proof->id)
            ->where('type', ['origin'])
            ->get();
        
        if ($proofFiles->isEmpty()) {
            return redirect()
                ->route('admin.proofs.archive', ['id' => $proof->id])
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => '対象ファイルが見つかりませんでした。'
                ]);
        }
        
        Log::info($proofFiles);

        // 一時ディレクトリのパス
        $tempDir = storage_path('app/public/tmp/' . $proof->id);
        // $proofFiles->first()->nameから拡張子を取り除いたファイル名を取得
        $fileNameWithoutExtension = pathinfo($proof->proof_number, PATHINFO_FILENAME);
        // ZIPファイル名
        $zipFileName = $fileNameWithoutExtension . '.zip';
        $zipFilePath = storage_path('app/public/tmp/' . $zipFileName);

        Log::info('ZIPファイル名: ' . $zipFileName);
        Log::info('ZIPファイルパス: ' . $zipFilePath);
        Log::info('一時ディレクトリ: ' . $tempDir);

        // 一時ディレクトリの作成
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($zipFilePath, ZipArchive::CREATE) !== true) {
            return redirect()
                ->route('admin.proofs.archive', ['id' => $proof->id])
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => 'ZIPファイルの作成に失敗しました。'
                ]);
        }

        $filesToDelete = [];

        try {
            foreach ($proofFiles as $proofFile) {
                $file = ProofFileManager::getContentFromS3($proofFile->path);
                if (!$file) {
                    throw new \Exception('S3からのファイル取得に失敗しました: ' . $proofFile->name);
                }
                
                $tempFilePath = $tempDir . '/' . $proofFile->name;
                $filesToDelete[] = $tempFilePath;
                
                // ファイルを一時ディレクトリに保存
                if (file_put_contents($tempFilePath, $file) === false) {
                    throw new \Exception('ファイルの書き込みに失敗しました: ' . $tempFilePath);
                }
                
                Log::info('ファイル保存: ' . $tempFilePath);
                
                // ZIPに追加
                if (!$zip->addFile($tempFilePath, $proofFile->name)) {
                    throw new \Exception('ZIPへのファイル追加に失敗しました: ' . $proofFile->name);
                }
            }

            $zip->close();

            $headers = [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $zipFileName . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ];
            // ダウンロードレスポンスを返す
            return response()->download($zipFilePath, $zipFileName, $headers)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            // エラー発生時はZIPファイルを閉じて削除
            Log::error('ZIPファイル作成エラー: ' ,[$e->getMessage()]);
            return redirect()
                ->route('admin.proofs.archive')
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => 'ZIPファイルの作成に失敗しました。'
                ]);
        }
    }
}
