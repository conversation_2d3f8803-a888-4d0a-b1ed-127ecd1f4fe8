<template>
  <v-progress-linear
    v-if="progressing"
    :value="batchData.progress"
    class="progress"
    color="primary"
    height="32"
    rounded
  >
    <template v-slot:default="{ value }">
      <strong class="white--text"> PDF変換中 {{ Math.ceil(value) }}% </strong>
      <v-progress-circular
        indeterminate
        color="white"
        class="progress-icon"
        size="15"
        width="2"
        small
      />
    </template>
  </v-progress-linear>

  <v-btn
    v-else-if="failed"
    class="chip-status"
    color="yellow"
    style="height: 32px"
    @click.stop="showLog"
    block
  >
    <v-icon left style="font-size: 24px">
      mdi-alert
    </v-icon>
    リコメンド生成未了
  </v-btn>

  <v-chip v-else class="chip-status" :color="proof.status.color" label>
    <v-icon left>
      {{ proof.status.icon }}
    </v-icon>
    {{ proof.status.text }}
  </v-chip>
</template>

<script>
export default {
  name: 'ProofStatus',
  props: {
    proof: {
      type: Object,
      required: true,
    },
    intervalTime: {
      type: Number,
      required: false,
      default: 2 * 1000,
    },
  },
  data() {
    return {
      interval: null,
      batchData: null,
    };
  },
  computed: {
    queued() {
      return this.batchData?.status === 'queued';
    },
    executing() {
      return this.batchData?.status === 'executing';
    },
    failed() {
      return this.batchData?.status === 'failed';
    },
    progressing() {
      return this.queued || this.executing;
    },
    requesting() {
      return this.proof.status.value === 'requesting';
    },
  },
  methods: {
    validateBatch() {
      if (!this.requesting) {
        return false;
      }

      if (this.proof.batch === null) {
        return false;
      }

      if (!this.progressing) {
        return false;
      }

      return true;
    },

    registerBatchInterval() {
      if (this.validateBatch()) {
        const path = this.route('admin.logs.batch', this.proof.batch.id);

        this.interval = setInterval(() => {
          this.$axios.get(path).then(({ data: batch }) => {
            this.batchData = batch;

            if (!batch.pending) {
              clearInterval(this.interval);
              this.interval = null;
            }
          });
        }, this.intervalTime);
      }
    },
    showLog(e) {
      const path = this.route('admin.logs.queue.index', {
        id: this.proof.job_batch_id,
      });

      this.$inertia.get(path);
    },
  },

  mounted() {
    this.registerBatchInterval();
  },

  beforeDestroy() {
    clearInterval(this.interval);
  },

  created() {
    this.batchData = this.proof?.batch;
  },
};
</script>

<style lang="scss" scoped>
.progress {
  margin-top: 4px !important;
  margin-bottom: 4px !important;

  .progress-icon {
    left: 5px;
    top: 1px;
  }
}

.chip-status {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
  color: white;
  width: -webkit-fill-available;
  justify-content: center;
}
</style>
