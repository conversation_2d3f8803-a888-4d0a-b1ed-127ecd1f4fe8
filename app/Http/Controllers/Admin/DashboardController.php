<?php

namespace App\Http\Controllers\Admin;

use App\Actions\GetNoticeAction;
use App\Actions\Proof\GetProofBusinessCategoriesCountAction;
use App\Actions\Proof\GetProofCategoriesCountAction;
use App\Actions\Proof\GetProofReservationWithInPeriodAction;
use App\Data\GetNoticeData;
use App\Data\ProofCategoryCountData;
use App\Http\Controllers\Controller;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function index(
        GetNoticeAction $noticeAction,
        GetProofBusinessCategoriesCountAction $categoriesCountAction,
        GetProofReservationWithInPeriodAction $proofReservationWithInPeriodAction
    ) {
        $notices = $noticeAction->execute(
            GetNoticeData::from(['user_type' => auth()->user()->type])
        );

        $categoriesCount = $categoriesCountAction
            ->execute(
                filters: [
                    'proof' => function ($query) {
                        $getStartFiscalDate = function (CarbonImmutable $date, int $startMonth = 4) {
                            $startYear = $date->month >= $startMonth ? $date->year : $date->year - 1;

                            return CarbonImmutable::create($startYear, $startMonth)->startOf('month');
                        };

                        $getEndFiscalDate = function (CarbonImmutable $date, int $startMonth = 4) {
                            $endYear = $date->month >= $startMonth ? $date->year + 1 : $date->year;
                            $endMonth = ($startMonth + 11) % 12; // 11ヶ月後

                            return CarbonImmutable::create($endYear, $endMonth)->endOf('month');
                        };

                        /**
                         * NOTE: 最適化のため、日付の範囲を指定
                         * 昨年の新年度始まり ~ 今年の新年度終わり
                         */
                        $startOfFiscalDateOfLastYear = $getStartFiscalDate(CarbonImmutable::now()->subYear());
                        $endOfFiscalDateOfCurrentYear = $getEndFiscalDate(CarbonImmutable::now());

                        $query->whereBetween('proofs.created_at', [
                            $startOfFiscalDateOfLastYear,
                            $endOfFiscalDateOfCurrentYear,
                        ]);
                    },
                ],
                dateFormat: '%Y-%m'
            )
            ->then(
                fn (Collection $values) => $values->map(function (ProofCategoryCountData $value) {
                    return [
                        ...$value->toArray(),
                        'label' => $value->name
                    ];
                })
            );

        $reservations = $proofReservationWithInPeriodAction->execute(
            start: CarbonImmutable::now()->subMonth()->startOfMonth(),
            end: CarbonImmutable::now()->addMonth()->endOfMonth()
        );

        return Inertia::render('Admin/Dashboard/AicIndex', [
            'notices' => $notices,
            'categoriesCount' => $categoriesCount,
            'reservations' => $reservations,
        ]);
    }
}
