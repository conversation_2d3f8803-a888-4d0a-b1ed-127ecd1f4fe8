<?php

namespace App\Http\Controllers\Admin;

use App\Actions\GetRecommendAction;
use App\Actions\PredictClassificationsAsyncAction;
use App\Enums\PRMEnum;
use App\Http\Controllers\Controller;
use App\Models\Proof;
use App\Models\ProofFile;
use Inertia\Inertia;

class PrmController extends Controller
{
    public function index(
        PredictClassificationsAsyncAction $action,
        Proof $proof,
        ProofFile $proofFile
    ) {
        try {
            return Inertia::render('Share/Proof/Prm/Index', [
                'user' => request()->user(),
                'proof' => $proof,
                'proofFile' => $proofFile,
                'predicts' => $action->execute($proofFile, PRMEnum::getInstances()),
            ]);
        } catch (\Throwable $th) {
            return "({$th->getCode()}) - {$th->getMessage()}";
        }
    }

    public function show(
        GetRecommendAction $action,
        Proof $proof,
        ProofFile $proofFile,
        PRMEnum $prmEnum,
        int|string $classNo
    ) {
        try {
            return Inertia::render('Share/Proof/Prm/Show', [
                'user' => request()->user(),
                'proof' => $proof,
                'proofFile' => $proofFile,
                'recommend' => $action->execute($prmEnum, $classNo),
                'prmEnum' => $prmEnum,
            ]);
        } catch (\Throwable $th) {
            return "({$th->getCode()}) - {$th->getMessage()}";
        }
    }
}
