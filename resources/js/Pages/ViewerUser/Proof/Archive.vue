<template>
  <div>
    <TableFilter :filter.sync="filter" :drawer.sync="filterDrawer" class="table-filter">

      <v-row>
        <v-col class="pa-2 pt-4 mt-3">
          <span class="filter-span">
            申請No.フィルター
          </span>
        </v-col>
      </v-row>
      <v-row>
        <v-col class="pa-2">
          <BNXPOnlyProof
            :filter="filter"
            target="xp_only"
          />
        </v-col>
        <v-col class="pa-2">
          <BNAMOnlyProof
            :filter="filter"
            target="am_only"
          />
        </v-col>
        <v-col class="pa-2">
          <BNALOnlyProof
            :filter="filter"
            target="al_only"
          />
        </v-col>
      </v-row>

      <v-row>
        <v-col class="px-2 py-4">
          <span class="filter-span">
            アーカイブ検索
          </span>
        </v-col>
      </v-row>

      <!-- No -->
      <v-row>
        <v-col cols="3" class="pa-2">
          <v-select
            v-model="filter.company_code"
            :items="companyCode"
            label="会社略称"
            item-value="company_code"
            item-text="company_code"
            outlined
            dense
            density="comfortable"
          />
        </v-col>
        <v-col cols="1" class="pt-4">
          ー
        </v-col>
        <v-col class="pa-2">
          <v-text-field
            v-model="filter.proof_number"
            label="申請No."
            name="no"
            class="pb-1"
            clearable
            outlined
            dense
          />
        </v-col>
      </v-row>


      <!-- 倫理確認申請名(title) -->
      <v-row>
        <v-col>
          <v-text-field
            v-model="filter.title"
            label="件名"
            name="title"
            class="pb-1"
            clearable
            outlined
            dense
          />
        </v-col>
      </v-row>

      <!-- 種別 -->
      <v-row>
        <v-col>
          <v-select
            v-model="filter.categories"
            :items="$page.props.categories"
            label="種別"
            item-value="id"
            item-text="name"
            multiple
            outlined
            dense
          />
        </v-col>
      </v-row>

      <!-- IP -->
      <v-row>
        <v-col>
          <v-select
            v-model="filter.ips"
            :items="$page.props.ips"
            label="IP"
            item-value="id"
            item-text="name"
            multiple
            outlined
            dense
          />
        </v-col>
      </v-row>

      <!-- 外部締切日(internal_deadline_at) -->
      <v-row>
        <v-col>
          <DateTimePicker
            label="締切日時開始"
            v-model="filter.internal_deadline_at.from"
            :textFieldProps="{
              dense: true,
              outlined: true,
            }"
          />
        </v-col>
        <span class="d-flex align-center pb-7">~</span>
        <v-col>
          <DateTimePicker
            label="締切日時終了"
            v-model="filter.internal_deadline_at.to"
            :textFieldProps="{
              dense: true,
              outlined: true,
            }"
          />
        </v-col>
      </v-row>
    </TableFilter>

    <h1 style="color: rgba(64, 64, 64, 1);">アーカイブ</h1>

    <v-data-table
      :headers="headers"
      :items="filteredProofs"
      :items-per-page="paginator.per_page"
      class="row-pointer mb-4 mt-4"
      elevation="0"
      no-data-text="倫理確認申請データが存在しません"
      hide-default-footer
      dense
      disable-sort
    >
      <template v-slot:top>
        <v-toolbar flat class="checklist-title">
          <v-toolbar-title>
          </v-toolbar-title>

          <v-spacer></v-spacer>
          <AicSortButton :filterDrawer.sync="filterDrawer" />
        </v-toolbar>
      </template>

      <!-- 申請ID -->
      <template v-slot:[`item.id`]="{ item }">
        <span class="table-text" >{{ item.proof_number }}</span>
      </template>

      <template v-slot:[`item.internal_deadline_at`]="{ item }">
        <span class="table-text" style="margin: 0%;">{{ formatDateTime(item.internal_deadline_at) }}</span>
      </template>

      <!-- 申請名 -->
      <template v-slot:[`item.title`]="{ item }">
        <span style="margin: 0%;" class="table-text-title">{{ item.title }}</span>
        <div class="chip-container">
          <v-chip class="custom-chip mr-1" color="primary" v-if="hasNotification(item)" rounded small>
            <span class="table-text">{{ getNotificationType(item) }}</span>
          </v-chip>
          <!-- proof.category.name -->
          <v-chip color="success" class="custom-chip mr-1" rounded small v-for="category in item.categories" :key="category.id">
            <span class="table-text">{{ category.short_name }}</span>
          </v-chip>
          <!-- proof.ips[].name -->
          <v-chip v-for="ip in item.ips" color="success" class="custom-chip mr-1" rounded small :key="ip.id">
            <span class="table-text"> {{ ip.name }}</span>
          </v-chip>
        </div>
      </template>

      <template v-slot:[`item.convertFile`]="{ item }">
        <div>
          <v-btn
          v-if="!isDownloading[getThumbnailId(item.convertFile)]"
          color="primary"
          :disabled="item.convertFile.length == 0"
          icon
          class="white--text"
          @click="closedOriginFileZipDownload(item)"
          >
            <v-icon center dark size="45">
              mdi-file-download
            </v-icon>
          </v-btn>
            <v-progress-circular
              v-if="isDownloading[getThumbnailId(item.convertFile)]"
              indeterminate
              color="primary"
              class="progress-icon mx-3"
              size="15"
              width="2"
              style="height: 25px; width: 25px; z-index: 100; position: sticky;"
            />
        </div>
      </template>

      <template v-slot:[`item.closedFile`]="{ item }">
        <div>
          <v-btn
          v-if="!isDownloading[getThumbnailId(item.closedFile)]"
          color="primary"
          icon
          class="white--text"
          @click="closePdfDownload(item.closedFile, item)"
          >
            <v-icon center dark size="45">
              mdi-file-download
            </v-icon>
          </v-btn>
            <v-progress-circular
              v-if="isDownloading[getThumbnailId(item.closedFile)]"
              indeterminate
              color="primary"
              class="progress-icon mx-3"
              size="15"
              width="2"
              style="height: 25px; width: 25px; z-index: 100; position: sticky;"
            ></v-progress-circular>
        </div>
      </template>

      <!-- FOOTER -->
      <template v-slot:footer="{}">
        <Pagination :paginator="paginator" />
      </template>
    </v-data-table>
  </div>
</template>

<script>
import NewLayout from '@/Layouts/NewLayout';
import Pagination from '@/Components/AicPagination';
import TableFilter from '@/Components/NewTableFilter';
import TodayDeadlineAtFilter from '@/Components/TodayDeadlineAtFilter';
import NextBusinessDayDeadline from '@/Components/NextBusinessDayDeadline.vue';
import DateTimePicker from '@/Components/NewDateTimePicker';
import ProofStatus from '@/Components/NewProofStatus';
import AvatarGroup from '@/Components/User/AvatarGroup.vue';
import MyCompletionProof from '@/Components/Proof/MyCompletionProof.vue';
import NotMyCompletionProof from '@/Components/Proof/NotMyCompletionProof.vue';
import MySpecialSupporterProof from '@/Components/Proof/MySpecialSupporterProof.vue';
import AicSortButton from '@/Components/AicSortButton';
import BNXPOnlyProof from '@/Components/BNXPOnlyProof.vue';
import BNAMOnlyProof from '@/Components/BNAMOnlyProof.vue';
import BNALOnlyProof from '@/Components/BNALOnlyProof.vue';
import { previousMonday } from 'date-fns';
import '@/../css/app.css';;
import S3 from '@/lib/AWS/S3';

export default {
  name: 'AdminProofArchive',
  layout: NewLayout,
  props: {
    paginator: {
      type: Object,
      required: true,
    },
    user: {
      type: Object,
      required: true,
    },
    companyCode: {
      type: Array,
      required: true,
    },
  },

  components: {
    Pagination,
    TableFilter,
    NextBusinessDayDeadline,
    TodayDeadlineAtFilter,
    DateTimePicker,
    ProofStatus,
    AvatarGroup,
    MyCompletionProof,
    NotMyCompletionProof,
    MySpecialSupporterProof,
    AicSortButton,
    BNXPOnlyProof,
    BNAMOnlyProof,
    BNALOnlyProof,
  },

  computed: {
    filterIconColor() {
      return location.search ? 'success' : 'secondary';
    },
    filteredProofs() {
      if (this.filter.my_completion_only) {
        return this.proofs.filter(proof => proof.completion_marks.some(mark => mark.user_id === this.user.id));
      }

      if (this.filter.idt_my_completion_only) {
        return this.proofs.filter(proof => !proof.completion_marks.some(mark => mark.user_id === this.user.id));
      }

      if(this.filter.my_special_sup_only){
        return this.proofs.filter(proof => proof.members.some(member => member.user_id === this.user.id));
      }
      return this.proofs;
    },
  },

  data() {
    return {
      filterDrawer: false,
      filter: {
        user_id: [],
        user_name: null,
        title: null,
        proof_status_id: [],
        categories: [],
        ips: [],
        external_deadline_at: {
          from: null,
          to: null,
        },
        internal_deadline_at: {
          from: null,
          to: null,
        },
        created_at: {
          from: null,
          to: null,
        },
        id: [],
        next_business_day_deadline: false,
        my_completion_only: false,
        my_special_sup_only: false,
        not_my_completion_only: false,
        // id_title: "XP-",
        proof_number: "",
        company_code: this.companyCode[0],
        xp_only: false,
        am_only: false,
        al_only: false,
      },
      headers: [
        { text: '申請No.', value: 'id', width: '5%' },
        { text: '締め切り日時', value: 'internal_deadline_at', width: '5%' },
        { text: '件名', value: 'title', width: '30%' },
        { text: '申請原本', value: 'convertFile', width: '1%' },
        { text: '注釈済み', value: 'closedFile', width: '1%' },
      ],
      proofs: [],
      avatarGroup: {
        avatarSize: 42,
        maxDisplay: 0,
        margin: -16,
      },
      file: null,
      isDownloading: {},
    };
  },
  methods: {
    hasNotification(proof) {
      return this.getNotification(proof);
    },
    getNotification(proof) {
      return this.$page.props.notifications.find(
        notification => notification.data.proof_id == proof.id,
      );
    },
    getNotificationType(proof) {
      return this.getNotification(proof)?.data.type;
    },
    showProof({ id }) {
      const path = this.route('admin.proofs.show', { id });

      this.$inertia.get(path);
    },
    toggleNextBusinessDayDeadline() {
      this.filter.next_business_day_deadline = !this.filter.next_business_day_deadline;
    },
    resetNextBusinessDayDeadline() {
      this.filter.next_business_day_deadline = false;
    },

    // 2025-05-20 廃止
    // formatId(id){
    //   return "XP-" + String(id).padStart(5, '0');
    // },

    formatDateTime(dateTime){
      return this.$moment(dateTime).format('YYYY年 M月 D日 H:mm');
    },

    titleFormatter(title) {
    // 件名が37文字以上の場合、37文字で毎回改行する
      if(title.length > 36) {
        return title.match(/.{1,36}/g).join('<br>');
      } else {
        // 37文字以下の場合はそのまま返す
        return title;
      }
    },

    closePdfDownload(proofFile, proof) {
      console.log('closePdfDownload');
      const file = proofFile;
      const key = file[0].path.startsWith('/')
      ? file[0].path.substring(1)
      : file[0].path;
      const s3 = new S3();
      this.isDownloading = { ...this.isDownloading, [this.getThumbnailId(file)]: true };

      return Promise.resolve(key)
        .then(key => s3.get(key))
        .then(obj => {
          const url = URL.createObjectURL(
            new Blob([obj.Body], { type: 'application/pdf' }),
          );

          const a = document.createElement('a');
          a.href = url;
          a.download = proof.proof_number + '.pdf';
          a.click();
        }).then(() => {
          this.$page.props.snackbar = {
            show: true,
            icon: 'mdi-file-download',
            message: 'ダウンロードを開始しました。',
            color: 'primary',
          };
        })
        .catch(() => {
          this.$page.props.snackbar = {
            show: true,
            icon: 'mdi-alert-circle',
            message: 'ダウンロードに失敗しました。',
            color: 'error',
          };
        })
        .finally(() => {
          this.isDownloading = { ...this.isDownloading, [this.getThumbnailId(file)]: false };
        });
    },

    closedOriginFileZipDownload(item) {
      console.log('closedOriginFileZipDownload');
      console.log(item);

      // Inertiaを使わず、直接ウィンドウを開いてダウンロードする
      const url = this.route('viewer.proofs.origin-download', { proof: item });
      window.open(url, '_blank');
    },

    getThumbnailId(file) {
      if(file.length == 0){
        return null;
      }else{
        return file[0].id;
      }
    },
  },

  created() {
    // site.cssの適応を削除する為の処理
    // const path = this.route('qg.index');
    // if (this.$page.props.urlPrev == path) {
    //   window.location.reload();
    // }

    this.proofs = this.paginator.data;
    // proofsのinternal_deadline_atをYYYY-MM-DD HH:mm形式に変換
    this.proofs.forEach(proof => {
      proof.internal_deadline_at = this.$moment(proof.internal_deadline_at).format('YYYY-M-D H:mm');
    });
    console.log(this.proofs);
  },

  mounted() {
    this.filter.my_completion_only=false;
    this.filter.idt_my_completion_only=false;
    this.filter.my_special_sup_only=false;
    this.filter.not_my_completion_only=false;
  }
};
</script>

<style scoped lang="css">
.row-pointer >>> tbody > tr:hover {
  cursor: pointer;
}

.row-pointer ::v-deep(td),
.row-pointer ::v-deep(th) {
  white-space: nowrap;
  padding-left: 10px !important;
  padding-right: 0px !important;
  color: rgba(64,64,64,1) !important

}

.row-pointer ::v-deep(th)
{
  padding-top: 7px !important;
}

.row-pointer ::v-deep(span) {
  font-size: 14px;
}

.row-pointer ::v-deep(tr) {
  height: 60px;
}

.custom-chip {
  border-radius: 5px; /* 角の丸みを調整 */
  margin-bottom: 4px;
  margin-top: 3px;
  padding-top: 2px;

}

.checklist-title ::v-deep(.v-toolbar__content) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.table-text{
  font-size: 14px;
}

.filter-no{
  color: rgba(64, 64, 64, 1);
  font-size: 16px;
  max-width: 10%;
  min-width: -webkit-fill-available;
  border: none;
  border-image: none;
}

.filter-no ::v-deep(.v-input__slot:before),
.filter-no ::v-deep(.v-input__slot:after)  {
  border-image: none !important;  /* 余計なボーダーを消す */
  border-style: none !important;
}

.table-filter::v-deep(.v-label) {
  color: rgba(64,64,64,1) !important;
}

.table-text-title{
  font-size: 14px;
  color: rgba(64, 64, 64, 1);
  white-space: break-spaces;
}

.chip-container{
  display: flex;
  flex-wrap: wrap;
}

.filter-span {
  font-size: 15px;
  font-weight: bold;
  color: rgba(64, 64, 64, 1);
}

</style>