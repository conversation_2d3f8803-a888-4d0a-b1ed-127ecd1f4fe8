<?php

namespace App\Http\API;

use Carbon\Carbon;
use Illuminate\Support\Facades\Http;

const IMS_HOST = 'https://ims-na1.adobelogin.com/';
const AUTH_ENDPOINT = IMS_HOST . 'ims/exchange/jwt/'; //token auth Endpoint
const AUD_ENDPOINT_PREFIX = IMS_HOST . 'c/';
const METASCOPE_ENDPOINT_PREFIX = IMS_HOST . 's/';

/**
 * Class JWTProvider
 * simple implementation
 */
class AdobeAccessTokenProvider
{
    private const SIGN_ALGORITHM = 'RS256';

    public static function auth($config)
    {
        $expTime = Carbon::now()->addDay();
        $issuer = $config['organizationId'];
        $subject = $config['technicalAccountId'];
        $audience = AUD_ENDPOINT_PREFIX . $config['clientId'];
        $metaScopes = $config['metaScopes'];
        
        // PrivateKey
        $keyFile = config('adobe.pdf_tools_api.privateKey');
        $fHandle = fopen($keyFile, "r") or die("Unable to read the key file " . $keyFile . "\n");
        $privateKey = fread($fHandle, filesize($keyFile)) or die("Unable to read the key file " . $keyFile . "\n");

        $payload = self::buildJWTPayload($expTime, $issuer, $subject, $audience, $metaScopes);
        $jwt = self::encode($payload, $privateKey);

        $accessToken = self::doAdobeIOAuth($jwt, $config['clientId'], $config['secretId']);
        
        return $accessToken;
    }

    /**
     * Converts and signs array into a JWT string.
     *
     * @param array $payload
     * @param string $key
     *
     * @return string
     * @throws \InvalidArgumentException
     */
    private static function encode(array $payload, string $key)
    {
        $header = ['typ' => 'JWT', 'alg' => self::SIGN_ALGORITHM];

        $headerJson = json_encode($header);
        $segments[] = self::urlSafeB64Encode($headerJson);

        $payloadJson = json_encode($payload);

        $segments[] = self::urlSafeB64Encode($payloadJson);

        //now going to use openssl_sign()
        $result = openssl_sign(
            implode('.', $segments),
            $signature,
            $key,
            'sha256'
        );
        if (false === $result) {
            throw new \RuntimeException('Failed to encrypt value. ' . implode("\n", $this->getSslErrors()));
        }
        $segments[] = self::urlSafeB64Encode($signature);

        return implode('.', $segments); //PACK THE ARRAY CONTAINING JWT
    }

    /**
     * Encode a string with URL-safe Base64.
     *
     * @param string $input The string you want encoded
     *
     * @return string
     */
    private static function urlSafeB64Encode(string $input): string
    {
        return str_replace('=', '', strtr(base64_encode($input), '+/', '-_'));
    }


    /**
     * Builds request JWT.
     *
     * @param string $formattableTimeString
     * @param string $issuer
     * @param string $subject
     * @param string $audience
     * @param string $metascopes
     * @return string[]
     */
    public static function buildJWTPayload($formattableTimeString, $issuer, $subject, $audience, $metascopes)
    {
        $data = [
            "exp" => strtotime($formattableTimeString),
            "iss" => $issuer,
            "sub" => $subject,
            "aud" => $audience
        ];

        if (is_array($metascopes)) {
            foreach ($metascopes as &$aMetascope) {
                $data[METASCOPE_ENDPOINT_PREFIX . $aMetascope] = true;
            }
        } else {
            // single metascope
            $data[METASCOPE_ENDPOINT_PREFIX . $metascopes] = true;
        }

        return $data;
    }

    /** Authenticates with Adobe IO - returns Auth Response
     * @param $jwt
     * @param $client_id
     * @param $client_secret
     * @return mixed |
     */
    public static function doAdobeIOAuth($jwt, $clientId, $clientSecret)
    {
        $client = Http::withHeaders([
                    "Content-Type" => "application/x-www-form-urlencoded",
                ]);
                        
        $response = $client->asForm()->post(AUTH_ENDPOINT, [
            'client_id' => $clientId,
            'client_secret' => $clientSecret,
            'jwt_token' => $jwt
        ]);
        
        if ($response->failed()) {
            return $response->throw();
        }
        
        $accessToken = $response->json()['access_token'];
        
        return $accessToken;
    }
}
