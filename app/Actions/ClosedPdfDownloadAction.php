<?php

namespace App\Actions;

use App\Data\ClosedPdfDownloadData;
use App\Models\Proof;
use App\Models\ProofFile;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class ClosedPdfDownloadAction
{
    public function execute(ClosedPdfDownloadData $data)
    {
        Log::info('ClosedPdfDownloadAction::execute');
        Log::info($data);
        $proof = Proof::where('id', $data->proofId)->first();
        if (!$proof) {
            abort(404);
        }
        Log::info($proof);
        Log::info($proof->title);
        Log::info($data->proofId);
        $request = request();
        $request->merge(['title_name' => $proof->title, 'proofId' => $data->proofId]);
        $requestData = ClosedPdfDownloadData::createToken($request);
        return $data->isMatchToken($requestData->token) ? $this->download($requestData->proofId) : abort(403);
    }

    private function download(int $proofId)
    {
        $file = ProofFile::select(['id','dir', 'name', 'created_at'])->where('proof_id', $proofId)->where('type', ProofFile::TYPE_CLOSED)->first();
        // ファイルが存在しない場合はエラー
        if (!$file) {
            abort(404);
        }
        // dirにある最初の１文字を削除
        $now = Carbon::now();
        // created_atと現在時刻の差が7日以上の場合はエラー
        if ($now->diffInDays($file->created_at) > 7) {
            return Inertia::render('ClosedPdf/Index');
        }
        $key = substr($file->dir, 1) . '/' . $file->name;

        $headers = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'filename="' . $file->name . '"',
        ];

        return Storage::disk('s3')->response($key, 200, $headers);
    }
}