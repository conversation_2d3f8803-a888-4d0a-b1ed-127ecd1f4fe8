#!/usr/bin/env node

/**
 * Sprite Sheet Generator
 * 
 * This script generates a sprite sheet from individual animation folders.
 * It reads all the animation folders in public/images/kairu/ and creates
 * a single sprite sheet image with all frames arranged in a grid.
 * 
 * Usage: node scripts/generate-sprite-sheet.js
 * 
 * Requirements: npm install sharp (for image processing)
 */

const fs = require('fs');
const path = require('path');

// Configuration
const KAIRU_DIR = path.join(__dirname, '../public/images/kairu');
const OUTPUT_PATH = path.join(KAIRU_DIR, 'sprite_sheet_generated.png');
const COLS = 10; // Number of columns in sprite sheet
const FRAME_WIDTH = 252; // Width of each frame
const FRAME_HEIGHT = 190; // Height of each frame

// Animation folders to process (in order)
const ANIMATION_FOLDERS = [
    'idle_01',
    'idle_02',
    'idle_03',
    'idle_04',
    'idle_05',
    'idle_06',
    'idle_07',
    'idle_08',
    'idle_09',
    'idle_10',
    'idle_11',
    'idle_12',
    'idle_13',
    'get_attention',
    'goodbye_01',
    'goodbye_02',
    'goodbye_03',
    'goodbye_04',
    'congratulate_01',
    'congratulate_02',
    'reading',
    'searching',
    'thinking',
    'writing',
    'look'
];

async function generateSpriteSheet() {
    console.log('🎬 Starting sprite sheet generation...');

    // Check if Sharp is available
    let sharp;
    try {
        sharp = require('sharp');
    } catch (error) {
        console.error('❌ Sharp is not installed. Please run: npm install sharp');
        process.exit(1);
    }

    const allFrames = [];
    let currentRow = 0;
    let currentCol = 0;

    // Process each animation folder
    for (const folderName of ANIMATION_FOLDERS) {
        const folderPath = path.join(KAIRU_DIR, folderName);

        if (!fs.existsSync(folderPath)) {
            console.warn(`⚠️ Folder not found: ${folderName}`);
            continue;
        }

        console.log(`📁 Processing folder: ${folderName}`);

        // Get all PNG files in the folder
        const files = fs.readdirSync(folderPath)
            .filter(file => file.endsWith('.png'))
            .sort(); // Sort to ensure correct order

        console.log(`   Found ${files.length} frames`);

        // Process each frame
        for (const file of files) {
            const filePath = path.join(folderPath, file);

            allFrames.push({
                path: filePath,
                row: currentRow,
                col: currentCol,
                animation: folderName,
                frame: file
            });

            currentCol++;
            if (currentCol >= COLS) {
                currentCol = 0;
                currentRow++;
            }
        }
    }

    console.log(`📊 Total frames: ${allFrames.length}`);
    console.log(`📐 Grid size: ${COLS} columns x ${currentRow + 1} rows`);

    // Calculate sprite sheet dimensions
    const spriteWidth = COLS * FRAME_WIDTH;
    const spriteHeight = (currentRow + 1) * FRAME_HEIGHT;

    console.log(`🖼️ Sprite sheet size: ${spriteWidth}x${spriteHeight}`);

    // Create the sprite sheet
    const spriteSheet = sharp({
        create: {
            width: spriteWidth,
            height: spriteHeight,
            channels: 4,
            background: { r: 0, g: 0, b: 0, alpha: 0 }
        }
    });

    // Prepare composite operations
    const compositeOps = [];

    for (const frame of allFrames) {
        const x = frame.col * FRAME_WIDTH;
        const y = frame.row * FRAME_HEIGHT;

        compositeOps.push({
            input: await sharp(frame.path)
                .resize(FRAME_WIDTH, FRAME_HEIGHT, { fit: 'contain' })
                .png()
                .toBuffer(),
            left: x,
            top: y
        });
    }

    // Composite all frames
    await spriteSheet
        .composite(compositeOps)
        .png()
        .toFile(OUTPUT_PATH);

    console.log(`✅ Sprite sheet generated: ${OUTPUT_PATH}`);

    // Generate animation mapping
    generateAnimationMapping(allFrames);
}

function generateAnimationMapping(frames) {
    const mapping = {};
    let currentAnimation = null;
    let animationFrames = [];

    for (const frame of frames) {
        if (currentAnimation !== frame.animation) {
            // Save previous animation
            if (currentAnimation && animationFrames.length > 0) {
                mapping[currentAnimation] = {
                    startRow: animationFrames[0].row,
                    startCol: animationFrames[0].col,
                    frameCount: animationFrames.length,
                    frames: animationFrames.map(f => ({ row: f.row, col: f.col }))
                };
            }

            // Start new animation
            currentAnimation = frame.animation;
            animationFrames = [];
        }

        animationFrames.push(frame);
    }

    // Save last animation
    if (currentAnimation && animationFrames.length > 0) {
        mapping[currentAnimation] = {
            startRow: animationFrames[0].row,
            startCol: animationFrames[0].col,
            frameCount: animationFrames.length,
            frames: animationFrames.map(f => ({ row: f.row, col: f.col }))
        };
    }

    // Save mapping to file
    const mappingPath = path.join(KAIRU_DIR, 'animation-mapping.json');
    fs.writeFileSync(mappingPath, JSON.stringify(mapping, null, 2));

    console.log(`📋 Animation mapping saved: ${mappingPath}`);
    console.log('\n🎯 Vue.js behavior configuration:');

    // Generate Vue.js compatible configuration
    for (const [animName, config] of Object.entries(mapping)) {
        console.log(`${animName}: {`);
        console.log(`  startRow: ${config.startRow}, startCol: ${config.startCol}, frameCount: ${config.frameCount},`);
        console.log(`  duration: 3000, // Adjust as needed`);
        console.log(`  loop: true // Adjust as needed`);
        console.log(`},`);
    }
}

// Run the generator
if (require.main === module) {
    generateSpriteSheet().catch(console.error);
}

module.exports = { generateSpriteSheet };
