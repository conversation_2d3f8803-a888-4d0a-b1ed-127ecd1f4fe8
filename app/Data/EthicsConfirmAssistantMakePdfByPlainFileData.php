<?php

namespace App\Data;

use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;

class EthicsConfirmAssistantMakePdfByPlainFileData extends Data
{
    public function __construct(
        // 申請番号: xxxx
        public int $proofId,

        // 申請ID
        public string $proofUuid,

        // 申請日: XXXX-XX-XX
        public CarbonImmutable $date,

        // 申請名: あああああああああああ
        public string $title,

        // 公開・稼動日
        public RequestFormPublishDateData $publishDate,

        /** @var Collection<int, \App\Data\EthicsConfirmAssistantPlainTextData> */
        public Collection $texts,

        /** @var Collection<int, \App\Data\EthicsConfirmAssistantPlainImageData> */
        public Collection $images,

        public Collection $subConcepts,
    ) {
    }
}
