<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableModifyColumnSupplementProofsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        // proofs.supplementをVARCHAR(255)からVARCHAR(300)に変更
        // 2025年5月22日
        Schema::table('proofs', function (Blueprint $table) {
            $table->text('supplement')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
        // proofs.supplementをVARCHAR(300)からVARCHAR(255)に変更
        // 2025年5月22日
        Schema::table('proofs', function (Blueprint $table) {
            $table->string('supplement', 255)->change();
        });
    }
}
