<?php

namespace App\Data;

class EthicsManualContent
{
    /**
     * Get comprehensive ethics manual content structure
     */
    public static function getManualContent(): array
    {
        return [
            'system_overview' => [
                'purpose' => 'AIC倫理確認システムは、企業の製品・サービス・文書が倫理的基準を満たしているかを確認するためのシステムです。',
                'scope' => [
                    '対象文書' => ['広告・宣伝資料', '製品仕様書', 'マーケティング資料', 'プレスリリース', '社外向け文書'],
                    '確認項目' => ['人権・ダイバーシティ', '文化的配慮', 'プライバシー・安全性', '法的コンプライアンス', '社会的責任'],
                    '利用者' => ['メンバー（申請者）']
                ],
                'benefits' => [
                    'リスク軽減' => '倫理的問題の早期発見により、後の大きな問題を防止',
                    'コンプライアンス強化' => '法的・社会的基準への適合を確保',
                    '信頼性向上' => 'ステークホルダーからの信頼獲得',
                    '品質向上' => '製品・サービスの品質向上'
                ]
            ],

            'member_responsibilities' => [
                'primary_duties' => [
                    '申請作成' => '倫理確認が必要な案件の申請書作成',
                    '資料準備' => '確認対象となる文書・資料の準備とアップロード',
                    '情報提供' => '正確で詳細な背景情報の提供',
                    '結果対応' => '確認結果に基づく適切な対応と修正'
                ],
                'quality_standards' => [
                    '完全性' => '必要な情報を漏れなく提供する',
                    '正確性' => '事実に基づく正確な情報を提供する',
                    '適時性' => '適切なタイミングで申請を行う',
                    '協力性' => '確認プロセスに積極的に協力する'
                ],
                'best_practices' => [
                    '事前準備' => '申請前に社内での予備確認を実施',
                    '明確な説明' => '確認ポイントや懸念事項を明確に記述',
                    '迅速な対応' => '追加情報要求に迅速に対応',
                    '継続改善' => 'フィードバックを活用した継続的改善'
                ]
            ],

            'application_workflow' => [
                'preparation_phase' => [
                    'step_1' => [
                        'title' => '対象の特定と分析',
                        'description' => '倫理確認が必要な製品・文書・サービスを特定し、リスク分析を実施',
                        'deliverables' => ['対象リスト', 'リスク評価表', '関連資料一覧'],
                        'duration' => '1-2日'
                    ],
                    'step_2' => [
                        'title' => '関連資料の収集',
                        'description' => '確認に必要な全ての文書・資料を収集・整理',
                        'deliverables' => ['仕様書', 'マーケティング資料', '法的文書', '過去の類似事例'],
                        'duration' => '2-3日'
                    ],
                    'step_3' => [
                        'title' => '背景情報の整理',
                        'description' => 'プロジェクトの背景、目的、対象顧客等の情報を整理',
                        'deliverables' => ['背景説明書', '対象顧客分析', '市場環境分析'],
                        'duration' => '1日'
                    ]
                ],
                'submission_phase' => [
                    'step_1' => [
                        'title' => '申請書の作成',
                        'description' => 'システムで新規申請を作成し、基本情報を入力',
                        'required_fields' => ['申請タイトル', '概要', '緊急度', '希望完了日', '担当者情報'],
                        'duration' => '30分-1時間'
                    ],
                    'step_2' => [
                        'title' => 'カテゴリとタグの設定',
                        'description' => '適切なカテゴリを選択し、検索用タグを設定',
                        'options' => ['製品', '広告', '文書', 'サービス', 'その他'],
                        'duration' => '15分'
                    ],
                    'step_3' => [
                        'title' => 'ファイルのアップロード',
                        'description' => '確認対象の文書・資料をシステムにアップロード',
                        'supported_formats' => ['PDF', 'Word', 'PowerPoint', 'JPEG', 'PNG'],
                        'max_size' => '50MB per file',
                        'duration' => '15-30分'
                    ]
                ]
            ],

            'evaluation_process' => [
                'automated_screening' => [
                    'ai_analysis' => [
                        'description' => 'AI システムによる自動的な内容分析',
                        'check_items' => ['明らかな問題表現', '法的リスク要因', '文化的配慮事項', 'プライバシー関連'],
                        'output' => '初期リスクスコア（0-100点）',
                        'duration' => '数分-1時間'
                    ],
                    'pattern_matching' => [
                        'description' => '過去の問題事例との照合',
                        'database' => '過去の確認結果データベース',
                        'output' => '類似事例リスト',
                        'duration' => '数分'
                    ]
                ],
                'expert_review' => [
                    'assignment' => [
                        'description' => '内容に応じた適切な専門家の割当',
                        'criteria' => ['専門分野', '経験年数', '現在の作業負荷'],
                        'specialists' => ['法務専門家', '倫理専門家', '業界専門家', '文化専門家']
                    ],
                    'detailed_analysis' => [
                        'description' => '人間の専門家による詳細な内容分析',
                        'methodology' => ['多角的視点での検証', '法的要件との照合', '社会的影響の評価'],
                        'duration' => '1-3営業日'
                    ]
                ]
            ]
        ];
    }

    /**
     * Get role-specific guidelines
     */
    public static function getRoleGuidelines(string $role): array
    {
        $guidelines = [
            'member' => [
                'key_responsibilities' => [
                    '申請の質' => '完全で正確な申請書の作成',
                    '資料準備' => '必要な文書・資料の適切な準備',
                    '迅速対応' => '追加要求への迅速な対応',
                    '結果活用' => '確認結果の適切な活用と改善'
                ],
                'success_tips' => [
                    '事前確認' => '社内での事前レビューを実施',
                    '明確な記述' => '懸念事項や確認ポイントを明確に記述',
                    '完全な情報' => '関連する全ての情報を提供',
                    '継続学習' => '過去の事例から学習し改善'
                ],
                'common_mistakes' => [
                    '情報不足' => '必要な背景情報の不足',
                    '不明確な説明' => '確認ポイントの不明確な記述',
                    '遅延対応' => '追加情報要求への遅延対応',
                    '結果軽視' => '確認結果の軽視や無視'
                ]
            ]
        ];

        return $guidelines[$role] ?? $guidelines['member'];
    }

    /**
     * Get quick reference information
     */
    public static function getQuickReference(): array
    {
        return [
            'emergency_contacts' => [
                'システム障害' => '内線1234（24時間対応）',
                '緊急確認' => '<EMAIL>',
                '技術サポート' => '<EMAIL>'
            ],
            'important_deadlines' => [
                '通常案件' => '3-5営業日',
                '緊急案件' => '24時間以内（追加料金）',
                '複雑案件' => '1-2週間',
                '大規模案件' => '2-4週間（要事前相談）'
            ],
            'file_requirements' => [
                '対応形式' => 'PDF, Word, PowerPoint, JPEG, PNG',
                '最大サイズ' => '50MB per file',
                '推奨サイズ' => '10MB以下',
                '文字認識' => 'OCR対応（画像ファイル）'
            ],
            'scoring_guide' => [
                '90-100点' => '優秀（問題なし）',
                '70-89点' => '良好（軽微な改善推奨）',
                '50-69点' => '要注意（修正必要）',
                '50点未満' => '不適切（大幅修正必要）'
            ]
        ];
    }
}