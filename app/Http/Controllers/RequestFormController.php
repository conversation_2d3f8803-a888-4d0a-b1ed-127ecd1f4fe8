<?php

namespace App\Http\Controllers;

use App\Actions\GetNoticeAction;
use App\Actions\UpsertUserByEmail;
use App\Actions\Proof\GetProofReservationWithInPeriodAction;
use App\Actions\RequestForm\RequestFormSubmitAction;
use App\Data\GetNoticeData;
use App\Data\RequestFormData;
use App\Enums\ProofNotificationTypeEnum;
use App\Events\RequestFormStored;
use App\Http\Requests\RequestFormController\RequestFormStoreRequest;
use App\Http\Requests\RequestFormController\RequestFormContactRequest;
use App\Http\Requests\RequestFormController\RequestFormContactFormRequest;
use App\Models\Category;
use App\Models\Holiday;
use App\Models\IntellectualProperty;
use App\Models\Proof;
use App\Models\ProofDeadLine;
use App\Models\ProofFile;
use App\Models\ProofNumber;
use App\Models\ProofStatus;
use App\Models\User;
use App\Notifications\ProofNotification;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;


class RequestFormController extends Controller
{
    public function index(
        GetNoticeAction $noticeAction,
        RequestFormData $data,
        UpsertUserByEmail $upsertAction,
        GetProofReservationWithInPeriodAction $getReservationAction
    ) {
        $user = $upsertAction->execute($data->email, [
            'last_name' => $data->name,
            'department' => $data->department,
            'user_type_id' => User::USER_TYPE_REQUESTER,
        ]);
        $now = CarbonImmutable::now();
        $timestamp = $now->format('YmdHis');
        $year = substr($now->year, 3);
        $month = ltrim($now->month, '0');
        $day = ltrim($now->day, '0');
        $hour = ltrim($now->hour, '0');
        $minute = ltrim($now->minute, '0');
        $second = ltrim($now->second, '0');
        $uuid = env('APP_COMPANY') . '-' . $timestamp;
        $uuidDisplay = env('APP_COMPANY') . '-' . $year . $month . $day . $hour . $minute . $second;

        Auth::login($user, remember: true);

        $notices = $noticeAction->execute(
            GetNoticeData::from(['user_type' => $user->type])
        );

        return Inertia::render("RequestForm/Index", [
            'uuid' => $uuid,
            'uuidDisplay' => $uuidDisplay,
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'categories' => Category::all(),
            'externalTime' => ProofDeadLine::getAllowedTime(true),
            'holidays' => Holiday::all(),
            'reservations' => $getReservationAction->execute(
                CarbonImmutable::now()->subMonth()->startOfDay(),
                CarbonImmutable::now()->addMonths(3)->endOfDay()
            ),
            ...$data->toArray(),
            'defaultEmailToAddress' => config('mail.request-form.to.address'),
            'notices' => $notices,
        ]);
    }

    public function error()
    {
        return Inertia::render("RequestForm/Error", [
            'errors' => session()->get('errors')
                ?->getBag('default')
                ?->all() ?? []
        ]);
    }

    /**
     * API(Axios)
     *
     * 理由
     * ※InertiaでredirectするとUUIDが変わる。
     * バリデーションと普通のエラーを区別するため。
     */
    public function store(
        RequestFormStoreRequest $request,
        RequestFormSubmitAction $action
    ) {
        try {
            DB::beginTransaction();
            // $requestのuuidでテーブルのuuidのカラムをselect検索して既にテーブルにレコードが存在していたらエラーを返す
            $proofDuplication = Proof::where('uuid', $request->uuid)->exists();
            if ($proofDuplication) {
                return response()->json([
                    'reload' => true,
                    'snackbar' =>  [
                        'color' => 'error',
                        'icon' => 'mdi-alert-circle',
                        'title' => '失敗',
                        'message' => '申請エラーが発生しました、再度の申請をお願いいたします。'
                    ],
                ], Response::HTTP_CONFLICT);
            }
            // 申請処理
            $proof = $action->execute(
                $request->data(),
                auth()->user()
            );

            // 管理者の未読追加
            Notification::send(
                User::admin()->get(),
                new ProofNotification($proof, ProofNotificationTypeEnum::New)
            );

            DB::commit();

            return response()->json([
                'snackbar' =>  [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => "「{$proof->title}」を申請しました。"
                ],
            ]);
        } catch (\Throwable $th) {
            // DB::rollBack();
            DB::commit();
            $errorMessage = json_decode($th->getMessage(), true);
            if (isset($errorMessage['command']) && $errorMessage['command'] === 'createConvertedProofFiles') {
                $proof = $errorMessage['proof'];
                $proof = Proof::find($proof['id']);

                 // 管理者の未読追加
                Notification::send(
                    User::admin()->get(),
                    new ProofNotification($proof, ProofNotificationTypeEnum::New)
                );

                return response()->json([
                    'snackbar' =>  [
                        'color' => 'success',
                        'icon' => 'mdi-check-circle',
                        'title' => '成功',
                        'message' => $proof->title . 'を申請しました。'
                    ],
                ]);
            } else {
                $errorMessage = '申請エラーが発生しました、再度の申請の内容をご確認お願いいたします。';
            }

            return response()->json([
                'snackbar' =>  [
                    'color' => 'error',
                    'icon' => 'mdi-alert-circle',
                    'title' => '失敗',
                    'message' => "{$th->getMessage()} ({$th->getCode()})"
                ],
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function history()
    {

        $user = auth()->user();
        $period_moth = config('proof.proof_history_period_month');
        Log::info('period_moth', ['period_moth' => $period_moth]);
        if (request()->has('proof_number') && request()->proof_number != null) {
            // proof_numberが5桁未満なら先頭から0を埋める
            $proof_number = request()->proof_number;
            if (strlen($proof_number) < 5) {
                $proof_number = str_pad($proof_number, 5, '0', STR_PAD_LEFT);
            }
            // company_codeを付ける
            $proof_number = request()->company_code . '-' . $proof_number;
            request()->merge(['proof_number' => $proof_number]);
        }
        if($period_moth == 0) {
                $query = Proof::filter(request()->all())
                    ->where('user_id', auth()->id())
                    ->whereIn('proof_status_id', [
                        ProofStatus::ofValue(ProofStatus::STATUS_REQUESTING)->first()->id,
                        ProofStatus::ofValue(ProofStatus::STATUS_PROGRESSING)->first()->id,
                        ProofStatus::ofValue(ProofStatus::STATUS_CLOSED)->first()->id,
                    ])
                    ->where('created_at', '>=', now()->subMonths($period_moth))
                    ->orderByDesc('id');
        }else{
                $query = Proof::filter(request()->all())
                    ->where('user_id', auth()->id())
                    ->whereIn('proof_status_id', [
                        ProofStatus::ofValue(ProofStatus::STATUS_REQUESTING)->first()->id,
                        ProofStatus::ofValue(ProofStatus::STATUS_PROGRESSING)->first()->id,
                        ProofStatus::ofValue(ProofStatus::STATUS_CLOSED)->first()->id,
                    ])
                    ->where('created_at', '>=', now()->subMonths($period_moth))
                    ->orderByDesc('id');
        }

        $paginator = $query->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $paginator
            ->each(fn(Proof $proof) => $proof->append('capable_completion_mark_users'));


        $proofStatuses = ProofStatus::where('value', '!=', ProofStatus::STATUS_REJECT)
            ->where('value', '!=', ProofStatus::STATUS_ASSIGNMENT)
            ->get();

        $proofStatuses = $proofStatuses->map(function ($proofStatus) {
            if ($proofStatus->value === ProofStatus::STATUS_REQUESTING) {
                $proofStatus->text = 'PDF化待ち';
            }
            return $proofStatus;
        });

        $requesters = User::has('proofs')->get();

        $closedProofFiles = ProofFile::whereIn('type', [ProofFile::TYPE_CLOSED, ProofFile::TYPE_CONVERTED])->get();
        $paginator->each(function ($proof) use ($closedProofFiles) {
            // $proofのidをもつファイルでnameに_gendocがはいっていないものを取得
            $proof->convertFile = $closedProofFiles->filter(function ($closedProofFile) use ($proof) {
                return $closedProofFile->proof_id === $proof->id && strpos($closedProofFile->name, '_gendoc') === false && $closedProofFile->type === 'converted';
            })->values();

            $proof->closedFile = $closedProofFiles->filter(function ($closedProofFile) use ($proof) {
                return $closedProofFile->proof_id === $proof->id && $closedProofFile->type === 'closed';
            })->values();
        });

        // 今月の休日を取得
        $today = now();
        $startOfMonth = $today->copy()->startOfMonth();
        $endOfMonth = $today->copy()->endOfMonth();
        $holidays = [];
        $holidays = Holiday::where('date', '>=', $startOfMonth)
            ->where('date', '<=', $endOfMonth)
            ->get();

        return Inertia::render('Requester/Proof/History', [
            'paginator' => $paginator,
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'categories' => Category::all(),
            'proofStatuses' => $proofStatuses,
            'requesters' => $requesters,
            'user' => auth()->user(),
            'holidays' => $holidays,
            'companyCode' => ProofNumber::select('company_code')->get()->pluck('company_code')->toArray(),
        ]);
    }
}
