<?php

namespace App\Http\Requests\Admin\CartridgeController;

use App\Data\CartridgeData;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                "required",
                "string",
                "max:255",
            ],
            'description' => [
                "nullable",
                "string",
            ],
            'elements' => [
                "nullable",
                "array",
            ],
            'elements.*.active' => [
                "nullable",
                "boolean",
            ],
            'elements.*.level' => [
                "nullable",
                "integer",
            ],
        ];
    }

    public function attributes()
    {
        return [
            'name' => 'カートリッジ名',
            'description' => 'カートリッジ説明',
        ];
    }

    public function data(): CartridgeData
    {
        $result = $this->validated();
        $result['id'] = null;
        return CartridgeData::from($result);
    }

    public function messages()
    {
        return [
            'name.required' => ':attribute項目は必須です',
        ];
    }
}
