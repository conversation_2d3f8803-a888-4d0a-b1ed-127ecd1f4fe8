<?php

namespace App\Http\Controllers\Requester;

use App\Actions\Proof\GetProofReservationWithInPeriodAction;
use App\Actions\Proof\MarkProofNotificationsAsRead;
use App\Enums\ProofNotificationTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Requester\ProofController\StoreRequest as ProofStoreRequest;
use App\Http\Requests\Requester\ProofController\UpdateRequest as ProofUpdateRequest;
use App\Helpers\ProofFileManager;
use App\Models\Category;
use App\Models\Holiday;
use App\Models\IntellectualProperty;
use App\Models\Proof;
use App\Models\ProofBusinessCategory;
use App\Models\ProofCategory;
use App\Models\ProofComment;
use App\Models\ProofLanguage;
use App\Models\ProofStatus;
use App\Models\ProofDeadLine;
use App\Models\ProofFile;
use App\Models\ProofNumber;
use Carbon\CarbonImmutable;
use App\Models\User;
use App\Notifications\ProofNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;
use Inertia\Inertia;
use ZipArchive;

class ProofController extends Controller
{
    /**
     * 倫理確認申請リスト
     */
    public function index()
    {
        $paginator = Proof::filter(request()->all())
            ->ofRequester(auth()->user()->id)
            ->orderByDesc('created_at')
            ->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $proofStatuses = ProofStatus::all();

        return Inertia::render('Requester/Proof/Index', [
            'paginator' => $paginator,
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'categories' => Category::all(),
            'proofStatuses' => $proofStatuses,
        ]);
    }

    public function archive()
    {
        $user = auth()->user();
        Log::info('user', ['user' => $user]);
        if($user->type->id == User::USER_TYPE_ADMIN){
            $email = config('request_form.proxy_user.email');
            $user = User::where('email', $email)->first();
            if($user){
                Auth::login($user, remember: true);
                Log::info('login', ['user' => $user]);
            }else{
                Log::info('user not found', ['email' => $email]);
            }
        }

        $query = Proof::filter(request()->all())
            ->where('user_id', auth()->id())
            ->where('proof_status_id', ProofStatus::ofValue(ProofStatus::STATUS_CLOSED)->first()->id)
            ->orderByDesc('id');

        if (request()->has('checkedOnly') && request()->checkedOnly) {
            $userId = auth()->id();
            $query->whereHas('completionMarks', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            });
        }

        $paginator = $query->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $paginator
            ->each(fn(Proof $proof) => $proof->append('capable_completion_mark_users'));


        $proofStatuses = ProofStatus::where('value', '!=', ProofStatus::STATUS_REJECT)
            ->where('value', '!=', ProofStatus::STATUS_ASSIGNMENT)
            ->get();
        $requesters = User::has('proofs')->get();

        $closedProofFiles = ProofFile::whereIn('type', [ProofFile::TYPE_CLOSED, ProofFile::TYPE_CONVERTED])->get();
        $paginator->each(function ($proof) use ($closedProofFiles) {
            // $proofのidをもつファイルでnameに_gendocがはいっていないものを取得
            $proof->convertFile = $closedProofFiles->filter(function ($closedProofFile) use ($proof) {
                return $closedProofFile->proof_id === $proof->id && strpos($closedProofFile->name, '_gendoc') === false && $closedProofFile->type === 'converted';
            })->values();

            $proof->closedFile = $closedProofFiles->filter(function ($closedProofFile) use ($proof) {
                return $closedProofFile->proof_id === $proof->id && $closedProofFile->type === 'closed';
            })->values();
        });

        return Inertia::render('Requester/Proof/Archive', [
            'paginator' => $paginator,
            'ips' => IntellectualProperty::with('parent')->children()->get(),
            'categories' => Category::all(),
            'proofStatuses' => $proofStatuses,
            'requesters' => $requesters,
            'user' => auth()->user(),
            'companyCode' => ProofNumber::select('company_code')->get()->pluck('company_code')->toArray(),
        ]);
    }

    /**
     * 倫理確認申請作成フォーム
     */
    public function create(GetProofReservationWithInPeriodAction $action)
    {
        $proofLanguages = ProofLanguage::all();
        $proofBusinessCategories = ProofBusinessCategory::all();

        $externalTime = ProofDeadLine::getAllowedTime(true);

        $holidays = Holiday::upcomingHoliday()->get();

        $reservations = $action->execute(
            CarbonImmutable::now()->subMonth()->startOfDay(),
            CarbonImmutable::now()->addMonths(3)->endOfDay()
        );

        return Inertia::render('Requester/Proof/Create', [
            'proofLanguages' => $proofLanguages,
            'proofBusinessCategories' => $proofBusinessCategories,
            'externalTime' => $externalTime,
            'holidays' => $holidays,
            'reservations' => $reservations
        ]);
    }

    /**
     * 倫理確認申請作成保存
     */
    public function store(ProofStoreRequest $request)
    {
        $validated = $request->validated();

        try {
            DB::beginTransaction();

            $status = ProofStatus::ofValue(ProofStatus::STATUS_REQUESTING)->first();

            $proof = Proof::createWithComments([
                'user_id' => auth()->user()->id,
                'uuid' => (string) Str::uuid(),
                'title' => $validated['title'],
                'description' => $validated['description'],
                'proof_status_id' => $status->id,
                'external_deadline_at' => $validated['external_deadline_at'],
                'internal_deadline_at' => $validated['external_deadline_at'],
                'comments' => $validated['comments'],
            ]);

            $proof->business_categories()->attach($validated['business_categories']);

            ProofFileManager::request($validated['files'], $proof);

            Notification::send(
                User::admin()->get(),
                new ProofNotification($proof, ProofNotificationTypeEnum::New)
            );

            DB::commit();

            return redirect()->route('requester.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'primary',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $proof->title . 'を申請しました。'
                ]);
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }
    }

    /**
     * 倫理確認申請作成確認
     */
    public function show(
        Proof $proof,
        MarkProofNotificationsAsRead $markProofNotificationAsReadAction
    ) {
        $proofCategories = ProofCategory::all();
        $proofLanguages = ProofLanguage::all();

        $proof->load([
            'comments',
            'origin',
            'closed',
        ]);

        $markProofNotificationAsReadAction->execute([auth()->user()], $proof);
        $proof->append('uuid_display');

        return Inertia::render('Requester/Proof/Show', [
            'proof' => $proof,
            'proofCategories' => $proofCategories,
            'proofLanguages' => $proofLanguages,
        ]);
    }

    public function update(ProofUpdateRequest $request, Proof $proof)
    {
        $validated = $request->validated();

        try {
            DB::beginTransaction();

            ProofComment::createWithReplies($validated['comments'], $proof, true);

            DB::commit();

            return redirect()->route('requester.proofs.index', [], self::HTTP_STATUS_SEE_OTHER)
                ->with('snackbar', [
                    'color' => 'success',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $proof->title . 'を更新しました。'
                ]);
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }
    }

    public function viewer(Proof $proof, ProofFile $proofFile)
    {
        return Inertia::render("Requester/Proof/Viewer", [
            'user' => request()->user(),
            'proofFile' => $proofFile
        ]);
    }

    public function closedOriginFileZipDownload(Proof $proof)
    {
        Log::info('closedOriginFileZipDownload');
        Log::info($proof->id);
        
        $proofFiles = ProofFile::where('proof_id', $proof->id)
            ->where('type', ['origin'])
            ->get();
        
        if ($proofFiles->isEmpty()) {
            return redirect()
                ->route('admin.proofs.archive', ['id' => $proof->id])
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => '対象ファイルが見つかりませんでした。'
                ]);
        }
        
        Log::info($proofFiles);

        // 一時ディレクトリのパス
        $tempDir = storage_path('app/public/tmp/' . $proof->id);
        // $proofFiles->first()->nameから拡張子を取り除いたファイル名を取得
        $fileNameWithoutExtension = pathinfo($proof->proof_number, PATHINFO_FILENAME);
        // ZIPファイル名
        $zipFileName = $fileNameWithoutExtension . '.zip';
        $zipFilePath = storage_path('app/public/tmp/' . $zipFileName);

        Log::info('ZIPファイル名: ' . $zipFileName);
        Log::info('ZIPファイルパス: ' . $zipFilePath);
        Log::info('一時ディレクトリ: ' . $tempDir);

        // 一時ディレクトリの作成
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($zipFilePath, ZipArchive::CREATE) !== true) {
            return redirect()
                ->route('admin.proofs.archive', ['id' => $proof->id])
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => 'ZIPファイルの作成に失敗しました。'
                ]);
        }

        $filesToDelete = [];

        try {
            foreach ($proofFiles as $proofFile) {
                $file = ProofFileManager::getContentFromS3($proofFile->path);
                if (!$file) {
                    throw new \Exception('S3からのファイル取得に失敗しました: ' . $proofFile->name);
                }
                
                $tempFilePath = $tempDir . '/' . $proofFile->name;
                $filesToDelete[] = $tempFilePath;
                
                // ファイルを一時ディレクトリに保存
                if (file_put_contents($tempFilePath, $file) === false) {
                    throw new \Exception('ファイルの書き込みに失敗しました: ' . $tempFilePath);
                }
                
                Log::info('ファイル保存: ' . $tempFilePath);
                
                // ZIPに追加
                if (!$zip->addFile($tempFilePath, $proofFile->name)) {
                    throw new \Exception('ZIPへのファイル追加に失敗しました: ' . $proofFile->name);
                }
            }

            $zip->close();

            $headers = [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $zipFileName . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ];
            // ダウンロードレスポンスを返す
            return response()->download($zipFilePath, $zipFileName, $headers)->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            // エラー発生時はZIPファイルを閉じて削除
            Log::error('ZIPファイル作成エラー: ' ,[$e->getMessage()]);
            return redirect()
                ->route('requester.proofs.archive')
                ->with('snackbar', [
                    'color' => 'error',
                    'icon' => 'mdi-alert',
                    'title' => '失敗',
                    'message' => 'ZIPファイルの作成に失敗しました。'
                ]);
        }
    }
}
