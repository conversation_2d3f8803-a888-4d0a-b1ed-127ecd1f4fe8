<?php

namespace App\Http\Requests\Admin\CartridgeController;

use App\Data\CartridgeData;
use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id' => [
                'required',
                'numeric'
            ],
            'name' => [
                "required",
                "string",
                "max:255",
            ],
            'description' => [
                "nullable",
                "string",
            ],
            'elements' => [
                "nullable",
                "array",
            ],
            'elements.*.active' => [
                "nullable",
                "boolean",
            ],
            'elements.*.level' => [
                "nullable",
                "integer",
            ],
        ];
    }


    public function attributes()
    {
        return [
            'name' => 'カートリッジ名',
            'description' => 'カートリッジ説明',
        ];
    }

    public function data(): CartridgeData
    {
        $result = $this->validated();
        return CartridgeData::from($result);
    }

    public function messages()
    {
        return [
            'name.required' => ':attribute項目は必須です',
        ];
    }
}
