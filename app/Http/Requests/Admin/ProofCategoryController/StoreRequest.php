<?php

namespace App\Http\Requests\Admin\ProofCategoryController;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                "required",
                "string",
                "max:255",
                "unique:proof_categories"
            ]
        ];
    }
}
