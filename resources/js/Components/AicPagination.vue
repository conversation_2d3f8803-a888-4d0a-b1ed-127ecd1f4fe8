<template>
  <v-pagination
    class="my-3 pb-3"
    :value="paginator.current_page"
    :length="paginator.last_page"
    :total-visible="7"
    @input="link"
  />
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    paginator: {
      type: Object,
      required: true,
    },
  },
  data(){
    return {
      length: 5,
    }
  },
  methods: {
    link(page) {
      const url = new URL(location.href);
      url.searchParams.set('page', page);

      const path = url.href;

      this.$inertia.get(path);
    },
  },
  created() {
    this.length = this.paginator.last_page > 5 ? 5 : this.paginator.last_page;
  },
};
</script>

<style></style>
