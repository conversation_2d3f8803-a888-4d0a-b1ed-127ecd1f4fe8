<template>
  <v-toolbar flat dense color="blue-grey lighten-5">
    <!-- 強制アップロード -->
    <FileUploadingDialog :form="form" :loading.sync="loading" />

    <v-toolbar-items>
      <!-- ROOT -->
      <v-btn
        text
        :input-value="handler.current.model.path === '/'"
        @click="changeActive('/')"
      >
        <v-icon class="mr-2">mdi-folder-multiple-outline</v-icon>
        ROOT
      </v-btn>

      <!-- ROOT以外 -->
      <template v-for="(segment, index) in pathSegments">
        <v-icon :key="index + '-icon'">mdi-chevron-right</v-icon>
        <v-btn
          text
          :input-value="index === pathSegments.length - 1"
          :key="index + '-btn'"
          @click="changeActive(segment.path)"
        >
          {{ segment.name }}
        </v-btn>
      </template>
    </v-toolbar-items>

    <div class="flex-grow-1"></div>

    <!-- Buttons -->
    <!-- 失敗の際、受付ファイルダウンロード -->
    <v-tooltip bottom v-if="isForceUploadable">
      <template v-slot:activator="{ on, attrs }">
        <input
          v-show="false"
          type="file"
          ref="inputFileUpload"
          accept=".pdf"
          @change="forceUpload"
        />
        <v-btn
          fab
          x-small
          @click="$refs.inputFileUpload.click()"
          v-bind="attrs"
          v-on="on"
          icon
        >
          <v-icon>
            mdi-upload
          </v-icon>
        </v-btn>
      </template>
      <span>結合PDFファイルアップロード</span>
    </v-tooltip>

    <!-- Buttons -->
    <!-- フォルダー生成 -->
    <v-btn
      v-if="isCreatable"
      icon
      title="フォルダー生成"
      @click="newDirShow = true"
    >
      <v-icon>mdi-folder-plus-outline</v-icon>
    </v-btn>

    <!-- フォルダーアップロード追加 -->
    <v-btn
      v-if="isCreatable"
      icon
      @click="$refs.inputDirUpload.click()"
      title="フォルダーアップロード"
    >
      <v-icon>mdi-folder-upload</v-icon>
      <input
        v-show="false"
        type="file"
        ref="inputDirUpload"
        multiple
        webkitdirectory
        mozdirectory
        msdirectory
        odirectory
        directory
        @change="uploadDir"
      />
    </v-btn>

    <!-- ファイルアップロード追加 -->
    <v-btn
      v-if="isCreatable"
      icon
      @click="$refs.inputFileUpload.click()"
      title="ファイルアップロード"
    >
      <v-icon>mdi-file-upload</v-icon>
      <input
        v-show="false"
        type="file"
        ref="inputFileUpload"
        multiple
        :accept="accept"
        @change="addFiles"
      />
    </v-btn>

    <!-- フォルダー生成ダイアログ -->
    <v-dialog v-model="newDirShow" max-width="300" @keydown.esc="cancel">
      <v-card>
        <v-toolbar dark color="primary" dense flat>
          <v-toolbar-title class="white--text">フォルダー生成</v-toolbar-title>
        </v-toolbar>

        <v-card-text class="pa-4 text-center">
          <v-text-field label="名前" v-model="newDirName" hide-details />
        </v-card-text>

        <v-card-actions class="pt-0 pb-3">
          <v-spacer></v-spacer>
          <v-btn @click="cancel" depressed>キャンセル</v-btn>

          <v-btn
            color="success"
            :disabled="!newDirName"
            depressed
            @click="mkdir"
          >
            生成
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-toolbar>
</template>

<script>
import Path from 'path';
import FileUploadingDialog from '@/Components/FileUploadingDialog';
import { head } from 'lodash';

export default {
  props: {
    handler: Object,
    user: Object,
    readonly: Boolean,
    proof: Object,
  },
  components: {
    FileUploadingDialog,
  },
  data() {
    return {
      newDirShow: false,
      newDirName: '',
      loading: false,
      form: this.$inertia.form({
        file: null,
      }),
    };
  },
  computed: {
    pathSegments() {
      if (this.handler.current.isRoot()) {
        return [];
      }

      const paths = this.handler.current.getPath();
      paths.shift(); // ROOT 削除

      const segments = paths.map(node => node.model);

      return segments;
    },

    isRequestCreate() {
      const isOriginPath = this.handler.current.model.path.startsWith(
        '/' + this.handler.ORIGIN_PATH,
      );

      return !this.readonly && isOriginPath && this.proof === undefined;
    },

    isCloseCreate() {
      const isClosedPath = this.handler.current.model.path.startsWith(
        '/' + this.handler.CLOSED_PATH,
      );

      return (
        !this.readonly &&
        isClosedPath &&
        (this.proof?.status.value === this.handler.PROOF_STATUS_ASSIGNMENT ||
          this.proof?.status.value === this.handler.PROOF_STATUS_PROGRESSING)
      );
    },

    isAssignmentCreate() {
      const isSharedPath = this.handler.current.model.path.startsWith(
        '/' + this.handler.SHARED_PATH,
      );

      return (
        !this.readonly &&
        isSharedPath &&
        this.proof?.status.value === this.handler.PROOF_STATUS_REQUESTING
      );
    },

    isCreatable() {
      return this.isRequestCreate || this.isCloseCreate;
    },

    isForceUploadable() {
      const isSharedPath = this.handler.current.model.path.startsWith(
        '/' + this.handler.SHARED_PATH,
      );

      return (
        !this.readonly &&
        isSharedPath &&
        (this.proof?.status.value === this.handler.PROOF_STATUS_REQUESTING ||
          this.proof?.status.value === this.handler.PROOF_STATUS_ASSIGNMENT ||
          this.proof?.status.value === this.handler.PROOF_STATUS_PROGRESSING)
      );
    },

    accept() {
      return this.handler.accepts.join(',');
    },
  },
  methods: {
    forceUpload(event) {
      const files = event.target.files;
      const file = files[0];

      const message = `${file.name}\n本当にファイルをアップロードしますか?\n\n・ 既存のファイルに上書き保存されます。\n・ 既存の注釈は全て削除されます。\n・ 「種別」と「使用IP」によってメンバーは自動に担当配置されます。`;

      if (confirm(message)) {
        this.loading = true;

        this.form.file = file;

        const path = this.route('admin.proofs.force-upload', {
          proof: this.proof,
        });

        this.form.post(path, {
          onError: errors => {
            console.log(errors);

            alert(head(errors.default.file));
          },
        });
      }

      this.$refs.inputFileUpload.value = '';
    },

    changeActive(path) {
      this.handler.changeActive(path);
    },

    addFiles(event) {
      const files = event.target.files;

      Array.from(files).forEach(file => {
        const path = this.handler.current.model.path;

        this.handler.addFile(file, path);
      });

      this.$refs.inputFileUpload.value = '';
    },

    uploadDir(event) {
      const files = event.target.files;

      // ディレクトリー生成
      const dirs = _.uniq(
        _.map(files, file => Path.dirname(file.webkitRelativePath)),
      );

      dirs.forEach(dir => {
        this.handler.createDir(dir);
      });

      // ファイル追加
      Array.from(files).forEach(file => {
        const currentPath = this.handler.current.model.path;
        const path = Path.join(
          currentPath,
          Path.dirname(file.webkitRelativePath),
        );

        this.handler.addFile(file, path);
      });

      this.$refs.inputDirUpload.value = '';
    },

    mkdir() {
      const path = this.newDirName;

      this.handler.createDir(path);

      this.newDirShow = false;
      this.newDirName = '';
    },

    cancel() {
      this.newDirShow = false;
      this.newDirName = '';
    },
  },
};
</script>

<style lang="scss" scoped></style>
