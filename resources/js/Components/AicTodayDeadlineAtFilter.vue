<template>
  <div>
    <v-switch
      label="本日の締切分"
      hide-details
      dense
      :input-value="active"
      @change="change"
    />
  </div>
</template>

<script>
import { filterSerialize as serialize } from '../lib/utils';

export default {
  name: 'TodayDeadlineAtFilter',
  props: ['filter', 'target'],
  computed: {
    active() {
      const { startOfDay, endOfDay } = this.getStartEndOfToday();

      const { from, to } = this.filter[this.target];

      return startOfDay == from && endOfDay == to;
    },
    _filter: {
      get() {
        return this.filter;
      },
      set(value) {
        this.$emit('update:filter', value);
      },
    },
  },

  methods: {
    getStartEndOfToday() {
      const format = 'YYYY-MM-DD HH:mm:ss';

      return {
        startOfDay: this.$moment()
          .startOf('day')
          .format(format),
        endOfDay: this.$moment()
          .endOf('day')
          .format(format),
      };
    },

    createTodayFilters(targets, from, to) {
      return targets.reduce(
        (acc, cur) => ({
          ...acc,
          [cur]: {
            from: from,
            to: to,
          },
        }),
        {},
      );
    },

    reset() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: {
            from: null,
            to: null,
          },
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },

    // フィルター送信
    submit() {
      const { startOfDay, endOfDay } = this.getStartEndOfToday();

      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: {
            from: startOfDay,
            to: endOfDay,
          },
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },

    change(active) {
      active ? this.submit() : this.reset();
    },
  },
};
</script>
