<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ProofFile;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Helpers\PdfParser;
use App\Helpers\UnnaturalTextCheckRequester;
use App\Helpers\ImageAlertRequester;
use Symfony\Component\Yaml\Yaml;
use App\Helpers\ImageAlertExamHelper;


use Illuminate\Http\Request;

class ReplicateTestController extends Controller
{
    /**
     * 
     */
    public function start()
    {
        $texts = [
            "報道関係各位0年0月0日News Releaseオトナの女性も楽しめる地域最大級のカプセルトイ専門店『ガシャポンのデパート』を名古屋と熊本にオープン",
            "【確認依頼】VS PARKららぽーと愛知東郷店 公式HP（新着ニュース）掲載内容9月14日 8：00公開予定★VS PARK ららぽーと愛知東郷店 OPEN記念 InstagramキャンペーンInstagramキャンペーン「世界初登場アクティビティの体験フォトを投稿してオリジナルマスクをGETしよう」「VS PARKら ?",
        ];

        $requesters = [];
        foreach ($texts as $text) {
            array_push($requesters, new UnnaturalTextCheckRequester($text));
        }

        foreach ($requesters as $requester) {
            $requester->requet_and_polling();
        }
        
        return "start";
    }
    
    public function recieve()
    {
        return "recieve";
    }
    
    public function fileList()
    {
        $texts = ["気象庁によると、９日午前５時１４分ごろ、石川県能登地方で震度４の地震があった。"];
        $base_dir = "replicate";
        $files = Storage::disk('s3')->allfiles($base_dir);

        $result_list = [];
        foreach ($texts as $text) {
            $result = $this->findUnnaturalTextResult($files, $text);
            array_push($result_list, $result);
        }

        $response = ["result" => "success", "textcheck_result" => $result_list ];

        
        
        return "fileList";
    }

    private function outputPdfText($path)
    {
        // pdfのテキスト取得テスト
        $parser = new PdfParser(
            config('filesystems.disks.s3.bucket'),
            $path
        );

        $texts = data_get($parser->parseByPage(), 'content');
        dd($texts);
    }

    private function outputPdfImage($pdf_path)
    {
        $pdf_file_path = pathinfo($pdf_path, PATHINFO_FILENAME);
        
        // pdfから画像を保存
        $parser = new PdfParser(
            config('filesystems.disks.s3.bucket'),
            $pdf_path
        );
        
        $images = data_get($parser->parseForImage(), 'images');

        $output_dir = "error/image";
        
        foreach ($images as $index => $image) {
            $content = $image->getContent();
            $format = $parser->getImageFormat($image);
            $ext = $format;
            
            $path = sprintf("%s/%s/%02d", $output_dir, $pdf_file_path, $index);
            if ($ext !== "") {
                $path .= ".{$ext}";
            }
            
            Storage::disk('temp')->put($path, $content);
        }

        $image_num = count($images);
        echo("image num = {$image_num}<br>");
    }
    
    private function callChatGptOpenAI()
    {
        $apikey = "***************************************************";
        $url = "https://api.openai.com/v1/chat/completions";

        $messages = [
            ['role' => 'user', 'content' => "大谷翔平について教えて"],
         ];
        // リクエストボディ
        $data = array(
            'model' => 'gpt-3.5-turbo',
            'messages' => $messages,
            'max_tokens' => 500,
        );

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apikey
            ])
            ->accept('application/json')
            ->post($url, $data);

        $res_json = $response->json();
        $result_message = $res_json["choices"][0]["message"]["content"];
        
        // 結果を出力
//        echo $result_message;
        array_push($messages, ['role' => 'system', 'content' => $res_json["choices"][0]["message"]["content"]]);
        array_push($messages, ['role' => 'user', 'content' => '具体的な記録は？']);

        // リクエストボディ
        $data = array(
            'model' => 'gpt-3.5-turbo',
            'messages' => $messages,
            'max_tokens' => 500,
        );

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apikey
            ])
            ->accept('application/json')
            ->post($url, $data);

        $res_json = $response->json();
        $result_message = $res_json["choices"][0]["message"]["content"];
        
        // 結果を出力
        echo $result_message;

    }

    private function postChatGptAzure($messages)
    {
        $apikey = "********************************";
        $url = "https://tf-azure-openai-bapi.openai.azure.com/openai/deployments/tf-bapi-gpt-35-turbo-0613/chat/completions?api-version=2023-03-15-preview";

        // リクエストボディ
        $data = array(
            'messages' => $messages,
            'max_tokens' => 800,
            'temperature' => 0.7,
            'frequency_penalty' => 0,
            'presence_penalty' => 0,
            'top_p' => 0.95
        );

        $response = Http::withHeaders([
            'api-key' => $apikey
            ])
            ->accept('application/json')
            ->post($url, $data);

        $res_json = $response->json();

        return $res_json;
    }

    private function callChatGptAzure1()
    {
        // azure用
        $messages = [
            ['role' => 'user', 'content' => "大谷翔平について教えて"],
        ];
        $res_json = $this->postChatGptAzure($messages);
        $result_message = $res_json["choices"][0]["message"]["content"];
        
        // 結果を出力
//        echo $result_message;
        
        array_push($messages, ['role' => 'system', 'content' => $res_json["choices"][0]["message"]["content"]]);
        array_push($messages, ['role' => 'user', 'content' => '具体的な記録は？']);

        $res_json = $this->postChatGptAzure($messages);
        $result_message = $res_json["choices"][0]["message"]["content"];
        
        // 結果を出力
        echo $result_message;
    }
    
    private function callChatGptAzure2()
    {
        // azure用
        $pdf_content = "『本屋さんのガシャポンのデパート』は、書店内に出店するカプセルトイ専門点です。このたびオーブンする『本屋さんのガシャポンのデパート』TSUTAYA上江別店は、カプセルトイ自販機の設置数が地域最大級※となる610面展開。書籍同様、マジたくさんの品ぞろえの中から気になる商品を見つける体験を楽しめます。また、『本屋さんのガシャポンのデパート』限定のブックカバーやしおり、ステッカーなどをプレゼントするキャンペーン開催も予定しています。（無くなり次第終了致します)";
        $messages = [
//            ['role' => 'user', 'content' => "次の文章に誤字脱字があればリスト形式で指摘してください。\n-----------------------\n『本屋さんのガシャポンのデパート』は、書店内に出店するカプセルトイ専門点です。このたびオーブンする『本屋さんのガシャポンのデパート』TSUTAYA上江別店は、カプセルトイ自販機の設置数が地域最大級※となる610面展開。書籍同様、たくさんの品ぞろえの中から気になる商品を見つける体験を楽しめます。また、『本屋さんのガシャポンのデパート』限定のブックカバーやしおり、ステッカーなどをプレゼントするキャンペーン開催も予定しています。"],
            ['role' => 'user', 'content' => "次の文章に誤字脱字があればリスト形式で指摘してください。\n" . $pdf_content],
        ];
        $res_json = $this->postChatGptAzure($messages);
        if (array_key_exists("error", $res_json)) {
            echo("error!!");
        }

        $result_messages = array($res_json["choices"][0]["message"]["content"]);
        
        array_push($messages, ['role' => 'system', 'content' => $result_messages[0]]);
        array_push($messages, ['role' => 'user', 'content' => '表現に違和感がある箇所があれば指摘してください。']);

        $res_json = $this->postChatGptAzure($messages);
        $result_message = $res_json["choices"][0]["message"]["content"];
        array_push($result_messages, $result_message);
        // 結果を出力
        echo("result 1-------------------<br>");
        echo $result_messages[0] . "<br>";
        echo("result 2-------------------<br>");
        echo $result_messages[1] . "<br>";

        $content = json_encode($result_messages, JSON_UNESCAPED_UNICODE);

        $output_path = "test/gpt.json";
        Storage::disk('temp')->put($output_path, $content);

        $content = Storage::disk('temp')->get($output_path);
        $content_json = json_decode($content);
        dd($content_json);
    }

    private function isDispExamResult($result, $exam_item) {
        if (array_key_exists("follow_up_condition", $exam_item)) {
            $yes_min = 0;
            
            if (preg_match('/(\d+)\+/', $exam_item["follow_up_condition"], $mutchs)) {
                $yes_min = (float)$mutchs[1];
            }
            else {
                return false;
            }

            $yes_value = (float)$result;
            if ($yes_min <= $yes_value) {
                return true;
            }
            else {
                return false;
            }
        }
        else {
            if (preg_match("/yes/i", $result)) {
                return true;
            }
            else {
                return false;
            }
        }
    }
    
    // 画像アラートの結果を表示する
    private function result_image_alert()
    {
        $json_path = "image_alert/response/0.8.image_alert.112.0.json";
        // 設定ファイルのロード
        $image_alert_exam = new ImageAlertExamHelper();
        $exam_items = $image_alert_exam->getYamlData();
        if ($exam_items === null) {
            Log::debug("yaml file open error");
            return;
        }
        
        $base_path = 'image_alert/images';

        // APIのレスポンスファイルを探す
        if (Storage::disk('temp')->exists($json_path) == true) {
            $content = Storage::disk('temp')->get($json_path);
            $content_json = json_decode($content, true);
        }
        else {
            $content_json = [];
        }

        // 表示する結果を集める
        $image_results = [];
        // 画像アラートの結果
        $exam_results = [];
        
        if (!empty($content_json)) {
            $result = "success";
            
            foreach ($content_json["output"]["result"] as $index => $output_result) {
                if ($this->isDispExamResult($output_result[0], $exam_items[$index])) {
                    $value = "";
                    if (is_numeric($output_result[0])) {
                        $value = $output_result[0];
                    }
                    
                    $exam_result = [ "keyword" => $exam_items[$index]["keyword"], "value" => $value ];
                    array_push($exam_results, $exam_result);
                }
            }

//            $image_result = ["image_path" => $image_path, "exam_results" => $exam_results, "result" => $result];
            dd($exam_results);
        }

        $response = ["result" => "success", "image_results" => $image_results ];

        return $response;
    }

    private function py_api()
    {
//        $response = Http::get('http://localhost:8080/test');
        
//        $data_path = "test.txt";
        // pdfをfastapiにpostするサンプル
        $data_path = "sample.pdf";
        $content = Storage::disk('temp')->get($data_path);
        // python用サーバーにリクエストすること。現在はサンプル
        $response = Http::attach(
            'upload_file', $content, $data_path)
            ->post('http://localhost:8080/uploadfile');
        return $response->body();
    }
    
    public function test()
    {
        $path = "cache/temp/error/69_asada_test_20230712_02.pdf";
//        $path = "cache/temp/error/70_asada_test_20230712_03.pdf";
//        $path = "cache/temp/error/68.pdf";
//        $path = "cache/temp/image_sample.pdf";
//        $this->outputPdfText($path);
//        $this->outputPdfImage($path);
//        $this->callChatGptAzure2();
//        $this->result_image_alert();
//        return $this->py_api();
        return "hello";
        
        
        /*
        $text = '『ガシャポンのデパート』を名古屋と熊本にオープン添付資料(1/2)1：地域最大級の設置面数！キャラクターから雑貨まで圧倒的な品揃えはもはや“デパート”！バンダイの商品はもちろん、様々なブランドの商品が購入できる。※取り扱い商品例です。※画像コメント待ち※画像コメント待ち仮面ライダーセイバーコレクタブルワンダーライドブック GP03コメント※画像コメント待ちだんごむし 07 だんごむしとアルマジロトカゲコメントゴジラ HG D＋ゴジラ 04コメント※画像コメント待ちぴた！でふぉめ BTS CHARACTER和楽器これくしょん傘立ての鍵マーカー大太鼓、三味線、琴など全 5 種の和楽器が発売店舗などにある鍵付き傘立てをモチーフにした傘用。可動フィギュアと合わせて遊べるサイズとなっておりマーカーが登場！実際に鍵を使って開閉できる本、全て集めると遊びの幅が広がります。格派ギミック搭載。(C)Union-creative?GB2／KITAN CLUBアクリルスタンドコメント東海限定 スガキヤおでかけグッズ九州限定 リョーユーパン ポーチ＆巾着スーちゃんのポーチ＆トートバッグ。みそ煮込うどん、スガキヤラーメン、ラーメンフマンハッタン、牛乳サンド、あんドーナツ、大きか銀チョコ、マンハッタン（4）、ヤォークもポーチに！みそ煮込うどんとスガキヤラーメンポーチは実際の商品が入るキリンゴがデザインされたポーチまたは巾着です。裏面はリョーユーパンのキャラク大きさです。ター、リョーちゃんがポイントです。監修・・・スガキコシステムズ株式会社・寿がきや食品株式会社監修・・・株式会社リョーユーパン（C） EPOCH（C） EPOCH※「ガシャポン」は株式会社バンダイの登録商標です※ニュースリリースの情報は、発表日現在のものです。発表後予告なしに内容が変更されることがあります。あらかじめご了承ください。 ※画像はイメージです。必要に応じて下記の補足を表示する。・ららぽーと名古屋みなとアクルス店にて販売予定必要に応じて下記の補足を表示する。・ゆめタウン光の森店にて販売予定';
        $text1 = mb_substr($text, 0, 10);
        $text2 = mb_substr($text, 0, 10);
        Log::error($text1);
        //error_log("error test");
        */
        /*
        $exam_items_path = "yaml_data/exam_items.yaml";
        $content = Storage::disk('temp')->get($exam_items_path);
        $exam_items = Yaml::parse($content);
        //dd($exam_items);
        */
        /*
        foreach ($exam_items as $index => $exam_item) {
            if (array_key_exists("follow_up_condition", $exam_item)) {
                print("{$exam_item['question']}\n");
            }
        }
        */
        /*
        $path = "/abc/def/ghi/image.jpg";
        $filepath = pathinfo($path);
        $filepath_no_ext = sprintf("%s/%s", $filepath["dirname"], $filepath["filename"]);
        */
//        dd($filepath_no_ext);
        /*

        $prompt_list = ["\"what?\"", "\"why?\"", "\"who?\""];
        $prompt_text = "[";

        $prompt_num = count($prompt_list);
        foreach ($prompt_list as $index => $prompt) {
            $prompt_text .= "[{$prompt}]";
            
            if ($index == $prompt_num - 1) {
                $prompt_text .= "]";
            }
            else {
                $prompt_text .= ",";
            }
        }
        dd($prompt_text);
        */
        return "";
    }


    // 単体の画像で画像アラートを開始
    public function image_alert_simple_start()
    {
        $image_path = "image_alert/images/0.8.image_alert.112.0.jpg";
        // プロンプトのリストを生成
        $image_alert_exam = new ImageAlertExamHelper();
        $prompt_text = $image_alert_exam->getPromptText();

        // 画像のロードと変換
        $content = Storage::disk('temp')->get($image_path);
        $content_encoded = base64_encode($content);
        // 画像タイプで変更すること
        $mime = "image/jpeg";
        $data_uri = sprintf("data:%s;base64,%s", $mime, $content_encoded);
        
        // 処理開始API
        $response = Http::withHeaders([
            'Authorization' => 'Token ' . config('image-alert.auth_token')
        ])
        ->post(config('image-alert.url'). '/v1/predictions', [
            "version" => config('image-alert.version'),
            "input" => [
                'image' => $data_uri,
                'prompt' => $prompt_text,
                'prompt_is_json' => true
            ]
        ]);

        $res_json = $response->json();
        dd($res_json);
    }

    private function image_alert_simple_recieve()
    {
        $recieve_id = "obt3crjbv44tvtm25xsvv4zg64";
        $json_path = "image_alert/response/0.8.image_alert.112.0.json";

        $response = Http::withHeaders(['Authorization' => 'Token ' . config('image-alert.auth_token')])
            ->get(config('image-alert.url') . '/v1/predictions/' . $recieve_id);

        $res_json = $response->json();

        if ($res_json !== null) {
            if ($res_json["status"] === "succeeded") {
                $content = $response->body();
                Storage::disk('temp')->put($json_path, $content);
            }
        }
        
        dd($res_json);
    }

    /**
     * 
     */
    private function image_alert_pdf_start()
    {
        $pdf_path = "/image_alert/temp/image_sample.pdf";
        $pdf_file_path = pathinfo($pdf_path, PATHINFO_FILENAME);
        
        // pdfから画像を保存
        $parser = new PdfParser(
            config('filesystems.disks.s3.bucket'),
            $pdf_path
        );
        
        $images = data_get($parser->parseForImage(), 'images');
        $base_path = 'image_alert/images';

        $image_path_list = [];

        foreach ($images as $index => $image) {
            $content = $image->getContent();
            $path = sprintf("%s/%s_%02d.jpg", $base_path, $pdf_file_path, $index);
            Storage::disk('s3')->put($path, $content);
            array_push($image_path_list, $path);
        }

        // APIヘルパを生成
        $requesters = [];
        foreach ($image_path_list as $index => $image_path) {
            array_push($requesters, new ImageAlertRequester($pdf_path, $index, $image_path));
        }

        // APIをポーリング
        foreach ($requesters as $requester) {
            $requester->requet_and_polling();
        }

        return "start";
    }
    
    /**
     * 
     */
    public function image_alert_start()
    {
        $this->image_alert_simple_start();
        return "start";
    }
    
    public function image_alert_recieve()
    {
        $this->image_alert_simple_recieve();
        return "recieve";
    }
    
    
    // 不自然なテキストのチェック結果の検索
    private function findUnnaturalTextResult($files, $text) {
        $tokens_list = [];

        foreach ($files as $path) {
            $content = Storage::disk('s3')->get($path);
            $content_json = json_decode($content, true);
            
            if ($text === $content_json["input"]["text"]) {
                return $content_json["output"];
            }
        }
        
        return ["result" => "not found"];
    }
    
}
