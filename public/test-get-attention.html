<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kairu Animation Test - Get Attention, Idle & Writing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .canvas-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 10px;
        }

        canvas {
            border: 1px solid #ddd;
            background: #f9f9f9;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        button {
            margin: 5px;
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }

        button:hover {
            background: #0056b3;
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .status {
            text-align: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #e9ecef;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🎭 Kairu Animation Test - Get Attention, Idle & Writing</h1>

        <div class="info">
            <strong>Test Details:</strong><br>
            • Sprite Sheet: kairu_sprite_sheet.png<br>
            • Grid: 10 columns × 91 rows<br>
            • Frame Size: 252×190 pixels<br>
            • Get Attention: Row 34, Col 2, 29 frames<br>
            • Idle: Row 0, Col 0, 21 frames<br>
            • Writing: Row 71, Col 4, 22 frames<br>
            • Thinking: Row 69, Col 2, 22 frames<br>
            • Congratulate: Row 49, Col 8, 34 frames<br>
            • Test all animations to verify proper frame mapping
        </div>

        <div class="canvas-container">
            <canvas id="characterCanvas" width="252" height="190"></canvas>
        </div>

        <div class="controls">
            <button onclick="testGetAttention()" id="testBtn">Test Get Attention (29 frames)</button>
            <button onclick="testIdle()" id="idleBtn">Test Idle Animation (21 frames)</button>
            <button onclick="testWriting()" id="writingBtn">Test Writing Animation (22 frames)</button>
            <button onclick="testThinking()" id="thinkingBtn">Test Thinking Animation (22 frames)</button>
            <button onclick="testCongratulate()" id="congratulateBtn">Test Congratulate (34 frames)</button>
            <button onclick="showFrame(34, 2)" id="firstFrameBtn">Show First Frame (Get Attention)</button>
            <button onclick="showFrame(0, 0)" id="idleFrameBtn">Show First Frame (Idle)</button>
            <button onclick="showFrame(71, 4)" id="writingFrameBtn">Show First Frame (Writing)</button>
            <button onclick="stopAnimation()" id="stopBtn">Stop Animation</button>
        </div>

        <div id="status" class="status">Loading sprite sheet...</div>
    </div>

    <script>
        class SpriteSheetAnimator {
            constructor(canvasId, spriteSheetPath, cols = 10, rows = 86) {
                this.canvas = document.getElementById(canvasId);
                this.ctx = this.canvas.getContext('2d');
                this.spriteSheet = null;
                this.cols = cols;
                this.rows = rows;
                this.frameWidth = 0;
                this.frameHeight = 0;
                this.animationTimer = null;

                this.loadSpriteSheet(spriteSheetPath);
            }

            async loadSpriteSheet(path) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        this.spriteSheet = img;
                        this.frameWidth = img.width / this.cols;
                        this.frameHeight = img.height / this.rows;



                        resolve();
                    };
                    img.onerror = reject;
                    img.src = path;
                });
            }

            renderFrame(row, col) {
                if (!this.spriteSheet) return;

                // Clear canvas with transparent background
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                // Calculate source coordinates
                const sourceX = col * this.frameWidth;
                const sourceY = row * this.frameHeight;

                // Add small padding to avoid edge artifacts (1 pixel inset)
                const padding = 1;
                const adjustedSourceX = sourceX + padding;
                const adjustedSourceY = sourceY + padding;
                const adjustedWidth = this.frameWidth - (padding * 2);
                const adjustedHeight = this.frameHeight - (padding * 2);



                // Draw frame with padding to avoid edge lines
                this.ctx.drawImage(
                    this.spriteSheet,
                    adjustedSourceX, adjustedSourceY, adjustedWidth, adjustedHeight,
                    padding, padding, this.canvas.width - (padding * 2), this.canvas.height - (padding * 2)
                );
            }

            playAnimation(row, startCol, frameCount, duration, loop = false) {
                if (this.animationTimer) {
                    clearInterval(this.animationTimer);
                }

                let currentFrame = 0;
                const frameDuration = duration / frameCount;

                console.log(`Starting animation: row ${row}, startCol ${startCol}, frameCount ${frameCount}, duration ${duration}ms`);

                this.animationTimer = setInterval(() => {
                    // Calculate current row and column with proper wrapping
                    const totalFrameIndex = startCol + currentFrame;
                    const actualRow = row + Math.floor(totalFrameIndex / this.cols);
                    const actualCol = totalFrameIndex % this.cols;


                    this.renderFrame(actualRow, actualCol);
                    currentFrame++;

                    if (currentFrame >= frameCount) {
                        if (loop) {
                            currentFrame = 0;
                        } else {
                            clearInterval(this.animationTimer);
                        }
                    }
                }, frameDuration);

                // Render first frame immediately
                this.renderFrame(row, startCol);
            }

            stopAnimation() {
                if (this.animationTimer) {
                    clearInterval(this.animationTimer);
                    this.animationTimer = null;
                    console.log('Animation stopped');
                }
            }
        }

        let animator = null;
        const statusDiv = document.getElementById('status');

        async function initializeAnimator() {
            try {
                animator = new SpriteSheetAnimator('characterCanvas', '/images/kairu/kairu_sprite_sheet.png', 10, 91);
                await animator.loadSpriteSheet('/images/kairu/kairu_sprite_sheet.png');

                statusDiv.textContent = '✅ Sprite sheet loaded successfully! Ready to test get_attention animation.';
                statusDiv.className = 'status';

                // Enable all buttons
                document.querySelectorAll('button').forEach(btn => btn.disabled = false);

                // Show first frame of get_attention
                showFrame(34, 2);

            } catch (error) {
                statusDiv.textContent = '❌ Failed to load sprite sheet';
                statusDiv.className = 'status error';
            }
        }

        function testGetAttention() {
            if (!animator) {
                statusDiv.textContent = '❌ Animator not initialized';
                return;
            }

            statusDiv.textContent = '🎬 Playing get_attention animation (29 frames)...';
            animator.playAnimation(34, 2, 29, 2000, false);

            // Update status when animation completes
            setTimeout(() => {
                statusDiv.textContent = '✅ Get_attention animation completed! All 29 frames should have been displayed.';
            }, 2100);
        }

        function testIdle() {
            if (!animator) {
                statusDiv.textContent = '❌ Animator not initialized';
                return;
            }

            statusDiv.textContent = '🎬 Playing idle animation (21 frames)...';
            animator.playAnimation(0, 0, 21, 3000, true);

            // Update status
            setTimeout(() => {
                statusDiv.textContent = '✅ Idle animation playing! Should loop continuously with 21 frames.';
            }, 500);
        }

        function testWriting() {
            if (!animator) {
                statusDiv.textContent = '❌ Animator not initialized';
                return;
            }

            statusDiv.textContent = '🎬 Playing writing animation (22 frames)...';
            animator.playAnimation(71, 4, 22, 2000, true);

            // Update status
            setTimeout(() => {
                statusDiv.textContent = '✅ Writing animation playing! Should loop continuously with 22 frames.';
            }, 500);
        }

        function testThinking() {
            if (!animator) {
                statusDiv.textContent = '❌ Animator not initialized';
                return;
            }

            statusDiv.textContent = '🎬 Playing thinking animation (22 frames)...';
            animator.playAnimation(69, 2, 22, 2500, true);

            // Update status
            setTimeout(() => {
                statusDiv.textContent = '✅ Thinking animation playing! Should loop continuously with 22 frames.';
            }, 500);
        }

        function testCongratulate() {
            if (!animator) {
                statusDiv.textContent = '❌ Animator not initialized';
                return;
            }

            statusDiv.textContent = '🎬 Playing congratulate animation (34 frames)...';
            animator.playAnimation(49, 8, 34, 2500, false);

            // Update status
            setTimeout(() => {
                statusDiv.textContent = '✅ Congratulate animation completed! All 34 frames should have been displayed.';
            }, 2600);
        }

        function showFrame(row, col) {
            if (!animator) {
                statusDiv.textContent = '❌ Animator not initialized';
                return;
            }

            animator.stopAnimation();
            animator.renderFrame(row, col);
            statusDiv.textContent = `📍 Showing frame at row ${row}, col ${col}`;
        }

        function stopAnimation() {
            if (animator) {
                animator.stopAnimation();
                statusDiv.textContent = '⏹️ Animation stopped';
            }
        }

        // Initialize when page loads
        window.addEventListener('load', initializeAnimator);

        // Disable buttons initially
        document.querySelectorAll('button').forEach(btn => btn.disabled = true);
    </script>
</body>

</html>