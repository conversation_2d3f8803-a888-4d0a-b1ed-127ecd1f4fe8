<template>
  <v-autocomplete
    :value="_value"
    :items="items"
    :label="label"
    :item-value="itemValue"
    :item-text="itemText"
    :error-messages="errors"
    :disabled="disabled"
    @change="updateValue"
    :outlined="outlined"
    multiple
    :placeholder="_value.length === 0 ? 'IP' : ''"
    class="ip-label"
    v-bind:search-input.sync="searchInput"
  >
    <template #label>
      <slot class="items" name="label" />
    </template>

    <template #selection="{ item, index }">
      <v-chip
        :color="color"
        @click:close="removeIp(index)"
        :disabled="disabled"
        :label="chipLabel"
        rounded
        class="custom-chip"
      >
        {{ getText(item) }}
      </v-chip>
    </template>
  </v-autocomplete>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      required: true,
      default: () => [],
    },
    label: {
      type: String,
      required: false,
      default: '',
    },
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    errors: {
      type: Array,
      required: false,
      default: () => [],
    },
    itemValue: {
      type: String,
      required: false,
      default: 'id',
    },
    itemText: {
      type: String | Function,
      required: false,
      default: 'name',
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    color: {
      type: String,
      required: false,
      default: 'success',
    },
    outlined: {
      type: Boolean,
      required: false,
      default: true,
    },
    chipLabel: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  computed: {
    _value: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  data() {
    return {
      searchInput: "", // 検索文字列を管理
    };
  },
  watch: {
    value() {
      this.searchInput = '';
    }
  },
  methods: {
    getText(item) {
      if (typeof this.itemText === 'string') {
        return item[this.itemText];
      }
      return this.itemText(item);
    },
    removeIp(index) {
      this._value = this._value.filter((_, i) => i !== index);
    },
    updateValue(value) {
      this._value = value;
    },
  },
};
</script>

<style lang="scss" scoped>

.ip-label::v-deep(input) {
  font-size: 14px;
  color: rgba(64, 64, 64, 1) !important;
}

/* プレースホルダーの特定対応（各ブラウザ向け） */
.ip-label::v-deep(input::placeholder) {
  font-size: 14px;
  color: rgba(64, 64, 64, 1) !important;
  opacity: 1; /* Firefox用 */
}

.custom-chip {
  border-radius: 5px; /* 角の丸みを調整 */
  margin-bottom: 4px;
  margin-top: 3px;
  padding-top: 2px;

}

/* ドロップダウンリストのスタイル */
::v-deep(.v-list-item__content) {
  font-size: 14px !important;
  color: rgba(64, 64, 64, 1) !important;
}

/* ドロップダウンの各アイテム全体にも適用 */
::v-deep(.v-list-item) {
  font-size: 14px !important;
  color: rgba(64, 64, 64, 1) !important;
}

/* ドロップダウン内のテキスト要素にも適用 */
::v-deep(.v-list-item__title) {
  font-size: 14px !important;
  color: rgba(64, 64, 64, 1) !important;
}
</style>
