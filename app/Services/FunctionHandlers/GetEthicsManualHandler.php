<?php

namespace App\Services\FunctionHandlers;

use App\Services\ManualFileService;
use Illuminate\Support\Facades\Log;

class GetEthicsManualHandler
{
    private ManualFileService $manualFileService;

    public function __construct()
    {
        $this->manualFileService = new ManualFileService();
    }

    /**
     * Get comprehensive ethics manual information for members
     */
    public function execute(array $arguments): array
    {
        $section = $arguments['section'] ?? 'overview';
        $userRole = $arguments['user_role'] ?? 'admin';

        // Check if user has access (null userRole means no access)
        if ($userRole === null) {
            return [
                'success' => false,
                'error' => 'マニュアルへのアクセス権限がありません。このマニュアルは申請者（requester）ユーザーのみがアクセス可能です。',
                'access_denied' => true,
                'user_role' => null,
                'contact_info' => [
                    'システム管理者' => '<EMAIL>',
                    'アクセス権限相談' => '<EMAIL>'
                ]
            ];
        }

        // Load manual content dynamically from files
        $manual = $this->loadManualContent($userRole);

        // Check if manual content indicates access denied
        if (isset($manual['access_denied'])) {
            return [
                'success' => false,
                'error' => 'マニュアルへのアクセス権限がありません。',
                'access_denied' => true,
                'user_role' => $userRole,
                'content' => $manual['access_denied'],
                'contact_info' => $manual['contact'] ?? []
            ];
        }

        // Map English section names to Japanese section names
        $sectionMapping = $this->getSectionMapping();
        $actualSection = $sectionMapping[$section] ?? $section;

        // Try both the mapped section and original section
        $content = null;
        if (isset($manual[$actualSection])) {
            $content = $manual[$actualSection];
        } elseif (isset($manual[$section])) {
            $content = $manual[$section];
        }

        if ($content) {
            return [
                'success' => true,
                'section' => $section,
                'actual_section' => $actualSection,
                'user_role' => $userRole,
                'content' => $content,
                'navigation' => $this->getNavigationMenu(),
                'quick_actions' => $this->getQuickActions($userRole),
                'source' => 'dynamic_file_loading'
            ];
        }

        return [
            'success' => false,
            'error' => "指定されたセクション '{$section}' が見つかりません。",
            'available_sections' => array_keys($manual),
            'section_mapping' => $sectionMapping,
            'source' => 'dynamic_file_loading'
        ];
    }

    /**
     * Map English section names to Japanese section names from markdown files
     */
    private function getSectionMapping(): array
    {
        return [
            'overview' => 'システム概要',
            'getting_started' => 'はじめに',
            'application_process' => '申請プロセス',
            'document_guidelines' => '文書ガイドライン',
            'evaluation_criteria' => '評価基準',
            'common_issues' => 'よくある問題',
            'troubleshooting' => 'トラブルシューティング',
            'faq' => 'よくある質問',
            'contact' => 'お問い合わせ'
        ];
    }

    /**
     * Load manual content from files with fallback to hardcoded content
     */
    private function loadManualContent(?string $userRole): array
    {
        try {
            // First, try to load from dynamic files
            $dynamicContent = $this->manualFileService->loadManualContent($userRole);

            // If dynamic content is not empty and has valid sections, use it
            if (!empty($dynamicContent) && $this->isValidManualContent($dynamicContent)) {
                Log::info("Successfully loaded manual content from file for role: " . ($userRole ?? 'null'));
                return $dynamicContent;
            }

            // If dynamic content fails, return minimal fallback
            Log::warning("Dynamic content loading failed for role: " . ($userRole ?? 'null') . ", using minimal fallback");
            return $this->getMinimalFallbackContent();

        } catch (\Exception $e) {
            Log::error("Error loading manual content: " . $e->getMessage());
            return $this->getMinimalFallbackContent();
        }
    }

    /**
     * Validate if manual content has required structure
     */
    private function isValidManualContent(array $content): bool
    {
        // Check if content is not empty
        if (empty($content)) {
            return false;
        }

        // Check if content has at least some sections with proper structure
        foreach ($content as $sectionKey => $sectionData) {
            if (is_array($sectionData) && isset($sectionData['title'])) {
                return true; // Found at least one valid section
            }
        }

        return false;
    }

    /**
     * Get minimal fallback content when dynamic loading fails
     */
    private function getMinimalFallbackContent(): array
    {
        return [
            'overview' => [
                'title' => 'AIC倫理確認システム',
                'description' => 'マニュアルファイルの読み込みに失敗しました。システム管理者にお問い合わせください。',
                'items' => [
                    'マニュアルファイルが見つからないか、読み込みに失敗しました。',
                    'システム管理者に連絡して、マニュアルファイルの状態を確認してください。',
                    'ファイルパス: resources/js/Pages/Assistant/'
                ],
                'subsections' => []
            ],
            'getting_started' => [
                'title' => 'はじめに',
                'description' => 'マニュアルファイルの読み込みに失敗しました。',
                'items' => ['システム管理者にお問い合わせください。'],
                'subsections' => []
            ],
            'contact' => [
                'title' => 'お問い合わせ',
                'description' => 'マニュアルファイルの読み込みに失敗しました。',
                'items' => ['システム管理者にお問い合わせください。'],
                'subsections' => []
            ]
        ];
    }

    /**
     * Get navigation menu for the ethics manual
     */
    private function getNavigationMenu(): array
    {
        return [
            'overview' => 'システム概要',
            'getting_started' => '利用開始ガイド',
            'application_process' => '申請プロセス',
            'document_guidelines' => '文書ガイドライン',
            'evaluation_criteria' => '評価基準',
            'common_issues' => 'よくある問題',
            'troubleshooting' => 'トラブルシューティング',
            'faq' => 'よくある質問',
            'contact' => 'お問い合わせ'
        ];
    }

    /**
     * Get quick actions based on user role
     */
    private function getQuickActions(string $userRole): array
    {
        $commonActions = [
            'new_application' => [
                'title' => '新規申請作成',
                'description' => '新しい倫理確認申請を作成する',
                'action' => 'navigate_to_new_application'
            ],
            'view_history' => [
                'title' => '申請履歴確認',
                'description' => '過去の申請状況を確認する',
                'action' => 'navigate_to_history'
            ],
            'check_status' => [
                'title' => 'ステータス確認',
                'description' => '現在進行中の申請状況を確認する',
                'action' => 'check_application_status'
            ],
            'guidelines' => [
                'title' => 'ガイドライン確認',
                'description' => '最新の倫理ガイドラインを確認する',
                'action' => 'view_guidelines'
            ]
        ];

        return $commonActions;
    }
}