<template>
  <v-autocomplete
    :value="_value"
    :items="items"
    :label="label"
    :item-value="itemValue"
    :item-text="itemText"
    :error-messages="errors"
    :disabled="disabled"
    @change="updateValue"
    :outlined="outlined"
    multiple
    :placeholder="_value && _value.length > 0 ? '' : 'IP'"
    v-bind:search-input.sync="searchInput"
  >
    <template #label>
      <slot name="label" />
    </template>

    <template #selection="{ item, index }">
      <v-chip
        close
        text-color="white"
        :color="color"
        @click:close="removeIp(index)"
        :disabled="disabled"
        :label="chipLabel"
      >
        {{ getText(item) }}
      </v-chip>
    </template>
  </v-autocomplete>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      required: true,
      default: () => [],
    },
    label: {
      type: String,
      required: false,
      default: '',
    },
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    errors: {
      type: Array,
      required: false,
      default: () => [],
    },
    itemValue: {
      type: String,
      required: false,
      default: 'id',
    },
    itemText: {
      type: String | Function,
      required: false,
      default: 'name',
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    color: {
      type: String,
      required: false,
      default: 'success',
    },
    outlined: {
      type: Boolean,
      required: false,
      default: true,
    },
    chipLabel: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  computed: {
    _value: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  data() {
    return {
      searchInput: "", // 検索文字列を管理
    };
  },
  watch: {
    value() {
      this.searchInput = '';
    }
  },
  methods: {
    getText(item) {
      if (typeof this.itemText === 'string') {
        return item[this.itemText];
      }

      return this.itemText(item);
    },
    removeIp(index) {
      this._value = this._value.filter((_, i) => i !== index);
    },
    updateValue(value) {
      this._value = value;
    },
  },
};
</script>

<style lang="scss" scoped></style>
