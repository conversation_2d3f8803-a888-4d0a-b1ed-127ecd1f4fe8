<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\FunctionHandlers\GetEthicsManualHandler;

class EthicsManualHandlerTest extends TestCase
{
    /**
     * Test GetEthicsManualHandler functionality
     */
    public function test_ethics_manual_handler_returns_overview()
    {
        $handler = new GetEthicsManualHandler();
        
        $result = $handler->execute(['section' => 'overview', 'user_role' => 'member']);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('overview', $result['section']);
        $this->assertEquals('member', $result['user_role']);
        $this->assertArrayHasKey('content', $result);
        $this->assertArrayHasKey('navigation', $result);
        $this->assertArrayHasKey('quick_actions', $result);
    }

    /**
     * Test ethics manual handler with different sections
     */
    public function test_ethics_manual_handler_different_sections()
    {
        $handler = new GetEthicsManualHandler();
        
        $sections = ['getting_started', 'application_process', 'document_guidelines', 'evaluation_criteria'];
        
        foreach ($sections as $section) {
            $result = $handler->execute(['section' => $section, 'user_role' => 'member']);
            
            $this->assertTrue($result['success'], "Failed for section: {$section}");
            $this->assertEquals($section, $result['section']);
            $this->assertArrayHasKey('content', $result);
        }
    }

    /**
     * Test ethics manual handler with invalid section
     */
    public function test_ethics_manual_handler_invalid_section()
    {
        $handler = new GetEthicsManualHandler();
        
        $result = $handler->execute(['section' => 'invalid_section', 'user_role' => 'member']);
        
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('error', $result);
        $this->assertArrayHasKey('available_sections', $result);
    }

    /**
     * Test navigation menu structure
     */
    public function test_navigation_menu_structure()
    {
        $handler = new GetEthicsManualHandler();
        
        $result = $handler->execute(['section' => 'overview', 'user_role' => 'member']);
        
        $navigation = $result['navigation'];
        
        $expectedSections = [
            'overview', 'getting_started', 'application_process', 
            'document_guidelines', 'evaluation_criteria', 'common_issues',
            'troubleshooting', 'faq', 'contact'
        ];
        
        foreach ($expectedSections as $section) {
            $this->assertArrayHasKey($section, $navigation);
        }
    }

    /**
     * Test quick actions for different user roles
     */
    public function test_quick_actions_for_different_roles()
    {
        $handler = new GetEthicsManualHandler();

        // Test member role
        $memberResult = $handler->execute(['section' => 'overview', 'user_role' => 'member']);
        $memberActions = $memberResult['quick_actions'];

        $this->assertArrayHasKey('new_application', $memberActions);
        $this->assertArrayHasKey('view_history', $memberActions);
        $this->assertArrayHasKey('check_status', $memberActions);
        $this->assertArrayHasKey('guidelines', $memberActions);

        // Test any other role (all should use member manual now)
        $otherResult = $handler->execute(['section' => 'overview', 'user_role' => 'admin']);
        $otherActions = $otherResult['quick_actions'];

        $this->assertArrayHasKey('new_application', $otherActions);
        $this->assertArrayHasKey('guidelines', $otherActions);
    }

    /**
     * Test content structure completeness
     */
    public function test_content_structure_completeness()
    {
        $handler = new GetEthicsManualHandler();
        
        // Test all main sections have required content
        $sections = ['overview', 'getting_started', 'application_process', 'document_guidelines'];
        
        foreach ($sections as $section) {
            $result = $handler->execute(['section' => $section, 'user_role' => 'member']);
            
            $this->assertTrue($result['success']);
            $this->assertArrayHasKey('title', $result['content']);
            $this->assertNotEmpty($result['content']['title']);
        }
    }

    /**
     * Test error handling for missing parameters
     */
    public function test_error_handling_missing_parameters()
    {
        $handler = new GetEthicsManualHandler();
        
        // Test with empty arguments (should use defaults)
        $result = $handler->execute([]);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('overview', $result['section']);
        $this->assertEquals('member', $result['user_role']);
    }

    /**
     * Test specific content sections
     */
    public function test_specific_content_sections()
    {
        $handler = new GetEthicsManualHandler();
        
        // Test overview content
        $overviewResult = $handler->execute(['section' => 'overview']);
        $this->assertArrayHasKey('key_points', $overviewResult['content']);
        $this->assertArrayHasKey('benefits', $overviewResult['content']);
        
        // Test getting started content
        $gettingStartedResult = $handler->execute(['section' => 'getting_started']);
        $this->assertArrayHasKey('prerequisites', $gettingStartedResult['content']);
        $this->assertArrayHasKey('first_steps', $gettingStartedResult['content']);
        
        // Test application process content
        $processResult = $handler->execute(['section' => 'application_process']);
        $this->assertArrayHasKey('step_by_step', $processResult['content']);
        $this->assertArrayHasKey('timeline', $processResult['content']);
    }

    /**
     * Test FAQ section content
     */
    public function test_faq_section_content()
    {
        $handler = new GetEthicsManualHandler();
        
        $result = $handler->execute(['section' => 'faq']);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('questions', $result['content']);
        $this->assertIsArray($result['content']['questions']);
        $this->assertNotEmpty($result['content']['questions']);
    }

    /**
     * Test troubleshooting section content
     */
    public function test_troubleshooting_section_content()
    {
        $handler = new GetEthicsManualHandler();
        
        $result = $handler->execute(['section' => 'troubleshooting']);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('common_problems', $result['content']);
        $this->assertArrayHasKey('contact_support', $result['content']);
    }

    /**
     * Test contact section content
     */
    public function test_contact_section_content()
    {
        $handler = new GetEthicsManualHandler();
        
        $result = $handler->execute(['section' => 'contact']);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('support_channels', $result['content']);
        $this->assertArrayHasKey('escalation', $result['content']);
    }
}
