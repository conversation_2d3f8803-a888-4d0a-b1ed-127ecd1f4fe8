<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Proof\DeleteCompletionMark;
use App\Actions\User\AicUpdateUserAction;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Models\User;
use App\Models\UserType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserController\UpdateRequest;
use App\Models\Category;
use App\Models\IntellectualProperty;
use App\Models\ProofBusinessCategory;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;

class UserController extends Controller
{
    public function index()
    {
        // request()でdeleted_atがtrueならwithTrashed()を使用
        // request()でdeleted_atがfalseならwithTrashed()を使用しない
        if(request()->has('deleted_at') && request()->deleted_at === 'true') {
            Log::info('deleted_atがtrueの時');
            Log::info('deleted_atの値: ' . request()->deleted_at);
            // admin_roleがtrueの時はusers.user_roleがadmin_roleのユーザーを取得
            if(request()->has('admin_role') && request()->admin_role === 'true') {
                Log::info('admin_roleの時');
                $paginator = User::filter(request()->all())
                    ->where('user_role', 'admin_role')
                    ->with([
                        'ips',
                        'ips.parent',
                        'categories'
                    ])
                    ->withTrashed()
                    ->orderByDesc('id')
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

            } else {
                Log::info('admin_roleではない時');
                $paginator = User::filter(request()->all())
                    ->with([
                        'ips',
                        'ips.parent',
                        'categories'
                    ])
                    ->withTrashed()
                    ->orderByDesc('id')
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

            }
        } else {
            // admin_roleがtrueの時はusers.user_roleがadmin_roleのユーザーを取得
            if(request()->has('admin_role') && request()->admin_role === 'true') {
                Log::info('admin_roleの時');
                $paginator = User::filter(request()->all())
                    ->where('user_role', 'admin_role')
                    ->with([
                        'ips',
                        'ips.parent',
                        'categories'
                    ])
                    ->orderByDesc('id')
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

            } else {
                Log::info('deleted_atがfalseの時');
                Log::info('deleted_atの値: ' . request()->deleted_at);
                $paginator = User::filter(request()->all())
                    ->with([
                        'ips',
                        'ips.parent',
                        'categories'
                    ])
                    ->orderByDesc('id')
                    ->paginate(self::PER_PAGE)
                    ->appends(request()->except(['page', '_token']));

            }
        }

        $userTypes = UserType::all();
        // adminはメンバー、memberはサポーター, requesterは申請者に変更
        $userTypes = $userTypes->map(function ($userType) {
            if ($userType->value === 'admin') {
                $userType->text = 'メンバー';
            } elseif ($userType->value === 'member') {
                $userType->text = 'サポーター';
            } elseif ($userType->value === 'requester') {
                $userType->text = '申請者';
            }
            return $userType;
        });
        // メンバー、サポーター、申請者の順番に並び替え
        $userTypes = $userTypes->sortByDesc('id')->values();

        return Inertia::render("Admin/User/AicIndex", [
            'paginator' => $paginator,
            'userTypes' => $userTypes
        ]);
    }

    public function oldIndex()
    {
        $paginator = User::filter(request()->all())
            ->withTrashed()
            ->with([
                'ips',
                'ips.parent',
                'categories'
            ])
            ->orderByDesc('id')
            ->orderByDesc('created_at')
            ->paginate(self::PER_PAGE)
            ->appends(request()->except(['page', '_token']));

        $userTypes = UserType::all();

        return Inertia::render("Admin/User/Index", [
            'paginator' => $paginator,
            'userTypes' => $userTypes
        ]);
    }

    public function create()
    {
        $userTypes = UserType::all();

        // adminはメンバー、memberはサポーター, requesterは申請者に変更
        $userTypes = $userTypes->map(function ($userType) {
            if ($userType->value === 'admin') {
                $userType->text = 'メンバー';
            } elseif ($userType->value === 'member') {
                $userType->text = 'サポーター';
            } elseif ($userType->value === 'requester') {
                $userType->text = '申請者';
            }
            return $userType;
        });
        // メンバー、サポーター、申請者の順番に並び替え
        $userTypes = $userTypes->sortByDesc('id')->values();

        // requesterを除外
        $userTypes = $userTypes->filter(function ($userType) {
            return $userType->value != 'requester';
        });

        return Inertia::render('Admin/User/AicCreate', [
            'userTypes' => $userTypes
        ]);
    }

    public function oldCreate()
    {
        $userTypes = UserType::all();

        return Inertia::render('Admin/User/Create', [
            'userTypes' => $userTypes
        ]);
    }

    public function show(User $user)
    {
        $user->load([
            'ips',
            'categories'
        ]);

        $userTypes = UserType::all();

        // adminはメンバー、memberはサポーター, requesterは申請者に変更
        $userTypes = $userTypes->map(function ($userType) {
            if ($userType->value === 'admin') {
                $userType->text = 'メンバー';
            } elseif ($userType->value === 'member') {
                $userType->text = 'サポーター';
            } elseif ($userType->value === 'requester') {
                $userType->text = '申請者';
            }
            return $userType;
        });

        $user->type->text = $userTypes->where('value', $user->type_value)->first()->text;

        // メンバー、サポーター、申請者の順番に並び替え
        $userTypes = $userTypes->sortByDesc('id')->values();

        $categories = Category::all();
        $intellectualProperties = IntellectualProperty::with('parent')->children()->get();

        return Inertia::render('Admin/User/Show/AicIndex', [
            'tUser' => $user,
            'userTypes' => $userTypes,
            'categories' => $categories,
            'intellectualProperties' => $intellectualProperties
        ]);
    }

    public function oldShow(User $user)
    {
        $user->load([
            'ips',
            'categories'
        ]);

        $userTypes = UserType::all();
        $categories = Category::all();
        $intellectualProperties = IntellectualProperty::with('parent')->children()->get();

        return Inertia::render('Admin/User/Show/Index', [
            'tUser' => $user,
            'userTypes' => $userTypes,
            'categories' => $categories,
            'intellectualProperties' => $intellectualProperties
        ]);
    }

    public function update(
        UpdateRequest $request,
        User $user,
        AicUpdateUserAction $action,
        DeleteCompletionMark $deleteCompletionMark
    ) {
        try {
            DB::beginTransaction();
            Log::info('UserController@update');
            Log::info('user', [$user]);
            Log::info('user->type_value', [$user->type_value]);
            $isRequester = $user->type_value == 'requester' ? true : false;
            Log::info('isRequester', [$isRequester]);
            $action->execute($user, $request->data());

            if($request['type_value'] == 'requester' || $request['type_value'] == 'viewer'){
                $deleteCompletionMark->execute($user->id);
            }

            DB::commit();

            return redirect()->route('admin.users.index', ['isRequester' => $isRequester], self::HTTP_STATUS_SEE_OTHER)
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => $user['name'] . 'を情報を更新しました。'
                             ]);
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }
    }

    public function initPassword(User $user)
    {
        try {
            DB::beginTransaction();

            $user->password = bcrypt(Str::random(20));
            $user->email_verified_at = null;
            $user->save();

            (new ResetPasswordController())->sendEmail($user, true);

            DB::commit();

            return redirect()
                    ->route('admin.users.index', [], self::HTTP_STATUS_SEE_OTHER)
                    ->with('snackbar', [
                    'color' => 'primary',
                    'icon' => 'mdi-check-circle',
                    'title' => '成功',
                    'message' => $user['name'] . 'のパスワードをリセットしました。'
                    ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            return redirect()->back()
                             ->with([
                                'snackbar' =>  [
                                    'color' => 'error',
                                    'icon' => 'mdi-alert-circle',
                                    'title' => '失敗',
                                    'message' => 'エラーが発生しました。'
                                ],
                                'errors' => $th->errors(),
                            ]);
        }
    }
}
