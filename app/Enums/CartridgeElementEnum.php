<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
 use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

/**
 * @method static static TextTypo()
 * @method static static TextGrammar()
 * @method static static TextDayOfWeek()
 * @method static static ImageExposedSkin()
 * @method static static ImageSeductivePose()
 * @method static static ImageDangerousPlayInSports()
 * @method static static ImageCompressionRestraint()
 * @method static static ImageBlow()
 * @method static static ImageAttackingActWithAStrikingObject()
 * @method static static ImageShooting()
 * @method static static ImageBlood()
 * @method static static ImageDangerousObject()
 * @method static static ImageCrime()
 * @method static static ImageSwastikaSS()
 * @method static static ImageHexagram()
 * @method static static ImageCrescentMoonAndStars()
 * @method static static ImageCross()
 * @method static static ImageRisingSunFlagChrysanthemumEmblem()
 * @method static static ImageSickleAndHammerRedFlag()
 * @method static static ImageMilitaryUniform()
 * @method static static ImageSkull()
 */
final class CartridgeElementEnum extends Enum
{
    // 肌の露出
    const ImageExposedSkin = 'image_exposed_skin';
    // 性器
    const ImageGenitals = 'image_genitals';
    // 誘惑的ポーズ
    const ImageSeductivePose = 'image_seductive_pose';
    // 児童ポルノ
    const ImageChildPornography = 'image_child_pornography';
    // 性風俗
    const ImageSexualEntertainment = 'image_sexual_entertainment';
    // 暴力
    const ImageViolence = 'image_violence';
    // 危険物
    const ImageDangerousObject = 'image_dangerous_object';
    // 血
    const ImageBlood = 'image_blood';
    // LGBTQ+
    const ImageLgbtq = 'image_lgbtq';
    // 肌の色
    const ImageSkinColor = 'image_skin_color';
    // 民族
    const ImageEthnicity = 'image_ethnicity';
    // 酒・タバコ
    const ImageAlcoholSmoking = 'image_alcohol_smoking';
    // 賭博
    const ImageGambling = 'image_gambling';
    // 紙幣
    const ImageBanknote = 'image_banknote';
    // 薬物
    const ImageDrug = 'image_drug';
    // いじめ・虐待
    const ImageBullyingAbuse = 'image_bullying_abuse';
    // 排泄・嘔吐
    const ImageExcretionVomit = 'image_excretion_vomit';
    // ハンドサイン
    const ImageHandSign = 'image_hand_sign';
    // 殺人・自殺・自傷
    const ImageMurderSuicideSelfHarm = 'image_murder_suicide_self_harm';
    // 六芒星
    const ImageHexagram = 'image_hexagram';
    // イスラム教　肌の露出
    const ImageIslamExposedSkin = 'image_islam_exposed_skin';
    // 三日月と星
    const ImageCrescentMoonAndStars = 'image_crescent_moon_and_stars';
    // ハラルフード
    const ImageHalalFood = 'image_halal_food';
    // 十字架
    const ImageCross = 'image_cross';
    // キリスト教　偶像
    const ImageChristianityIdol = 'image_christianity_idol';
    // 鳥居
    const ImageShintoShrineArchway = 'image_shinto_shrine_archway';
    // 仏像
    const ImageBuddhistStatue = 'image_buddhist_statue';
    // 歴史的美術品
    const ImageHistoricalArtwork = 'image_historical_artwork';
    // 宗教施設
    const ImageReligiousFacilities = 'image_religious_facilities';
    // ナチス
    const ImageNazi = 'image_nazi';
    // 菊花紋章
    const ImageChrysanthemumEmblem = 'image_chrysanthemum_emblem';
    // 赤十字
    const ImageRedCross = 'image_red_cross';
    // 政党
    const ImagePoliticalParty = 'image_political_party';
    // 選挙
    const ImageElection = 'image_election';
    // 旭日旗
    const ImageRisingSunFlag = 'image_rising_sun_flag';
    // 軍服
    const ImageMilitaryUniform = 'image_military_uniform';
    // 兵器
    const ImageWeapon = 'image_weapon';
    // 国旗
    const ImageNationalFlag = 'image_national_flag';
    // 時事問題
    const ImageCurrentAffairs = 'image_current_affairs';
    // 誤字脱字
    const TextTypo = 'text_typo';
    // 文法の誤り
    const TextGrammar = 'text_grammar';
    // 常用外漢字
    const TextNonJoyoKanji = 'text_non_joyo_kanji';
    // 禁止用語
    const TextProhibitedWords = 'text_prohibited_words';
    // 商標にあたる固有名詞
    const TextTrademarkedProperNoun = 'text_trademarked_proper_noun';    
    // 曜日の誤り
    const TextDayOfWeek = 'text_day_of_week';
    // 日付
    const TextDate = 'text_date';    
    // 性表現
    const TextSexualExpression = 'text_sexual_expression';
    // LGBTQ+
    const TextLGBTQ = 'text_lgbtq';
    // 人種
    const TextRace = 'text_race';
    // 民族
    const TextEthnicity = 'text_ethnicity';
    // 容姿
    const TextAppearance = 'text_appearance';
    // 賭博
    const TextGambling = 'text_gambling';
    // 麻薬
    const TextDrugs = 'text_drugs';
    // いじめ・虐待
    const TextBullyingAbuse = 'text_bullying_abuse';
    // 殺人・自殺・自傷
    const TextMurderSuicideSelfHarm = 'text_murder_suicide_self_harm';
    // イスラム教
    const TextIslam = 'text_islam';
    // キリスト教
    const TextChristianity = 'text_christianity';
    // 仏教・神道
    const TextBuddhismShinto = 'text_buddhism_shinto';
    // ナチス
    const TextNazism = 'text_nazism';
    // 政治
    const TextPolitics = 'text_politics';
    // 戦争・紛争
    const TextWar = 'text_war';
    // 国名
    const TextCountryName = 'text_country_name';
    // 障がい者
    const TextDisability = 'text_disability';
    // 階級
    const TextClass = 'text_class';
    // 高齢者・子供
    const TextElderlyChild = 'text_elderly_child';
    // 男女平等
    const TextGenderEquality = 'text_gender_equality';
    // 時事問題
    const TextCurrentAffairs = 'text_current_affairs';  
    /*
    // スポーツにおける危険プレイ
    const ImageDangerousPlayInSports = 'image_dangerous_play_in_sports';
    // 打撃
    const ImageBlow = 'image_blow';
    // 圧迫・拘束
    const ImageCompressionRestraint = 'image_compression_restraint';
    // 打撃物による攻撃行為
    const ImageAttackingActWithAStrikingObject = 'image_attacking_act_with_a_striking_object';
    // 銃撃
    const ImageShooting = 'image_shooting';
    // 犯罪
    const ImageCrime = 'image_crime';
    // ハーケンクロイツ
    const ImageSwastika = 'image_swastika';
    // SS章
    const ImageSS = 'image_ss';
    // ライヒスアドラー
    const ImageReichsadler = 'image_reichsadler';
    // 旭日旗・菊花紋章
    const ImageRisingSunFlagChrysanthemumEmblem = 'image_rising_sun_flag_chrysanthemum_emblem';
    // 鎌と槌・赤旗
    const ImageSickleAndHammerRedFlag = 'image_sickle_and_hammer_red_flag';
    // 髑髏
    const ImageSkull = 'image_skull';
    */

    // 有効なCartridgeElementEnum
    private static array $activeCartridgeElementEnums = [
        self::ImageExposedSkin,
        self::ImageSeductivePose,
        // self::ImageBlow,
        // self::ImageCompressionRestraint,
        // self::ImageShooting,
        self::ImageBlood,
        self::ImageDangerousObject,
        // self::ImageCrime,
        // self::ImageSS,
        self::ImageHexagram,
        // self::ImageRisingSunFlagChrysanthemumEmblem,
        self::ImageGenitals,
        self::ImageChildPornography,
        self::ImageSexualEntertainment,
        self::ImageViolence,
        self::ImageLgbtq,
        self::ImageSkinColor,
        self::ImageEthnicity,
        self::ImageAlcoholSmoking,
        self::ImageGambling,
        self::ImageBanknote,
        self::ImageDrug,
        self::ImageBullyingAbuse,
        self::ImageExcretionVomit,
        self::ImageHandSign,
        self::ImageMurderSuicideSelfHarm,
        self::ImageIslamExposedSkin,
        self::ImageCrescentMoonAndStars,
        self::ImageHalalFood,
        self::ImageCross,
        self::ImageChristianityIdol,
        self::ImageShintoShrineArchway,
        self::ImageBuddhistStatue,
        self::ImageHistoricalArtwork,
        self::ImageReligiousFacilities,
        self::ImageNazi,
        self::ImageChrysanthemumEmblem,
        self::ImageRedCross,
        self::ImagePoliticalParty,
        self::ImageElection,
        self::ImageRisingSunFlag,
        self::ImageMilitaryUniform,
        self::ImageWeapon,
        self::ImageNationalFlag,
        self::ImageCurrentAffairs,
        self::TextTypo,
        self::TextGrammar,
        self::TextNonJoyoKanji,
        self::TextProhibitedWords,
        self::TextTrademarkedProperNoun,
        self::TextDayOfWeek,
        self::TextDate,
        self::TextSexualExpression,
        self::TextLGBTQ,
        self::TextRace,
        self::TextEthnicity,
        self::TextAppearance,
        self::TextGambling,
        self::TextDrugs,
        self::TextBullyingAbuse,
        self::TextMurderSuicideSelfHarm,
        self::TextIslam,
        self::TextChristianity,
        self::TextBuddhismShinto,
        self::TextNazism,
        self::TextPolitics,
        self::TextWar,
        self::TextCountryName,
        self::TextDisability,
        self::TextClass,
        self::TextElderlyChild,
        self::TextGenderEquality,
        self::TextCurrentAffairs,
    ];

    static $cartridgeData = [];

    // コンストラクタ
    public function __construct($value)
    {
        parent::__construct($value);
    }

    /**
     * Get all or a custom set of the enum values.
     *
     * @param  string|string[]|null  $keys
     *
     * @return array
     */
    public static function getValues($keys = null): array
    {
        $values = array_values(static::getConstants());

        if ($keys === null) {
            return array_intersect($values, self::$activeCartridgeElementEnums);
        }

        return collect(is_array($keys) ? $keys : func_get_args())
            ->map(function ($key) {
                return static::getValue($key);
            })
            ->filter(function ($value) {
                return in_array($value, self::$activeCartridgeElementEnums);
            })
            ->toArray();
    }

    /**
     * 検知項目の表示名を取得
     *
     * @param  string  $value
     *
     * @return string
     */
    public static function getDisplayName($value): string
    {
        return match ($value) {
            self::ImageExposedSkin => '露出',
            self::ImageSeductivePose => 'ポーズ・仕草',
            // self::ImageDangerousPlayInSports => 'スポーツにおける危険プレイ',
            // self::ImageBlow => '暴力-打撃',
            // self::ImageCompressionRestraint => '暴力-圧迫・拘束',
            // self::ImageAttackingActWithAStrikingObject => '打撃物による攻撃行為',
            // self::ImageShooting => '暴力-銃撃',
            self::ImageBlood => '出血を伴う表現',
            self::ImageDangerousObject => '閲覧者への危険物',
            // self::ImageCrime => '暴力-犯罪',
            // self::ImageSwastika => 'ハーケンクロイツ',
            // self::ImageSS => '宗教・政治-SS章',
            // self::ImageReichsadler => 'ライヒスアドラー',
            self::ImageHexagram => '六芒星',
            self::ImageCrescentMoonAndStars => '三日月と星',
            self::ImageCross => '十字架',
            // self::ImageRisingSunFlagChrysanthemumEmblem => '宗教・政治-旭日旗・菊花紋章',
            self::ImageRisingSunFlag => '旭日旗',
            // self::ImageSickleAndHammerRedFlag => '鎌と槌・赤旗',
            self::ImageMilitaryUniform => '軍服',
            // self::ImageSkull => '髑髏',
            self::ImageGenitals => '性器',
            self::ImageChildPornography => '児童ポルノ',
            self::ImageSexualEntertainment => '性風俗',
            self::ImageViolence => '暴力',
            self::ImageLgbtq => 'LGBTQ+',
            self::ImageSkinColor => '肌の色',
            self::ImageEthnicity => '民族',
            self::ImageAlcoholSmoking => '酒・タバコ',
            self::ImageGambling => '賭博',
            self::ImageBanknote => '紙幣',
            self::ImageDrug => '薬物',
            self::ImageBullyingAbuse => 'いじめ・虐待',
            self::ImageExcretionVomit => '排泄・嘔吐',
            self::ImageHandSign => 'ハンドサイン',
            self::ImageMurderSuicideSelfHarm => '殺人・自殺・自傷',
            self::ImageIslamExposedSkin => '肌の露出',
            self::ImageHalalFood => 'ハラルフード',
            self::ImageChristianityIdol => 'キリスト教　偶像',
            self::ImageShintoShrineArchway => '鳥居',
            self::ImageBuddhistStatue => '仏像',
            self::ImageHistoricalArtwork => '歴史的美術品',
            self::ImageReligiousFacilities => '宗教施設',
            self::ImageNazi => 'ナチス',
            self::ImageChrysanthemumEmblem => '菊花紋章',
            self::ImageRedCross => '赤十字',
            self::ImagePoliticalParty => '政党',
            self::ImageElection => '選挙',
            self::ImageWeapon => '兵器',
            self::ImageNationalFlag => '国旗',
            self::ImageCurrentAffairs => '時事問題',
            self::TextTypo => '誤字脱字',
            self::TextGrammar => '文法の誤り',
            self::TextNonJoyoKanji => '常用外漢字',
            self::TextProhibitedWords => '禁止用語',
            self::TextTrademarkedProperNoun => '商標にあたる固有名詞',            
            self::TextDayOfWeek => '曜日の誤り',
            self::TextDate => '日付',
            self::TextSexualExpression => '性表現',
            self::TextLGBTQ => 'LGBTQ+',
            self::TextRace => '人種',
            self::TextEthnicity => '民族',
            self::TextAppearance => '容姿',
            self::TextGambling => '賭博',
            self::TextDrugs => '麻薬',
            self::TextBullyingAbuse => 'いじめ・虐待',
            self::TextMurderSuicideSelfHarm => '殺人・自殺・自傷',
            self::TextIslam => 'イスラム教',
            self::TextChristianity => 'キリスト教',
            self::TextBuddhismShinto => '仏教・神道',
            self::TextNazism => 'ナチス',
            self::TextPolitics => '政治',
            self::TextWar => '戦争・紛争',
            self::TextCountryName => '国名',
            self::TextDisability => '障がい者',
            self::TextClass => '階級',
            self::TextElderlyChild => '高齢者・子供',
            self::TextGenderEquality => '男女平等',
            self::TextCurrentAffairs => '時事問題',
            default => parent::getDisplayName($value)
        };
    }

    /**
     * レベルの設定が無い検知項目の検知内容の文章を取得
     *
     * @param  string  $value
     *
     * @return string
     */
    public static function getDescription($value): string
    {
        return match ($value) {
            self::ImageExposedSkin => '乳首や性器の露出や肌の露出についてチェックします',
            self::ImageSeductivePose => '説明・誘惑的ポーズ',
            // self::ImageDangerousPlayInSports => '説明・スポーツにおける危険プレイ',
            // self::ImageBlow => '説明・打撃',
            // self::ImageCompressionRestraint => '首を絞める行為や拘束具や物体で相手の動きを制限しているもの。圧迫感や不快感を与えるが、軽度に制限しているものや苦痛を伴わない場面を含む。',
            // self::ImageAttackingActWithAStrikingObject => '説明・打撃物による攻撃行為',
            // self::ImageShooting => '説明・銃撃',
            self::ImageBlood => '血液があるかどうかをチェックします',
            self::ImageDangerousObject => '鋭利物や銃を画像の閲覧者の方向に向かって攻撃してるかをチェックします',
            // self::ImageCrime => '法律違反が明確であり、被害の深刻さが限定的で社会的影響が大きい場面。例えば殺人や強盗、薬物乱用など重大な損害や危険を伴う犯罪描写。',
            // self::ImageSwastika => '説明・ハーケンクロイツ',
            // self::ImageSS => 'ナチスのSS章を描写している。ただし、強調されていない場合や象徴的意味合いがない背景や装飾品を含む。',
            // self::ImageReichsadler => '説明・ライヒスアドラー',
            self::ImageHexagram => '六芒星に類似した物体(画像の状況・意図と関係なく)チェックします',
            self::ImageCrescentMoonAndStars => '説明・三日月と星',
            self::ImageCross => 'キリスト教のシンボルである"十字架"があるかチェックします',
            // self::ImageRisingSunFlagChrysanthemumEmblem => '旭日旗や菊花紋章を描写している。ただし、歴史的・政治的な強調がされていないものや特定の意図が見られない場面も含む。',
            self::ImageRisingSunFlag => '旭日旗に類似した物体(画像の状況・意図と関係なく)をチェックします',
            // self::ImageSickleAndHammerRedFlag => '説明・鎌と槌・赤旗',
            self::ImageMilitaryUniform => '軍服を想起させるものをチェックします',
            // self::ImageSkull => '説明・髑髏',
            self::ImageGenitals => '性器を想起させる表現をチェックします',
            self::ImageChildPornography => '（見た目年齢で）未成年の猥褻表現にあたるかチェックします',
            self::ImageSexualEntertainment => '性風俗を想起させるものチェックします',
            self::ImageViolence => '暴力表現が描写されているかチェックします',
            self::ImageLgbtq => 'LGBTQ+関連の表現やシンボルをチェックします',
            self::ImageSkinColor => 'ホワイトウォッシュやブラクフェイス、カラーブランドキャスティングをチェックします。',
            self::ImageEthnicity => '民族衣装等の見た目で特定の民族を表現しているかをチェックします。',
            self::ImageAlcoholSmoking => '酒やたばこが描かれているかをチェックします',
            self::ImageGambling => '賭博をモチーフにしたものや賭博を表現しているものをチェックします',
            self::ImageBanknote => '本物の紙幣らしきものが映っているものをチェックします',
            self::ImageDrug => '違法薬物を想起させるものをチェックします',
            self::ImageBullyingAbuse => 'いじめや虐待を想起させるものをチェックします',
            self::ImageExcretionVomit => '排泄・嘔吐行為が表現されているかチェックします',
            self::ImageHandSign => '描写されているハンドサインがネガティブな意味を持たないかをチェックします',
            self::ImageMurderSuicideSelfHarm => '殺人・自殺・自傷を想起させる表現をチェックします。',
            self::ImageIslamExposedSkin => 'イスラム圏での女性の顔と手以外の露出についてチェックします',
            self::ImageHalalFood => 'ハラルフードに含まれない食物をチェックします',
            self::ImageChristianityIdol => 'キリスト教に纏わる偶像があるかチェックします',
            self::ImageShintoShrineArchway => '鳥居があるかチェックします',
            self::ImageBuddhistStatue => '仏像があるかチェックします',
            self::ImageHistoricalArtwork => '歴史的美術は宗教モチーフが多いため、類似した表現が無いかチェックします',
            self::ImageReligiousFacilities => '宗教に纏わる建造物がないかチェックします',
            self::ImageNazi => 'ナチスのハーケンクロイツやSS章やライヒスアドラーに類似した物体(画像の状況・意図と関係なく)やナチス式敬礼・SSをチェックします',
            self::ImageChrysanthemumEmblem => '菊花紋章に類似した物体(画像の状況・意図と関係なく)をチェックします',
            self::ImageRedCross => 'ジュネーヴ条約で定められた赤十字の使用法に適しているかチェックします',
            self::ImagePoliticalParty => '特定の政党を示すシンボルや表現が無いかチェックします',
            self::ImageElection => '選挙活動に影響のある表現が無いかチェックします',
            self::ImageWeapon => '兵器を想起させるものが無いかチェックします',
            self::ImageNationalFlag => '国旗が無いかチェックします',
            self::ImageCurrentAffairs => '時事問題に関連する表現が無いかチェックします',
            self::TextTypo => '文字を間違えて書いてしまう誤字と文章を書く際に必要な文字を抜かしてしまう脱字。',
            self::TextGrammar => '日本語の文法として誤りがないかチェックします',
            self::TextNonJoyoKanji => '漢字が適切に使用されているかチェックします',
            self::TextProhibitedWords => 'BNグループで指定されている禁止用語に抵触しているかをチェックします',
            self::TextTrademarkedProperNoun => '商標として登録されており、通常では使用できない固有名詞をチェックします',
            self::TextDayOfWeek => '日付に対する曜日の誤り。年の情報がない場合は、入力された公開・稼動日の範囲内にある年月日になるように年を補完してチェックする。開始日と終了日の両方がない場合、現在から見て次に訪れる年月日となるように年を補完してチェックする。',
            self::TextDate => '存在しない年月日になっていないかチェックします',
            self::TextSexualExpression => '性行為や性風俗、性犯罪を想起させる表現をチェックします',
            self::TextLGBTQ => 'LGBTQ+に関する表現がないかチェックします',
            self::TextRace => '人種に関する表現がないかチェックします',
            self::TextEthnicity => '特定の民族を示す表現がないかチェックします',
            self::TextAppearance => '人の容姿を示す蔑称がないかチェックします',
            self::TextGambling => '賭博に関する表現がないかチェックします',
            self::TextDrugs => '違法薬物の名称や接種の表現が無いかチェックします',
            self::TextBullyingAbuse => 'いじめや虐待に関する表現が無いかチェックします',
            self::TextMurderSuicideSelfHarm => '殺人・自殺・自傷に関する表現が無いかチェックします',
            self::TextIslam => 'イスラム教に関する特有の固有名詞、人名、教えをチェックします。',
            self::TextChristianity => 'キリスト教に関する特有の固有名詞、人名、教えをチェックします。',
            self::TextBuddhismShinto => '仏教・神道に関する特有の固有名詞、人名、教えをチェックします。',
            self::TextNazism => 'ナチスに関する特有の固有名詞、人名、教えをチェックします。',
            self::TextPolitics => '政治的な内容や選挙に関する内容がないかチェックします',
            self::TextWar => '戦争・紛争を想起させる表現が無いかチェックします',
            self::TextCountryName => '特定の国名がないかチェックします',
            self::TextDisability => '障がい者を示す蔑称がないかチェックします',
            self::TextClass => '下位階級・地位を示す蔑称がないかチェックします',
            self::TextElderlyChild => '高齢者・子供を示す蔑称がないかチェックします',
            self::TextGenderEquality => '男性または女性の特徴や立場を決めつける表現がないかチェックします',
            self::TextCurrentAffairs => '時事問題に関連する表現が無いかチェックします',
            default => parent::getDescription($value)
        };
    }

    /**
     * 基礎値の有効無効を取得
     *
     * @param  string  $value
     *
     * @return string
     */
    public static function getDefalutActive($value): bool
    {
        return match ($value) {
            self::ImageExposedSkin => true,
            self::ImageSeductivePose => true,
            // self::ImageDangerousPlayInSports => true,
            // self::ImageBlow => true,
            // self::ImageCompressionRestraint => true,
            // self::ImageAttackingActWithAStrikingObject => true,
            // self::ImageShooting => true,
            self::ImageBlood => true,
            self::ImageDangerousObject => true,
            // self::ImageCrime => true,
            // self::ImageSwastika => true,
            // self::ImageSS => true,
            // self::ImageReichsadler => true,
            self::ImageHexagram => true,
            self::ImageCrescentMoonAndStars => true,
            self::ImageCross => true,
            // self::ImageRisingSunFlagChrysanthemumEmblem => true,
            self::ImageRisingSunFlag => true,
            // self::ImageSickleAndHammerRedFlag => true,
            self::ImageMilitaryUniform => true,
            // self::ImageSkull => true,
            self::ImageGenitals => true,
            self::ImageChildPornography => true,
            self::ImageSexualEntertainment => true,
            self::ImageViolence => true,
            self::ImageLgbtq => true,
            self::ImageSkinColor => true,
            self::ImageEthnicity => true,
            self::ImageAlcoholSmoking => true,
            self::ImageGambling => true,
            self::ImageBanknote => true,
            self::ImageDrug => true,
            self::ImageBullyingAbuse => true,
            self::ImageExcretionVomit => true,
            self::ImageHandSign => true,
            self::ImageMurderSuicideSelfHarm => true,
            self::ImageIslamExposedSkin => true,
            self::ImageHalalFood => true,
            self::ImageChristianityIdol => true,
            self::ImageShintoShrineArchway => true,
            self::ImageBuddhistStatue => true,
            self::ImageHistoricalArtwork => true,
            self::ImageReligiousFacilities => true,
            self::ImageNazi => true,
            self::ImageChrysanthemumEmblem => true,
            self::ImageRedCross => true,
            self::ImagePoliticalParty => true,
            self::ImageElection => true,
            self::ImageWeapon => true,
            self::ImageNationalFlag => true,
            self::ImageCurrentAffairs => true,
            self::TextTypo => true,
            self::TextGrammar => true,
            self::TextNonJoyoKanji => true,
            self::TextProhibitedWords => true,
            self::TextTrademarkedProperNoun => true,
            self::TextDayOfWeek => true,
            self::TextDate => true,
            self::TextSexualExpression => true,
            self::TextLGBTQ => true,
            self::TextRace => true,
            self::TextEthnicity => true,
            self::TextAppearance => true,
            self::TextGambling => true,
            self::TextDrugs => true,
            self::TextBullyingAbuse => true,
            self::TextMurderSuicideSelfHarm => true,
            self::TextIslam => true,
            self::TextChristianity => true,
            self::TextBuddhismShinto => true,
            self::TextNazism => true,
            self::TextPolitics => true,
            self::TextWar => true,
            self::TextCountryName => true,
            self::TextDisability => true,
            self::TextClass => true,
            self::TextElderlyChild => true,
            self::TextGenderEquality => true,
            self::TextCurrentAffairs => true, 
            default => true
        };
    }

    /**
     * 基礎値のレベルを取得
     *
     * @param  string  $value
     *
     * @return string
     */
    public static function getDefalutLevel($value): int | null
    {
        return match ($value) {
            self::ImageExposedSkin => 3,
            self::ImageSeductivePose => 2,
            // self::ImageDangerousPlayInSports => 2,
            // self::ImageBlow => 2,
            // self::ImageCompressionRestraint => null,
            // self::ImageAttackingActWithAStrikingObject => 2,
            // self::ImageShooting => 2,
            self::ImageBlood => 3,
            self::ImageDangerousObject => 2,
            // self::ImageCrime => null,
            // self::ImageSwastika => 2,
            // self::ImageSS => null,
            // self::ImageReichsadler => 2,
            self::ImageHexagram => null,
            self::ImageCrescentMoonAndStars => null,
            self::ImageCross => null,
            // self::ImageRisingSunFlagChrysanthemumEmblem => null,
            self::ImageRisingSunFlag => null,
            // self::ImageSickleAndHammerRedFlag => 2,
            self::ImageMilitaryUniform => 2,
            // self::ImageSkull => 2,
            self::ImageGenitals => null,
            self::ImageChildPornography => null,
            self::ImageSexualEntertainment => null,
            self::ImageViolence => 2,
            self::ImageLgbtq => 2,
            self::ImageSkinColor => null,
            self::ImageEthnicity => null,
            self::ImageAlcoholSmoking => 1,
            self::ImageGambling => null,
            self::ImageBanknote => null,
            self::ImageDrug => null,
            self::ImageBullyingAbuse => null,
            self::ImageExcretionVomit => null,
            self::ImageHandSign => null,
            self::ImageMurderSuicideSelfHarm => null,
            self::ImageIslamExposedSkin => null,
            self::ImageHalalFood => null,
            self::ImageChristianityIdol => null,
            self::ImageShintoShrineArchway => null,
            self::ImageBuddhistStatue => null,
            self::ImageHistoricalArtwork => null,
            self::ImageReligiousFacilities => null,
            self::ImageNazi => null,
            self::ImageChrysanthemumEmblem => null,
            self::ImageRedCross => null,
            self::ImagePoliticalParty => null,
            self::ImageElection => null,
            self::ImageWeapon => null,
            self::ImageNationalFlag => null,
            self::ImageCurrentAffairs => null,
            self::TextTypo => null,
            self::TextGrammar => null,
            self::TextNonJoyoKanji => 1,
            self::TextProhibitedWords => 3,
            self::TextTrademarkedProperNoun => null,
            self::TextDayOfWeek => null,
            self::TextDate => null,
            self::TextSexualExpression => 2,
            self::TextLGBTQ => 2,
            self::TextRace => null,
            self::TextEthnicity => null,
            self::TextAppearance => null,
            self::TextGambling => null,
            self::TextDrugs => null,
            self::TextBullyingAbuse => null,
            self::TextMurderSuicideSelfHarm => null,
            self::TextIslam => null,
            self::TextChristianity => null,
            self::TextBuddhismShinto => null,
            self::TextNazism => null,
            self::TextPolitics => null,
            self::TextWar => null,
            self::TextCountryName => null,
            self::TextDisability => null,
            self::TextClass => null,
            self::TextElderlyChild => null,
            self::TextGenderEquality => null,
            self::TextCurrentAffairs => null, 
            default => 2
        };
    }

    /**
     * レベルのスライダーのプロパティを取得
     *
     * @param  string  $value
     *
     * @return string
     */
    public static function getStepProps($value): array
    {
        $hiddenProps = [
            'hidden' => true,   // レベルのスライダーの表示/非表示 レベルの設定の無い検知項目は非表示
            'min' => 0,         // レベルのスライダーの最小値
            'max' => 0,         // レベルのスライダーの最大値
            'stepSize' => 0,    // レベルのスライダーのステップサイズ
            'steps' => []       // レベルのスライダーの各ステップの名称と説明文(レベルの設定の設定のある場合の検知項目の文章)
        ];
        // $stepProps = [
        //     'hidden' => false,
        //     'min' => 1,
        //     'max' => 3,
        //     'stepSize' => 1,
        //     'steps' => [
        //         ['stepName' => '弱', 'stepDescription' => '弱の設定です'],
        //         ['stepName' => '中', 'stepDescription' => '中の設定です'],
        //         ['stepName' => '強', 'stepDescription' => '強の設定です'],
        //     ]
        // ];
        return match ($value) {
            self::ImageExposedSkin => [
                'hidden' => false,
                'min' => 1,
                'max' => 3,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '男性：尻、性器のいずれかが完全に露出しているまたはそのように見られる状態。女性：胸、尻、乳首、性器のいずれかが完全に露出しているまたはそのように思わせる状態'],
                    ['stepName' => '2', 'stepDescription' => '下着が露出している。臀裂（でんれつ）が露出している'],
                    ['stepName' => '3', 'stepDescription' => '女性：胸は表面積の50%以上露出している'],
                ]
            ],
            self::ImageSeductivePose => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '直接的な性行為に関する動作。フレンチキス'],
                    ['stepName' => '2', 'stepDescription' => '間接的に性行為を想起させる。性的接触（ボディタッチや抱擁）がある。チラリズムな仕草。鉄壁の左足'],
                ]
            ],
            // self::ImageDangerousPlayInSports => $stepProps,
            // self::ImageBlow => [
            //     'hidden' => false,
            //     'min' => 1,
            //     'max' => 2,
            //     'stepSize' => 1,
            //     'steps' => [
            //         ['stepName' => '1', 'stepDescription' => '明確な打撃行為で、痛みを伴う表現。流血や深刻なダメージを与えている殴打。顔や急所への攻撃。'],
            //         ['stepName' => '2', 'stepDescription' => '明確な打撃行為で、痛みを伴う表現。ただし、流血や深刻なダメージには至らない殴打や顔以外の部位への攻撃や漫画やアニメなどを問わず喜劇的なものや遊びのニュアンスも含む。'],
            //         ]
            //     ],
            // self::ImageCompressionRestraint => $hiddenProps,
            // // self::ImageAttackingActWithAStrikingObject => $stepProps,
            // self::ImageShooting => [
            //     'hidden' => false,
            //     'min' => 1,
            //     'max' => 2,
            //     'stepSize' => 1,
            //     'steps' => [
            //         ['stepName' => '1', 'stepDescription' => '銃器が使用され、実際に撃たれているような深刻なダメージを伴う表現。'],
            //         ['stepName' => '2', 'stepDescription' => '銃器が使用され、威嚇や象徴的な描写が含まれるが、直接的な被害が描かれていない場面を含む。'],
            //     ]
            // ],
            self::ImageBlood => [
                'hidden' => false,
                'min' => 1,
                'max' => 3,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '動脈を切るなどの多量の出血が認められる。血液が鮮血色である'],
                    ['stepName' => '2', 'stepDescription' => '暴力等による流血表現がある'],
                    ['stepName' => '3', 'stepDescription' => '暴力などは伴っていないが、血液による表現がある（ホラーの血文字など）'],
                ]
            ],
            self::ImageDangerousObject => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '鋭利物の先端・銃の銃口を画像の閲覧者に向いている'],
                    ['stepName' => '2', 'stepDescription' => '鋭利物の先端・銃の銃口を画像の閲覧者に向いている（仮）'],
                ]
            ],
            // self::ImageCrime => $hiddenProps,
            // // self::ImageSwastika => $stepProps,
            // self::ImageSS => $hiddenProps,
            // self::ImageReichsadler => $stepProps,
            self::ImageHexagram => $hiddenProps,
            self::ImageCrescentMoonAndStars => $hiddenProps,
            self::ImageCross => $hiddenProps,
            // self::ImageRisingSunFlagChrysanthemumEmblem => $hiddenProps,
            self::ImageRisingSunFlag => $hiddenProps,
            // self::ImageSickleAndHammerRedFlag => $stepProps,
            self::ImageMilitaryUniform => $hiddenProps,
            // self::ImageSkull => $stepProps,
            self::ImageGenitals => $hiddenProps,
            self::ImageChildPornography => $hiddenProps,
            self::ImageSexualEntertainment => $hiddenProps,
            self::ImageViolence => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '人型に対するケガを伴う暴力表現'],
                    ['stepName' => '2', 'stepDescription' => '人型に対する暴力表現'],
                ]
            ],
            self::ImageLgbtq => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => 'LGBTQ+関連シンボルが映っている'],
                    ['stepName' => '2', 'stepDescription' => 'LGBTQ+関連シンボルが映っている（仮）'],
                ]
            ],
            self::ImageSkinColor => $hiddenProps,
            self::ImageEthnicity => $hiddenProps,
            self::ImageAlcoholSmoking => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '見た目年齢が20歳未満のキャラクターが飲酒、喫煙している'],
                    ['stepName' => '2', 'stepDescription' => '酒やたばこが描写されている'],
                ]
            ],
            self::ImageGambling => $hiddenProps,
            self::ImageBanknote => $hiddenProps,
            self::ImageDrug => $hiddenProps,
            self::ImageBullyingAbuse => $hiddenProps,
            self::ImageExcretionVomit => $hiddenProps,
            self::ImageHandSign => $hiddenProps,
            self::ImageMurderSuicideSelfHarm => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '人命を奪う行為が描写されている。自らの身体を傷つける行為が描写されている'],
                    ['stepName' => '2', 'stepDescription' => '動物の命を奪う行為が描写されている'],
                ]
            ],
            self::ImageIslamExposedSkin => $hiddenProps,
            self::ImageHalalFood => $hiddenProps,
            self::ImageChristianityIdol => $hiddenProps,
            self::ImageShintoShrineArchway => $hiddenProps,
            self::ImageBuddhistStatue => $hiddenProps,
            self::ImageHistoricalArtwork => $hiddenProps,
            self::ImageReligiousFacilities => $hiddenProps,
            self::ImageNazi => $hiddenProps,
            self::ImageChrysanthemumEmblem => $hiddenProps,
            self::ImageRedCross => $hiddenProps,
            self::ImagePoliticalParty => $hiddenProps,
            self::ImageElection => $hiddenProps,
            self::ImageWeapon => $hiddenProps,
            self::ImageNationalFlag => $hiddenProps,
            self::ImageCurrentAffairs => $hiddenProps,
            self::TextTypo => $hiddenProps,
            self::TextGrammar => $hiddenProps,
            self::TextNonJoyoKanji => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '常用漢字、表外漢字字体表にない漢字が記述されている'],
                    ['stepName' => '2', 'stepDescription' => '表外漢字字体表にある漢字が記述されている'],
                ]
            ],           
            self::TextProhibitedWords => [
                'hidden' => false,
                'min' => 1,
                'max' => 3,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '禁止用語辞書レベル３に当たる用語が記述されている（絶対禁止）'],
                    ['stepName' => '2', 'stepDescription' => '禁止用語辞書レベル２に当たる用語が記述されている（製品サービスの性質によっては取捨選択可能）'],
                    ['stepName' => '3', 'stepDescription' => '禁止用語辞書レベル１に当たる用語が記述されている（対象年齢や文脈によっては望ましくない）'],
                ]
            ],
            self::TextTrademarkedProperNoun => $hiddenProps,
            self::TextDayOfWeek => $hiddenProps,
            self::TextDate => $hiddenProps,
            self::TextSexualExpression => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => '直接的に性行為を想起させる記述がある。性風俗、性犯罪に関する用語が記述されている'],
                    ['stepName' => '2', 'stepDescription' => '間接的に性行為を想起させる記述がある'],
                ]
            ],
            self::TextLGBTQ => [
                'hidden' => false,
                'min' => 1,
                'max' => 2,
                'stepSize' => 1,
                'steps' => [
                    ['stepName' => '1', 'stepDescription' => 'LGBTQ＋の蔑称が記述されている'],
                    ['stepName' => '2', 'stepDescription' => 'LGBTQ＋に関する用語が記述されている'],
                ]
            ],
            self::TextRace => $hiddenProps,
            self::TextEthnicity => $hiddenProps,
            self::TextAppearance => $hiddenProps,
            self::TextGambling => $hiddenProps,
            self::TextDrugs => $hiddenProps,
            self::TextBullyingAbuse => $hiddenProps,
            self::TextMurderSuicideSelfHarm => $hiddenProps,
            self::TextIslam => $hiddenProps,
            self::TextChristianity => $hiddenProps,
            self::TextBuddhismShinto => $hiddenProps,
            self::TextNazism => $hiddenProps,
            self::TextPolitics => $hiddenProps,
            self::TextWar => $hiddenProps,
            self::TextCountryName => $hiddenProps,
            self::TextDisability => $hiddenProps,
            self::TextClass => $hiddenProps,
            self::TextElderlyChild => $hiddenProps,
            self::TextGenderEquality => $hiddenProps,
            self::TextCurrentAffairs => $hiddenProps, 
            default => []
        };
    }

    public static function getBaseAiProperties(): array
    {
        $enumValues = self::getValues();
        $result = [];
        foreach ($enumValues as $value) {
            $result[$value] = [
                'active' => self::getDefalutActive($value),
                'level' => self::getDefalutLevel($value),
            ];
        }
        return $result;
    }

    private static function getCartridgeData($key): array
    {
        if (static::$cartridgeData == null) {
            $jsonPath = storage_path('app/cartridge.json');
            static::$cartridgeData = json_decode(file_get_contents($jsonPath), true);
        }
        
        if (!array_key_exists($key, static::$cartridgeData)) {
            throw new \Exception('CartridgeElementEnum::getCartridgeData() key not found: ' . $key);
        }
        else {
            return static::$cartridgeData[$key];
        }
    }

    private static function getValueFromCartridgeData($key1, $key2)
    {
        $cartridgeData = self::getCartridgeData($key1);
        $module = $cartridgeData[$key2];
        return $module;
    }

    public static function getFormProps(): array
    {
        $enumValues = self::getValues();
        $result = [];
        foreach ($enumValues as $value) {
            $result[$value] = [
                'displayName' => self::getDisplayName($value),
                'description' => self::getDescription($value),
                'stepProps' => self::getStepProps($value),
                'module' => self::getValueFromCartridgeData($value, "module"),
                'cateHigh' => self::getValueFromCartridgeData($value, "cate_high"),
                'cateMid' => self::getValueFromCartridgeData($value, "cate_mid"),
                'cateLow' => self::getValueFromCartridgeData($value, "cate_low"),
                'detail' => self::getValueFromCartridgeData($value, "detail"),
                'example' => self::getValueFromCartridgeData($value, "example"),
            ];
        }
        return $result;
    }

    public function toArray()
    {
        return $this;
    }


}
