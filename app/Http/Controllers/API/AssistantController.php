<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Services\ChatGPTAssistantService;
use App\Services\UserRoleMappingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class AssistantController extends Controller
{
    private ChatGPTAssistantService $assistantService;

    public function __construct(ChatGPTAssistantService $assistantService)
    {
        $this->assistantService = $assistantService;
    }

    /**
     * Process user query with ChatGPT assistant
     */
    public function query(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|max:1000',
                'current_page' => 'nullable|string|max:50',
                'context' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid request parameters',
                    'details' => $validator->errors()
                ], 400);
            }

            $query = $request->input('query');
            $currentPage = $request->input('current_page', 'ホーム');
            $context = $request->input('context', []);

            // Add user context with role mapping
            $userContext = UserRoleMappingService::getUserContextForAssistant();
            $context = array_merge($context, $userContext);
            $context['timestamp'] = now()->toISOString();
            $context['user_agent'] = $request->userAgent();

            // Process query with ChatGPT
            $result = $this->assistantService->processQuery($query, $currentPage, $context);

            // Log the interaction for monitoring
            Log::info('Assistant Query Processed', [
                'user_id' => auth()->id(),
                'query' => $query,
                'current_page' => $currentPage,
                'success' => $result['success'],
                'function_calls' => count($result['function_calls'] ?? [])
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Assistant Controller Error: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'query_length' => strlen($request->input('query', '')),
                'current_page' => $request->input('current_page'),
                'exception_class' => get_class($e)
            ]);

            $errorResponse = \App\Services\ErrorSanitizer::sanitizeForApi($e);
            $errorResponse['response'] = 'すみません、一時的にサービスが利用できません。しばらくしてからもう一度お試しください。';

            return response()->json($errorResponse, 500);
        }
    }

    /**
     * Get assistant capabilities and available functions
     */
    public function capabilities(): JsonResponse
    {
        try {
            $capabilities = [
                'functions' => [
                    'navigation' => [
                        'name' => 'ページ移動',
                        'description' => '指定されたページに移動する',
                        'available_pages' => ['ホーム', '倫理確認システム', 'カードチェック', '申請フォーム', '申請履歴']
                    ],
                    'application_history' => [
                        'name' => '申請履歴取得',
                        'description' => 'ユーザーの申請履歴を取得する',
                        'filters' => ['all', '承認待ち', '承認済み', '差し戻し']
                    ],
                    'file_guidelines' => [
                        'name' => 'ファイルガイドライン',
                        'description' => 'ファイルアップロードのガイドラインを提供する',
                        'file_types' => ['PDF', '動画', '元素材']
                    ],
                    'ethics_guidelines' => [
                        'name' => '倫理ガイドライン',
                        'description' => '倫理確認の基準とガイドラインを提供する',
                        'categories' => ['general', '文書', '製品', '広告']
                    ],
                    'ethics_manual' => [
                        'name' => '倫理確認システムマニュアル',
                        'description' => 'AIC倫理確認システムの詳細マニュアル（メンバー向け）',
                        'sections' => [
                            'overview' => 'システム概要',
                            'getting_started' => '利用開始ガイド',
                            'application_process' => '申請プロセス',
                            'document_guidelines' => '文書ガイドライン',
                            'evaluation_criteria' => '評価基準',
                            'common_issues' => 'よくある問題',
                            'troubleshooting' => 'トラブルシューティング',
                            'faq' => 'よくある質問',
                            'contact' => 'お問い合わせ'
                        ],
                        'user_roles' => ['member', 'supporter', 'admin']
                    ],
                    'card_check_instructions' => [
                        'name' => 'カードチェック手順',
                        'description' => 'カードチェックの手順と要件を提供する',
                        'check_types' => ['general', '仕様確認', 'エラー修正', '結果確認']
                    ]
                ],
                'supported_languages' => ['日本語', 'English'],
                'response_format' => 'markdown',
                'max_query_length' => 1000,
                'session_timeout' => 3600
            ];

            return response()->json([
                'success' => true,
                'capabilities' => $capabilities
            ]);

        } catch (\Exception $e) {
            Log::error('Assistant Capabilities Error: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'exception_class' => get_class($e)
            ]);

            $errorResponse = \App\Services\ErrorSanitizer::sanitizeForApi($e);
            return response()->json($errorResponse, 500);
        }
    }

    /**
     * Get system status for assistant
     */
    public function status(): JsonResponse
    {
        try {
            $status = [
                'assistant_service' => 'operational',
                'chatgpt_api' => $this->checkChatGPTStatus(),
                'database' => $this->checkDatabaseStatus(),
                'file_storage' => $this->checkFileStorageStatus(),
                'last_updated' => now()->toISOString()
            ];

            $overallStatus = collect($status)->except('last_updated')->every(function ($value) {
                return $value === 'operational';
            }) ? 'operational' : 'degraded';

            return response()->json([
                'success' => true,
                'overall_status' => $overallStatus,
                'services' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('Assistant Status Check Error: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'exception_class' => get_class($e)
            ]);

            $errorResponse = \App\Services\ErrorSanitizer::sanitizeForApi($e);
            $errorResponse['overall_status'] = 'error';
            return response()->json($errorResponse, 500);
        }
    }

    /**
     * Execute a specific function call (for testing purposes only)
     * This endpoint is restricted to development/testing environments
     */
    public function executeFunction(Request $request): JsonResponse
    {
        // Only allow in development/testing environments
        if (!app()->environment(['local', 'testing'])) {
            return response()->json([
                'success' => false,
                'error' => 'Function execution endpoint is not available in production'
            ], 403);
        }

        try {
            $validator = Validator::make($request->all(), [
                'function_name' => 'required|string|regex:/^[a-zA-Z][a-zA-Z0-9]*$/',
                'arguments' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid request parameters',
                    'details' => $validator->errors()
                ], 400);
            }

            $functionName = $request->input('function_name');
            $arguments = $request->input('arguments', []);

            // Whitelist of allowed functions for direct execution
            $allowedFunctions = [
                'getCurrentPageInfo',
                'getSystemStatus',
                'getFileUploadGuidelines',
                'checkEthicsGuidelines',
                'getCardCheckInstructions'
            ];

            if (!in_array($functionName, $allowedFunctions)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Function not allowed for direct execution'
                ], 403);
            }

            // Sanitize arguments
            $arguments = \App\Services\InputSanitizer::sanitizeContext($arguments);

            // Create handler class name
            $handlerClass = "App\\Services\\FunctionHandlers\\" . ucfirst($functionName) . "Handler";

            if (!class_exists($handlerClass)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Function handler not found'
                ], 404);
            }

            $handler = new $handlerClass();
            $result = $handler->execute($arguments);

            // Log the direct function execution for security monitoring
            Log::info('Direct function execution', [
                'user_id' => auth()->id(),
                'function_name' => $functionName,
                'arguments' => $arguments,
                'environment' => app()->environment()
            ]);

            return response()->json([
                'success' => true,
                'function_name' => $functionName,
                'result' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Function Execution Error: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'function_name' => $request->input('function_name'),
                'arguments_keys' => array_keys($request->input('arguments', [])),
                'exception_class' => get_class($e)
            ]);

            $errorResponse = \App\Services\ErrorSanitizer::sanitizeForApi($e);
            return response()->json($errorResponse, 500);
        }
    }

    /**
     * Check ChatGPT API status
     */
    private function checkChatGPTStatus(): string
    {
        try {
            // Simple test to check if API key is configured
            $apiKey = config('services.openai.api_key');
            return !empty($apiKey) ? 'operational' : 'misconfigured';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Check database status
     */
    private function checkDatabaseStatus(): string
    {
        try {
            \DB::connection()->getPdo();
            return 'operational';
        } catch (\Exception $e) {
            return 'error';
        }
    }

    /**
     * Check file storage status
     */
    private function checkFileStorageStatus(): string
    {
        try {
            \Storage::disk('public')->exists('test');
            return 'operational';
        } catch (\Exception $e) {
            return 'error';
        }
    }

}