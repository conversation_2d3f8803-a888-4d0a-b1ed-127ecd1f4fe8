<?php

namespace App\Console\Commands;

use App\Helpers\ProofCloudSearchManager;
use App\Models\ProofCloudSearch;
use Illuminate\Console\Command;
use League\Flysystem\AwsS3v3\AwsS3Adapter;
use League\Flysystem\Filesystem;
use Aws\S3\S3Client;
use Illuminate\Support\Collection;

class ProofCloudSearchUploads extends Command
{
    private $found = [];

    private $isContinue = false;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cloudsearch:uploads 
                            {paths* : PDFファイルのS3バケット名とS3キー名} 
                            {--ignores=* : エラーが発生した場合、無視する。}
                            {--directory : ディレクトリーベースでアップロード「example 2)」参考} }
                            ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = <<<DESC
    AWS CloudSearchの検索するドキュメント(PDF)を追加する。
    追加する予定のPDFファイルは既にS3にアップロードされる必要があります。
    
    pathsパラメータは@(アットマーク)でBucket(バケット)名とKey(キー)名を区切ります。
    また複数対応の為各pathsはスペース区切ります。

    ※もしディレクトリーベースでアップロードを行う際には「example 2」を参考してください。

    bucket: https://docs.aws.amazon.com/AmazonS3/latest/userguide/UsingBucket.html
    key: https://docs.aws.amazon.com/AmazonS3/latest/userguide/UsingObjects.html

    example 1)
    ファイルベースでアップロード

    php artisan cloudsearch:uploads \
    "tf-bapi@proof/03a4273f-0396-4255-a8a2-da21163a579e/マージ済/テスト.pdf" \
    "tf-bapi@proof/0b8dbe5f-eb90-43b1-95fc-51d3ab1a8f83/マージ済/テスト2.pdf" \
    "bnam-bapi@【倫理】回答アーカイブ/(0718修正）17-C-000『VS PARK』大阪府松原市_archive.pdf"

    example 2)
    ディレクトリーベースでアップロード

    php artisan cloudsearch:uploads "bnam-bapi@【倫理】回答アーカイブ" --directory
    DESC;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->init();

        if (!$this->isContinue) {
            return 0;
        }

        $this->info('AWS CloudSearch Document Upload Start...');

        
        collect($this->found)->each(function ($path, $index) {
            $this->current = $path;

            $model = ProofCloudSearch::ofPath($path)->first();

            try {
                if (is_null($model)) {
                    $result = ProofCloudSearchManager::uploadDocument($path);
                    $model = $result['model'];
                }

                $this->progressMessage($model, $index + 1, count($this->found));
            } catch (\Throwable $th) {
                $this->error($this->current);
                $this->error($th->getmessage());

                return true;
            }
        });

        $this->newLine();
        $this->info('AWS CloudSearch Document Upload Finish.');
            
        return 0;
    }

    private function init()
    {
        // arguments
        $paths = $this->argument("paths");
    
        // options
        $isDirectory = $this->option("directory");
        $ignores = $this->option("ignores");
        
        $found = $this->getContentPaths($paths, $isDirectory);
        $found = $found->diff($ignores); // filter ignores
        
        $this->info('AWS CloudSearch Document Uploading Ready');
        $this->line(
            sprintf(
                "Found <fg=green>%s items</> out of %s in S3",
                count($found),
                count($paths),
            )
        );
        
        if (count($found) !== 0) {
            $this->isContinue = $this->confirm("Do you want to continue?");
            $this->found = $found;
        }
    }

    private function getContent(Filesystem $filesystem, $bucket, $key)
    {
        if ($filesystem->has($key)) {
            return [sprintf("%s@%s", $bucket, $key)];
        }

        return [];
    }

    private function getContentsFromDirectory(Filesystem $filesystem, $bucket, $key, $recursive)
    {
        $isPdfFileFunc = function ($content) {
            return $content['type'] == 'file' && $content['extension'] == 'pdf';
        };

        return collect($filesystem->listContents($key, $recursive))
                ->filter($isPdfFileFunc)
                ->map(fn ($content) => sprintf("%s@%s", $bucket, $content['path']));
    }
    
    // directoryオプションが存在する場合、S3ディレクトリーからPDFファイルのみ取得
    private function getContentPaths(array|Collection $paths, $isDirectory = false, $recursive = false)
    {
        return collect($paths)
                ->map(fn ($path) => ProofCloudSearchManager::splitPath($path))
                ->flatMap(function (array $info) use ($isDirectory, $recursive) {
                    $bucket = $info['bucket'];
                    $key = $info['key'];

                    $filesystem = $this->initFileSystem($bucket);
                    
                    $contents = $isDirectory ?
                        $this->getContentsFromDirectory($filesystem, $bucket, $key, $recursive):
                        $this->getContent($filesystem, $bucket, $key);

                    return $contents;
                });
    }

    private function initFileSystem(string $bucket): Filesystem
    {
        $client = new S3Client([
            'credentials' => [
                'key'    => env('AWS_ACCESS_KEY_ID'),
                'secret' => env('AWS_SECRET_ACCESS_KEY'),
            ],
            'region' => env('AWS_DEFAULT_REGION'),
            'version' => 'latest',
        ]);

        $adapter = new AwsS3Adapter($client, $bucket);

        return new Filesystem($adapter);
    }

    private function progressMessage(ProofCloudSearch $model, int $currentCount, int $totalCount)
    {
        $info = $model->wasRecentlyCreated ?
        [
            'color' => 'green',
            'status' => 'Success'
        ] :
        [
            'color' => 'yellow',
            'status' => 'Already'
        ];
        
        $message = sprintf(
            "(%s/%s) - <fg=%s>%s</> (<fg=green>%s</>) %s - %s%%",
            $currentCount,
            $totalCount,
            $info['color'],
            $info['status'],
            $model->id,
            $model->path,
            round(($currentCount * 100) / $totalCount, 2),
        );
        
        $this->line($message);
    }
}
