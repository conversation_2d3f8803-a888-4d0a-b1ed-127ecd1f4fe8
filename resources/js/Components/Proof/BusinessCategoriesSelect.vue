<template>
  <!-- 業務分類 -->
  <v-autocomplete
    class="v-chips"
    :value="_value"
    :items="items"
    :label="label"
    outlined
    :item-value="itemValue"
    :item-text="itemText"
    deletable-chips
    small-chips
    multiple
    :disabled="disabled"
    :error-messages="errors"
    @change="updateValue"
  >
    <template #item="data">
      <v-tooltip top max-width="500" :disabled="hasNotDescription(data.item)">
        <template #activator="{ on, attrs }">
          <v-layout wrap v-on="on" v-bind="attrs">
            <v-list-item-action>
              <v-checkbox v-model="data.attrs.inputValue" />
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>{{ data.item.name }}</v-list-item-title>
            </v-list-item-content>
          </v-layout>
        </template>
        <span v-html="data.item.description" />
      </v-tooltip>
    </template>
  </v-autocomplete>
</template>

<script>
export default {
  props: {
    value: {
      type: Array,
      required: true,
      default: () => [],
    },
    label: {
      type: String,
      required: false,
      default: '業務分類',
    },
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    errors: {
      type: Array,
      required: false,
      default: () => [],
    },
    itemValue: {
      type: String,
      required: false,
      default: 'id',
    },
    itemText: {
      type: String,
      required: false,
      default: 'name',
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  computed: {
    _value: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('update:value', value);
      },
    },
  },
  methods: {
    hasNotDescription(item) {
      return !item?.description;
    },
    updateValue(value) {
      this._value = value;
    },
  },
};
</script>

<style lang="scss" scoped></style>
