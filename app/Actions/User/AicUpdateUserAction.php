<?php

namespace App\Actions\User;

use App\Data\UpdateUserData;
use App\Mail\ProofAssignmentedToRequiredUserMail;
use App\Models\Proof;
use App\Models\ProofCompletionMark;
use App\Models\User;
use App\Models\UserType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class AicUpdateUserAction
{
    public function execute(User $user, UpdateUserData $data): User
    {
        Log::info('UpdateUserAction');
        Log::info('user', [$user]);
        Log::info('data', [$data]);
        $userType = UserType::where('value', $data->type_value)->first();
        $user->user_type_id = $userType->id;
        $user->update(
            $data->except(
                'business_categories',
                'ips',
                'category_id'
            )->toArray()
        );

        Log::info('UpdateUserAction');
        Log::info("this->isAllowedUserType",[$this->isAllowedUserType($user)]);
        if ($this->isAllowedUserType($user)) {
            $this->syncCategories($user, $data);
        }

        return $user->refresh();
    }

    private function syncCategories(User $user, UpdateUserData $data)
    {
        if ($this->isAicNotAssignable($data->categories, $data->ips, $user)) {
            Log::info('UpdateUserAction');
            Log::info("isNotAssignable");
            return;
        }

        $this->assignmentMemberToProofs($data->categories, $data->ips, $user);

        $this->syncCategory($user, $data->categories);
        $this->syncIntellectualProperties($user, $data->ips);
    }

    /**
     * 更新する前に
     * @param array $categoryIds
     * @param array $ips
     * @param User $user
     * 
     * @return void
     */
    private function assignmentMemberToProofs(array $categoryIds, array $ips, User $user)
    {
        Log::info('UpdateUserAction');
        Log::info("category",$categoryIds);
        Log::info("ips",$ips);
        /**
         * 「進行中」ステータスのProofと
         * ユーザーが所属するIPsまたはカテゴリを持つProofを取得
         */
        $proofs = Proof::query()
            ->whereProgressing()
            ->where(function (Builder $query) use ($ips, $categoryIds) {
                $query
                    ->orWhereHasIps($ips)
                    ->orWhereHasCategories($categoryIds);
            })
            ->get();

        $syncResult = $user->members()->sync($proofs);

        Log::info($syncResult);

        $isNewAssignedProof = fn(Proof $proof) => in_array($proof->id, $syncResult['attached']);
        Log::info("isNewAssignedProof");
        Log::info("user",[$user]);
        if($user->type->id == User::USER_TYPE_MEMBER){
            Log::info("sendMail");
            Log::info($proofs->filter($isNewAssignedProof));
            $sendMail = fn(Proof $proof) => Mail::to($user)->send(new ProofAssignmentedToRequiredUserMail($proof, $user));
            Log::info("sendMail : ",[$proofs->filter($isNewAssignedProof)->each($sendMail)]);
            $proofs
            ->filter($isNewAssignedProof)
            ->each($sendMail);
            Log::info("proofs",[$proofs]);
        }
    }

    private function syncCategory(User $user, ?array $categoryIds)
    {
        $user->categories()->sync($categoryIds);
    }

    private function syncIntellectualProperties(User $user, ?array $ips)
    {
        return $user->ips()->sync($ips);
    }

    private function isAllowedUserType(User $user): bool
    {
        return $user->user_type_id == User::USER_TYPE_MEMBER || $user->user_type_id == User::USER_TYPE_ADMIN;
    }

    private function isNotAssignable(array $categoryIds, array $ips, User $user): bool
    {
        if ($user->user_type_id != User::USER_TYPE_MEMBER) {
            Log::info('UpdateUserAction');
            Log::info("user type is not member");
            return true;
        }

        $isNotDifferent = value(function (array $categoryIds, array $ips, User $user) {
            $currentCategoryIds = $user->categories()->pluck('id')->toArray();
            $currentIps = $user->ips()->pluck('id')->toArray();

            return
                is_same_array($currentCategoryIds, $categoryIds) &&
                is_same_array($currentIps, $ips);
        }, $categoryIds, $ips, $user);

        if ($isNotDifferent) {
            return true;
        }

        return false;
    }

    private function isAicNotAssignable(array $categoryIds, array $ips, User $user): bool
    {
        if ($user->user_type_id != User::USER_TYPE_MEMBER && $user->user_type_id != User::USER_TYPE_ADMIN) {
            Log::info('UpdateUserAction');
            Log::info("user type is not member");
            return true;
        }

        $isNotDifferent = value(function (array $categoryIds, array $ips, User $user) {
            $currentCategoryIds = $user->categories()->pluck('id')->toArray();
            $currentIps = $user->ips()->pluck('id')->toArray();

            return
                is_same_array($currentCategoryIds, $categoryIds) &&
                is_same_array($currentIps, $ips);
        }, $categoryIds, $ips, $user);

        if ($isNotDifferent) {
            return true;
        }

        return false;
    }
}
