<?php

namespace App\Data;

use <PERSON><PERSON>\LaravelData\Data;

class UpsertIntellectualPropertyData extends Data
{
    public function __construct(
        public ?int $id,
        public string $name,
        public ?int $parent_id
    ) {
    }

    public static function rules(): array
    {
        return [
            'id' => [
                'nullable',
                'sometimes',
                'exists:intellectual_properties,id'
            ],
            'name' => [
                'required',
                'string',
            ],
            'parent_id' => [
                'nullable',
                'sometimes',
                'exists:intellectual_properties,id'
            ],
        ];
    }

    public static function attributes(): array
    {
        return [
            'name' => '名前',
        ];
    }
}
