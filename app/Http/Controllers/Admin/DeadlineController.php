<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ProofDeadLine;
use Carbon\Carbon;
use Inertia\Inertia;

class DeadlineController extends Controller
{
    public function index(){

        //締切日はまだ定義されてない場合、['no_deadline']の配列を送信します
        $deadlines = ProofDeadLine::first() ??  ['no_deadline'];

        return Inertia::render('Admin/Deadline/AicIndex', [
            'deadlines' => $deadlines,
        ]);
    }

    public function update(){
        $rowDeadlines = ProofDeadLine::first();

        $deadlines = request()->all();
 
        $internalFrom = Carbon::createFromFormat('H:i:s', $deadlines['internal_deadline_from']);
        $internalTo = Carbon::createFromFormat('H:i:s', $deadlines['internal_deadline_to']);
        $externalFrom = Carbon::createFromFormat('H:i:s', $deadlines['external_deadline_from']);
        $externalTo = Carbon::createFromFormat('H:i:s', $deadlines['external_deadline_to']);

        //レコードがない場合DBに追加
        if($rowDeadlines == null){
            ProofDeadLine::create([       
                'internal_deadline_from'=> $internalFrom,
                'internal_deadline_to'=>  $internalTo,
                'external_deadline_from'=> $externalFrom,
                'external_deadline_to'=> $externalTo
            ]);
        }else{
            //存在があれば修正
            $rowDeadlines->internal_deadline_from = $internalFrom;
            $rowDeadlines->internal_deadline_to = $internalTo;
            $rowDeadlines->external_deadline_from = $externalFrom;
            $rowDeadlines->external_deadline_to = $externalTo;

            $rowDeadlines->save();
        }  
        
        return redirect()->route('admin.deadline.index', ['deadlines'=> $rowDeadlines], 
        self::HTTP_STATUS_SEE_OTHER)
                            ->with('snackbar', [
                            'color' => 'success',
                            'icon' => 'mdi-check-circle',
                            'title' => '成功',
                            'message' =>'締切は更新しました。'
                        ]);     
    }
    
    public function aicIndex(){

        //締切日はまだ定義されてない場合、['no_deadline']の配列を送信します
        $deadlines = ProofDeadLine::first() ??  ['no_deadline'];

        return Inertia::render('Admin/Deadline/NewIndex', [
            'deadlines' => $deadlines,
        ]);
    }
}
