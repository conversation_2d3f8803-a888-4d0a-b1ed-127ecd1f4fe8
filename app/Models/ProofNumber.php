<?php

namespace App\Models;

use App\EloquentBuilder\ProofNumberBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ProofNumber
 * @property int $id
 * @property string $company_domain
 * @property string $company_code
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ProofNumber|ProofNumberBuilder newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProofNumber|ProofNumberBuilder newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProofNumber|ProofNumberBuilder query()
    * @method static \Illuminate\Database\Eloquent\Builder|ProofNumber|ProofNumberBuilder getProofNumber(string $company_domain)
 */
class ProofNumber extends Model
{
    use HasFactory;

    public static function getProofNumberFromCompanyCode(string $companyCode): ?ProofNumber
    {
        $row = self::where('company_code', $companyCode)->first();
        // $row->proof_number BNXP-0001形式なので、0001を取得し数値に変換して+1する
        if ($row) {
            $proofNumber = (int) substr($row->proof_number, strrpos($row->proof_number, '-') + 1);
            $row->proof_number = $proofNumber + 1;
        }
        return $row;
    }

    public static function updateProofNumber(string $companyCode, int $proofNumber): void
    {
        self::where('company_code', $companyCode)->update(['proof_number' => $proofNumber]);
    }

    public function newEloquentBuilder($query): Builder
    {
        return new ProofNumberBuilder($query);
    }
}
