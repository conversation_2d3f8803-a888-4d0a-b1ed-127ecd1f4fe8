<?php

namespace App\Actions\Proof;

use App\Enums\ProofNotificationTypeEnum;
use App\Mail\ProofUpdateExternalDeadlineAtToNotRequiredUserMail;
use App\Mail\ProofUpdateExternalDeadlineAtToRequesterMail;
use App\Mail\ProofUpdateExternalDeadlineAtToRequiredUserMail;
use App\Models\Proof;
use App\Models\User;
use App\Notifications\ProofNotification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class ChangeProofExternalDeadlineAt
{
    public function execute(Proof $proof, Carbon $afterExternalDeadlineAt): void
    {
        if ($this->isNotChanable($proof, $afterExternalDeadlineAt)) {
            return;
        }

        $this->getRequiredUsersInProof($proof)->each(
            fn(User $user) => Mail::to($user)->send(new ProofUpdateExternalDeadlineAtToRequiredUserMail(
                proof: $proof,
                receiver: $user,
                before: $proof->external_deadline_at?->format('Y年n月j日 H時i分') ?? '',
                after: $afterExternalDeadlineAt->format('Y年n月j日 H時i分'),
            ))
        );

        Mail::to($this->getNotRequiredUsersInProof($proof))->send(new ProofUpdateExternalDeadlineAtToNotRequiredUserMail(
            proof: $proof,
            before: $proof->external_deadline_at?->format('Y年n月j日 H時i分') ?? '',
            after: $afterExternalDeadlineAt->format('Y年n月j日 H時i分'),
        ));

        Mail::to($proof->user)->send(new ProofUpdateExternalDeadlineAtToRequesterMail(
            proof: $proof,
            before: $proof->external_deadline_at?->format('Y年n月j日 H時i分') ?? '',
            after: $afterExternalDeadlineAt->format('Y年n月j日 H時i分'),
        ));

        Notification::send(
            [$proof->user],
            new ProofNotification($proof, ProofNotificationTypeEnum::Update)
        );

        $proof->external_deadline_at = $afterExternalDeadlineAt;
        $proof->save();
    }

    private function getRequiredUsersInProof(Proof $proof)
    {
        return User::query()->requiredInProof($proof)->get();
    }

    private function getNotRequiredUsersInProof(Proof $proof)
    {
        return User::query()->notRequiredInProof($proof)->get();
    }

    /**
     * $afterExternalDeadlineAtと$proof->external_deadline_atが同じ場合、変更不可
     *
     * @param Proof $proof
     * @param Carbon $afterExternalDeadlineAt
     *
     * @return bool
     */
    private function isNotChanable(Proof $proof, Carbon $afterExternalDeadlineAt): bool
    {
        if (is_null($proof->external_deadline_at)) {
            return false;
        }

        return $proof->external_deadline_at->eq($afterExternalDeadlineAt);
    }
}
