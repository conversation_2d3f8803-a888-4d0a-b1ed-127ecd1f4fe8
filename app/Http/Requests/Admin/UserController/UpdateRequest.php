<?php

namespace App\Http\Requests\Admin\UserController;

use App\Data\UpdateUserData;
use Carbon\CarbonImmutable;
use Illuminate\Foundation\Http\FormRequest;

class UpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'last_name' => [
                "required",
                "string",
                "max:50",
            ],
            'first_name' => [
                "required",
                "string",
                "max:50",
            ],
            'department' => [
                "nullable",
                "string",
            ],
            'email_verified_at' => [
                "nullable",
            ],
            'deleted_at' => [
                "nullable",
            ],
            'ips' => [
                "nullable",
                "array",
            ],
            'ips.*' => [
                'integer',
                'exists:intellectual_properties,id'
            ],
            'categories' => [
                "nullable",
                "array",
            ],
            'categories.*' => [
                'integer',
                'exists:categories,id'
            ],
        ];
    }

    public function data()
    {
        return UpdateUserData::from([
            'last_name' => $this->last_name,
            'first_name' => $this->first_name,
            'department' => $this->department,
            'email_verified_at' => (bool)$this->email_verified_at ? CarbonImmutable::now() : null,
            'deleted_at' => (bool)$this->deleted_at ? CarbonImmutable::now() : null,
            'type_value' => $this->type_value,
            'ips' => $this->ips,
            'categories' => $this->categories,
        ]);
    }

    public function attributes()
    {
        return [
            'categories' => '種別'
        ];
    }
}
