<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    // HTTP Status
    const HTTP_STATUS_SEE_OTHER = 303;  //https://inertiajs.com/redirects

    // Pagination
    const PER_PAGE = 20;

    const CHECK_ITEM = 1000;
    
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;
}
