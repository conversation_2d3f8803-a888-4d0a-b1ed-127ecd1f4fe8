<template>
  <div class="mb-3">
    <v-card outlined>
      <!-- Comment Header -->
      <v-card-title>
        <v-layout align-center justify-space-between>
          <h6 class="font-weight-bold text-subtitle-1">
            <v-icon>mdi-comment-multiple</v-icon>
            コメント({{ comments.length }})
          </h6>
        </v-layout>
      </v-card-title>
      <v-divider></v-divider>

      <!-- Comment List -->
      <v-card-text v-if="comments.length" class="pa-0">
        <v-card
          v-for="comment in comments"
          :key="comment.id"
          class="elevation-0"
          tile
        >
          <v-card-title>
            <v-row>
              <v-col clas1s="text-subtitle-1 font-weight-bold">
                {{ comment.user.name }}
              </v-col>
              <v-col class="text-right">
                <v-tooltip bottom v-if="isYetUpdated(comment)">
                  <template v-slot:activator="{ on, attrs }">
                    <v-icon color="red" v-bind="attrs" v-on="on">
                      mdi-alert-circle
                    </v-icon>
                  </template>
                  <span>更新ボタンを押して、コメントを反映させてください</span>
                </v-tooltip>

                <v-badge
                  :content="comment.replies.length"
                  :value="comment.replies.length"
                  color="primary"
                  overlap
                >
                  <v-btn icon @click="openReplies(comment)">
                    <v-icon color="primary">
                      mdi-message-reply-text
                    </v-icon>
                  </v-btn>
                </v-badge>
              </v-col>
            </v-row>
          </v-card-title>

          <v-card-subtitle class="text-caption">
            {{ comment.created_at }}
          </v-card-subtitle>

          <v-card-text class="text-body-2">
            <pre style="white-space: break-spaces">{{ comment.value }}</pre>
          </v-card-text>
        </v-card>
      </v-card-text>
      <v-divider></v-divider>

      <!-- Comment Input -->
      <v-card-actions class="px-4">
        <v-textarea
          v-model="commentValue"
          name="comment"
          placeholder="コメント入力"
          dense
          outlined
          auto-grow
          single-line
          hide-details
          rows="1"
          row-height="30"
          class="my-2"
        >
          <template v-slot:append>
            <v-icon
              @click="addComment"
              :color="commentValue ? 'green' : 'grey'"
            >
              mdi-arrow-right-box
            </v-icon>
          </template>
        </v-textarea>
      </v-card-actions>
    </v-card>

    <!-- コメント返事 -->
    <CommentReplies
      v-if="commentReply"
      :drawer.sync="drawerReplies"
      :comment.sync="commentReply"
    />
  </div>
</template>

<script>
import CommentReplies from './CommentReplies';
export default {
  name: 'Comments',
  components: {
    CommentReplies,
  },
  props: {
    comments: {
      type: Array,
      required: true,
      default: [],
    },
  },
  data() {
    return {
      drawerReplies: false,
      commentValue: '',
      commentReply: '',
    };
  },
  methods: {
    openReplies(comment) {
      this.commentReply = comment;
      this.drawerReplies = true;
    },
    addComment(event) {
      if (this.commentValue) {
        this.comments.push({
          value: this.commentValue,
          created_at: this.$moment().format('YYYY-MM-DD HH:mm:ss'),
          user: this.$page.props.user,
          replies: [],
        });

        this.commentValue = '';
      }
    },
    isYetUpdated(comment) {
      return !comment.id;
    },
  },
};
</script>
