<?php

namespace App\Services;

class ErrorSanitizer
{
    /**
     * Sanitize error messages to prevent information disclosure
     */
    public static function sanitizeError(\Exception $exception, bool $isProduction = null): string
    {
        if ($isProduction === null) {
            $isProduction = app()->environment('production');
        }

        // In production, return generic error messages
        if ($isProduction) {
            return self::getGenericErrorMessage($exception);
        }

        // In development, return more detailed but still sanitized messages
        return self::getSanitizedErrorMessage($exception);
    }

    /**
     * Get generic error message for production
     */
    private static function getGenericErrorMessage(\Exception $exception): string
    {
        $errorMap = [
            'Illuminate\Database\QueryException' => 'データベースエラーが発生しました。',
            'Illuminate\Http\Client\RequestException' => '外部サービスとの通信エラーが発生しました。',
            'Illuminate\Validation\ValidationException' => '入力データに問題があります。',
            'Symfony\Component\HttpKernel\Exception\NotFoundHttpException' => 'リクエストされたリソースが見つかりません。',
            'Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException' => 'アクセスが拒否されました。',
            'Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException' => '認証が必要です。',
            'InvalidArgumentException' => '無効な引数が指定されました。',
            'RuntimeException' => 'システムエラーが発生しました。',
        ];

        $exceptionClass = get_class($exception);

        return $errorMap[$exceptionClass] ?? 'システムエラーが発生しました。しばらくしてからもう一度お試しください。';
    }

    /**
     * Get sanitized error message for development
     */
    private static function getSanitizedErrorMessage(\Exception $exception): string
    {
        $message = $exception->getMessage();

        // Remove sensitive information patterns
        $patterns = [
            '/password[=:]\s*[^\s,)]+/i',
            '/api[_-]?key[=:]\s*[^\s,)]+/i',
            '/token[=:]\s*[^\s,)]+/i',
            '/secret[=:]\s*[^\s,)]+/i',
            '/\/[a-zA-Z]:[\\\\\/][^\\s]+/i', // Windows file paths
            '/\/[^\\s]+\/[^\\s]+/i', // Unix file paths (partial)
            '/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/', // IP addresses
            '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', // Email addresses
        ];

        $sanitized = preg_replace($patterns, '[REDACTED]', $message);

        // Limit message length
        if (strlen($sanitized) > 200) {
            $sanitized = substr($sanitized, 0, 200) . '...';
        }

        return $sanitized;
    }

    /**
     * Sanitize error for API responses
     */
    public static function sanitizeForApi(\Exception $exception): array
    {
        $isProduction = app()->environment('production');

        $response = [
            'success' => false,
            'error' => self::sanitizeError($exception, $isProduction)
        ];

        // Add error code for specific exceptions
        if ($exception instanceof \Illuminate\Validation\ValidationException) {
            $response['error_code'] = 'VALIDATION_ERROR';
        } elseif ($exception instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
            $response['error_code'] = 'NOT_FOUND';
        } elseif ($exception instanceof \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException) {
            $response['error_code'] = 'ACCESS_DENIED';
        } elseif ($exception instanceof \Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException) {
            $response['error_code'] = 'UNAUTHORIZED';
        } else {
            $response['error_code'] = 'INTERNAL_ERROR';
        }

        // In development, add more details
        if (!$isProduction) {
            $response['debug'] = [
                'exception_class' => get_class($exception),
                'file' => basename($exception->getFile()),
                'line' => $exception->getLine()
            ];
        }

        return $response;
    }

    /**
     * Check if error message contains sensitive information
     */
    public static function containsSensitiveInfo(string $message): bool
    {
        $sensitivePatterns = [
            '/password/i',
            '/api[_-]?key/i',
            '/token/i',
            '/secret/i',
            '/database/i',
            '/connection/i',
            '/sql/i',
            '/query/i',
            '/file.*not.*found/i',
            '/permission.*denied/i',
            '/access.*denied/i',
        ];

        foreach ($sensitivePatterns as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }

        return false;
    }
}
