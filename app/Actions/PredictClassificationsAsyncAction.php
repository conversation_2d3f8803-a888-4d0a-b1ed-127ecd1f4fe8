<?php

namespace App\Actions;

use App\Enums\PRMEnum;
use App\Models\ProofFile;
use Illuminate\Support\Arr;
use Spatie\Async\Pool;
use Illuminate\Support\Facades\Log;


class PredictClassificationsAsyncAction
{
    private ProofFile $proofFile;

    public function __construct(
        public PredictClassificationsAction $action
    ) {
    }

    public function execute(ProofFile $proofFile, array|PRMEnum $prmEnums): array
    {
        Log::debug("PredictClassificationsAsyncAction excute call");
        $this->proofFile = $proofFile;

        $pool = Pool::create();

        foreach ($prmEnums as $prmEnum) {
            $pool->add(fn () => $this->run($prmEnum));
        }

        return [...$pool->wait()];
    }

    private function run($prmEnum)
    {
        $this->bootstrap();

        return Arr::first(
            $this->action->execute($this->proofFile, [$prmEnum])
        );
    }

    private function bootstrap()
    {
        $app = require __DIR__.'/../../bootstrap/app.php';

        $kernel = $app->make(\Illuminate\Contracts\Console\Kernel::class);
        $kernel->bootstrap();
    }
}
