<?php

namespace App\Services\FunctionHandlers;

class GetCardCheckInstructionsHandler
{
    /**
     * Get card check instructions and requirements
     */
    public function execute(array $arguments): array
    {
        $checkType = $arguments['checkType'] ?? 'general';

        $instructions = [
            'general' => [
                'title' => 'カードチェック全般の手順',
                'overview' => 'カードチェックシステムでは、カード仕様と実際の版下データの整合性を自動的に確認します。',
                'steps' => [
                    '1. ファイル準備' => [
                        'カードデザインファイル（PDF、AI、PSD形式）',
                        '対応する仕様書（PDF形式）',
                        '必要に応じて元素材ファイル'
                    ],
                    '2. アップロード' => [
                        'メインファイルをドラッグ&ドロップ',
                        '仕様書をアップロード',
                        'ファイル形式と容量の確認'
                    ],
                    '3. チェック設定' => [
                        'チェック項目の選択',
                        '許容誤差の設定',
                        '優先度の設定'
                    ],
                    '4. 実行と結果確認' => [
                        'チェック開始ボタンをクリック',
                        '進行状況の確認',
                        '結果レポートの確認'
                    ]
                ]
            ],
            '仕様確認' => [
                'title' => '仕様確認の詳細',
                'check_items' => [
                    '寸法チェック' => [
                        'カードサイズ（幅×高さ）',
                        'マージン・余白',
                        '要素の配置位置',
                        'フォントサイズ'
                    ],
                    '色彩チェック' => [
                        'CMYK値の確認',
                        'RGB値の確認',
                        '特色の使用',
                        'グラデーションの再現性'
                    ],
                    'フォントチェック' => [
                        'フォント種類の確認',
                        'フォントサイズの確認',
                        '文字間隔・行間',
                        'アウトライン化の確認'
                    ],
                    '画像チェック' => [
                        '解像度の確認（300dpi以上）',
                        '画像形式の確認',
                        '透明度の処理',
                        'トリミング範囲'
                    ]
                ],
                'tolerance_settings' => [
                    '寸法許容誤差' => '±0.1mm',
                    '色彩許容誤差' => 'CMYK各色±5%',
                    'フォントサイズ許容誤差' => '±0.5pt',
                    '位置許容誤差' => '±0.5mm'
                ]
            ],
            'エラー修正' => [
                'title' => 'エラー修正の手順',
                'common_errors' => [
                    '寸法エラー' => [
                        '原因' => 'カードサイズが仕様と異なる',
                        '修正方法' => 'デザインソフトでアートボードサイズを調整',
                        '確認点' => 'トリムマークの位置も確認'
                    ],
                    '色彩エラー' => [
                        '原因' => 'CMYK値が仕様と異なる',
                        '修正方法' => 'カラーパレットで正確な値に調整',
                        '確認点' => 'プロファイル設定も確認'
                    ],
                    'フォントエラー' => [
                        '原因' => 'フォントが仕様と異なる、またはアウトライン化されていない',
                        '修正方法' => '正しいフォントに変更、またはアウトライン化実行',
                        '確認点' => 'フォントライセンスの確認'
                    ],
                    '解像度エラー' => [
                        '原因' => '画像解像度が不足',
                        '修正方法' => '高解像度画像に差し替え',
                        '確認点' => 'リサンプリング設定の確認'
                    ]
                ],
                'fix_workflow' => [
                    '1. エラー詳細の確認',
                    '2. 該当箇所の特定',
                    '3. デザインソフトでの修正',
                    '4. 修正版の再アップロード',
                    '5. 再チェックの実行',
                    '6. 結果の確認'
                ]
            ],
            '結果確認' => [
                'title' => 'チェック結果の確認方法',
                'result_types' => [
                    '適合' => [
                        '表示' => '緑色のチェックマーク',
                        '意味' => '仕様に完全に適合',
                        '対応' => '特に対応不要'
                    ],
                    '警告' => [
                        '表示' => '黄色の警告マーク',
                        '意味' => '軽微な差異あり',
                        '対応' => '必要に応じて修正'
                    ],
                    'エラー' => [
                        '表示' => '赤色のエラーマーク',
                        '意味' => '仕様から大きく逸脱',
                        '対応' => '修正が必要'
                    ]
                ],
                'report_sections' => [
                    '概要' => '全体的な適合率とエラー数',
                    '詳細結果' => '項目別の詳細な結果',
                    '修正提案' => '具体的な修正方法',
                    '比較画像' => '仕様との視覚的比較'
                ],
                'export_options' => [
                    'PDF形式でのレポート出力',
                    'Excel形式での詳細データ',
                    '画像形式での比較結果',
                    'チーム共有用のリンク生成'
                ]
            ]
        ];

        if (isset($instructions[$checkType])) {
            return [
                'success' => true,
                'check_type' => $checkType,
                'instructions' => $instructions[$checkType],
                'supported_formats' => [
                    'design_files' => ['PDF', 'AI', 'PSD', 'INDD'],
                    'specification_files' => ['PDF', 'DOC', 'DOCX'],
                    'max_file_size' => '100MB'
                ]
            ];
        }

        return [
            'success' => true,
            'available_types' => array_keys($instructions),
            'general_info' => $instructions['general']
        ];
    }
}