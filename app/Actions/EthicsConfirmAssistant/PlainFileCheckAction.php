<?php

namespace App\Actions\EthicsConfirmAssistant;

use App\Http\API\EthicsConfirmAssistant;
use App\Models\Proof;

class PlainFileCheckAction
{
    public function __construct(
        private EthicsConfirmAssistant $api,
    ) {
    }

    /**
     * @param Proof $proof
     */
    public function execute(Proof $proof)
    {
        $response = $this->api->plainFileCheck($proof);

        return $response->json();
    }
}