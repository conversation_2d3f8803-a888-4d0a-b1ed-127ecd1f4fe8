<?php

namespace App\Data;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Spatie\LaravelData\Data;

class RequestFormContactFormData extends Data
{
    public string $to;
    public string $label;
    public string $icon;
    public string $color;

    public function __construct(
        string $to,
        string $label,
        string $icon,
        string $color
    ) {
        $this->to = $to;
        $this->label = $label;
        $this->icon = $icon;
        $this->color = $color;
    }

    public static function fromRequest(Request $request)
    {
        return new self(
            to: $request->to ?? "",
            label: $request->label ?? "",
            icon: $request->icon ?? "",
            color: $request->color ?? "",
        );
    }
}
