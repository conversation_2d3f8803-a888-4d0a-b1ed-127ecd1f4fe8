<template>
  <div>
    <v-switch
      label="自分がチェック済み"
      hide-details
      dense
      :input-value="this.filter.checkedOnly"
      @change="change"
    />
  </div>
</template>

<script>
import { filterSerialize as serialize } from '../../lib/utils';

export default {
  name: 'MyCompletionProof',
  props: ['filter', 'target'],
  methods: {
    reset() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: false,
        },
        page: 1,
      };

      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },
    submit() {
      const newFilter = {
        ...this.filter,
        ...{
          [this.target]: true,
        },
        page: 1,
      };

      newFilter.unCheckedOnly = false;

      console.log(newFilter);
      const path = location.pathname + serialize(newFilter);

      this.$inertia.get(path);
    },
    change(active) {
      active ? this.submit() : this.reset();
    },
  },
};
</script>