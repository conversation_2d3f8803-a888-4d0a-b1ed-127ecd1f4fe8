<template>
  <div>
    <h1 style="color: rgba(64, 64, 64, 1); height: 100px;" class="d-flex mx-7">注釈テンプレート一覧 </h1>
    <v-data-table
      :headers="headers"
      :items="paginator.data"
      :items-per-page="paginator.per_page"
      @click:row="show"
      class="row-pointer mx-7"
      elevation="0"
      no-data-text="データが存在しません"
      hide-default-footer
      dense
      disable-sort
    >
      <template v-slot:top>
        <v-toolbar flat class="annotation-title">
          <v-spacer></v-spacer>
          <v-btn
            color="primary"
            class="mb-2 white--text"
            elevation="0"
            :href="route('admin.utils.annotation-templates.create')"
          >
            <v-icon left dark>
              mdi-comment-plus
            </v-icon>
            注釈テンプレート登録
          </v-btn>
        </v-toolbar>
      </template>

      <template v-slot:[`item.annotation`]="{ item }">
        <span
          style="word-break: break-all; white-space: break-spaces; margin: 0%;"
          class="table-text"
          v-html="nl2br(item.annotation)"
        />
      </template>

      <template v-slot:[`item.user`]="{ item }">
        <span class="table-text" style="margin: 0%;">
          {{ item.user }}
        </span>
      </template>

      <!-- FOOTER -->
      <template v-slot:footer="{}">
        <Pagination :paginator="paginator" />
      </template>
    </v-data-table>
  </div>
</template>

<script>
import Layout from '@/Layouts/NewLayout';
import Pagination from '@/Components/Pagination';

export default {
  name: 'AdminProofCategoryIndex',
  layout: Layout,
  props: {
    paginator: {
      type: Object,
      required: true,
    },
  },
  components: {
    Pagination,
  },

  data() {
    return {
      headers: [
        { text: '注釈テンプレート', value: 'annotation', width: '80%' },
        { text: '登録者', value: 'user', width: '10%' },
      ],
    };
  },

  methods: {
    nl2br(text) {
      return text.replace(/(?:\r\n|\r|\n)/g, '<br>');
    },
    show({ id }) {
      const path = this.route('admin.utils.annotation-templates.show', { id });

      this.$inertia.get(path);
    },
  },
};
</script>

<style scoped lang="css">
/* パスワード初期化チェックボックススタイル */
.v-input--selection-controls {
  margin: 0px;
  padding: 0px;
}

.row-pointer >>> tbody > tr:hover {
  cursor: pointer;
}

.row-pointer ::v-deep(td),
.row-pointer ::v-deep(th) {
  white-space: nowrap;
  padding-left: 10px !important;
  padding-right: 0px !important;
  color: rgba(64,64,64,1) !important

}

.row-pointer ::v-deep(th)
{
  padding-top: 7px !important;
}

.row-pointer >>> th > span {
  font-size: 15px !important;
}

.row-pointer ::v-deep(span) {
  font-size: 14px;
}

.row-pointer ::v-deep(tr) {
  height: 60px;
}

.table-text{
  font-size: 14px;
}

.annotation-title ::v-deep(.v-toolbar__content) {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
</style>
