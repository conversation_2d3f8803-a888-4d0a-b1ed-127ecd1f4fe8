<?php

namespace Database\Seeders;

use App\Models\Proof;
use App\Models\ProofNumber;
use Illuminate\Database\Seeder;

class ProofNumberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        ProofNumber::create([
            'company_domain' => 'xp',
            'company_code' => 'BNXP',
        ]);

        ProofNumber::create([
            'company_domain' => 'am',
            'company_code' => 'BNAM',
        ]);

        ProofNumber::create([
            'company_domain' => 'al',
            'company_code' => 'BNAL',
        ]);

        ProofNumber::create([
            'company_domain' => 'default',
            'company_code' => 'BNXP',
        ]);
    }
}
