<?php

namespace App\Actions;

use App\Data\UpsertNoticeData;
use App\Models\Notice;

class UpsertNoticeAction
{
    public function execute(UpsertNoticeData $data): Notice
    {
        return Notice::updateOrCreate([
            'id' => $data->id
        ], [
            'title' => $data->title,
            'body' => $data->body,
            'visible' => $data->visible,
            'user_type_id' => $data->user_type->id,
            'published_at' => $data->published_at,
            'closed_at' => $data->closed_at,
            'updated_at' => now()
        ]);
    }
}
