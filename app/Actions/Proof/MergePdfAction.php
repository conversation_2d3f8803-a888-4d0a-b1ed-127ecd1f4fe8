<?php

namespace App\Actions\Proof;

use App\Actions\BinaryToUploadedFileAction;
use Illuminate\Support\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Karriere\PdfMerge\PdfMerge;

class MergePdfAction
{
    public function __construct(
        private PdfMerge $merger,
        private BinaryToUploadedFileAction $binaryToUploadedFileAction
    ) {
    }

    /**
     * 複数のPDFファイルをマージして、新しいPDFファイルを生成する。
     *
     * @param string $fileBaseName
     * @param Collection<int, UploadedFile> $files
     *
     * @return UploadedFile
     */
    public function execute(string $fileBaseName, Collection $files): UploadedFile
    {
        $output = sys_get_temp_dir() . '/' . $fileBaseName;

        try {
            $files->each(
                fn (UploadedFile $file) => $this->merger->add($file->getPathname())
            );

            $this->merger->merge($output);

            return $this->binaryToUploadedFileAction->execute(
                fileBaseName: $fileBaseName,
                binary: file_get_contents($output)
            );
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            throw $th;
        }
    }
}
