<?php

namespace App\Services\FunctionHandlers;

class NavigateToPageHandler
{
    /**
     * Handle navigation to a specific page
     */
    public function execute(array $arguments): array
    {
        $page = $arguments['page'] ?? 'ホーム';

        // Map page names to routes
        $pageRoutes = [
            'ホーム' => 'aic.index',
            '倫理確認システム' => 'qg.index',
            'カードチェック' => 'cc.index',
            '申請フォーム' => 'requestform.index',
            '申請履歴' => 'requestform.history'
        ];

        $route = $pageRoutes[$page] ?? 'aic.index';

        return [
            'success' => true,
            'action' => 'navigate',
            'page' => $page,
            'route' => $route,
            'url' => route($route),
            'message' => "{$page}ページに移動します。"
        ];
    }
}