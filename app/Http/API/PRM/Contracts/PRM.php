<?php

namespace App\Http\API\PRM\Contracts;

use Illuminate\Support\Facades\Http;
use Illuminate\Http\Client\Response;
use DateTime;
use Exception;
use Illuminate\Support\Arr;

/**
 * BAPIセット事例リコメンドAPI
 * Dockerベース
 */
abstract class PRM
{
    public $client = null;
    public $code = 200;
    public $message = '';

    // Errors
    public $errors = [
        'fail' => '認証トークンのエラー又正しい分類項目ではありません。',
        'busy' => '実行中です。今しばらくお待ちください。',
        'time out' => 'タイムアウトが発生しました。',
    ];

    public function __construct()
    {
        $this->client = Http::asForm()
                            ->baseUrl(config('prm.url'));
    }

    public function version()
    {
        $response = $this->client->post('/file-class/version/', [
            'auth_token' => $this->getAuthToken(config('prm.hash_prefix')),
        ]);

        try {
            if ($this->fails($response)) {
                throw new Exception(
                    "APIバージョンの取得API: {$this->message}",
                    $this->code
                );
            }

            $result = $response->json();

            return $result['version'];
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    protected function getAuthToken(string $prefix)
    {
        $date = (new DateTime())->format('Ymd');

        $text = $prefix . $date;

        $hash_str = hash('sha256', $text);

        return substr($hash_str, 0, 16);
    }

    protected function fails(Response $response)
    {
        // Http Status Code 200以外エラー
        $response->throw(function ($res, $e) {
            $this->code = $e->getCode();
            $this->message= $e->getMessage();

            return true;
        });

        $result = $response->json()['result'];

        // public function predictのstring $textが空白('')の場合
        if ($result == 'no text') {
            $this->code = $response->status();
            $this->message = "ファイルにテキストデータがない為、予測することができませんでした";

            return true;
        }

        // Http Status Code 200でもエラーが発生した場合
        if ($result !== 'success') {
            $this->code = $response->status();
            $this->message = Arr::get($this->errors, $result, '');

            return true;
        }

        return false;
    }

    abstract public function predict(string $text): array;
    abstract public function annotationSamples(): array;
    abstract public function annotations(string|int $class_no): array;
}
