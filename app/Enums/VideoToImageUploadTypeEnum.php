<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static General()
 * @method static static Legal()
 */
final class VideoToImageUploadTypeEnum extends Enum
{
    const File    =   'file';
    const S3Key   =   's3_key';

    public static function getDescription($value): string
    {
        return match ($value) {
            self::File => 'PDFにする動画ファイル【アップロード】',
            self::S3Key => 'PDFにする動画ファイル【選択】',
            default => parent::getDescription($value)
        };
    }

    public function toArray()
    {
        return $this;
    }
}
