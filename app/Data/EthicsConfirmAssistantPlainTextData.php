<?php

namespace App\Data;

use App\Enums\EthicsConfirmAssistantPlainDataTypeEnum;
use Spatie\LaravelData\Data;

class EthicsConfirmAssistantPlainTextData extends Data
{
    public function __construct(
        public EthicsConfirmAssistantPlainDataTypeEnum $type,
        public string $content,
        public string $description,
    ) {
    }

    public function toArray(): array
    {
        return [
            'type' => $this->type->value,
            'content' => $this->content,
            'description' => $this->description,
        ];
    }
}
