<?php

namespace App\Actions\Proof;

use App\Models\Proof;
use App\Models\ProofReservationLimit;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriod;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GetProofReservationWithInPeriodAction
{
    public const DATE_FORMAT = 'Y-m-d';

    public function __construct(
        // @TODO おそらく削除予定
        // private GetProofReservedAction $getProofReservedAction,
    ) {
    }

    public function execute(CarbonImmutable $start, CarbonImmutable $end): array
    {
        $period = CarbonPeriod::create($start, $end)->day();

        $proofs = $this->getProofs($period->start, $period->end);
        $dailyStatus = $this->getDailyStatus($period->start, $period->end);

        return [
            'proofs' => $proofs,
            'dailyStatus' => $dailyStatus
        ];
    }

    /**
     * Get proofs
     *
     * @param CarbonImmutable $start
     * @param CarbonImmutable $end
     *
     * @return Collection
     */
    private function getProofs(Carbon $start, Carbon $end): Collection
    {
        return Proof::query()
                ->with([
                    'members',
                    'business_categories',
                ])
                ->where('created_at', '>', $start)
                ->where('external_deadline_at', '<', $end)
                ->get();
    }

    private function getColor(int $reserved, int $limit): string
    {
        $reservationPercent = ($reserved / $limit) * 100;

        return Arr::first(
            config('proof.reservation.colors'),
            fn (string $_, int $percent) =>   $reservationPercent <= $percent,
            'red'
        );
    }

    /**
     * Get daily status
     *
     * This method will return the daily status of the proofs
     *
     * @param Carbon $start
     * @param Carbon $end
     *
     * @return Collection
     * [
     *    '2024-04-01' => [
     *      'limit' => 10,
     *      'reserved' => 10,
     *      'reservedColor' => 'red'
     *    ]
     *    ...
     * ]
     */
    private function getDailyStatus(Carbon $start, Carbon $end): Collection
    {
        $DEFAULT_LIMIT_VALUE = config('proof.reservation.default_limit');
        $date = [
            'start' => "'{$start->format(self::DATE_FORMAT)}'",
            'end' => "'{$end->format(self::DATE_FORMAT)}'",
        ];

        $rows = DB::select(DB::raw(<<<SQL
            -- 期間内の日付を取得して仮想テーブル化
            -- 2024-04-01 ~ 2024-04-30の場合: 2024-04-01, 2024-04-02, ..., 2024-04-30
            WITH RECURSIVE `days` AS (
                SELECT 
                    {$date['start']} AS `dt`
                UNION ALL
                SELECT 
                    `dt` + INTERVAL 1 DAY
                FROM 
                    `days`
                WHERE 
                    `dt` < {$date['end']}
            )
            SELECT
                -- 日付                
                `days`.`dt` AS `date`,
                -- 予約可能数
                COALESCE(`limits`.`limit`, {$DEFAULT_LIMIT_VALUE}) AS `limit`,
                -- 予約済み数
                COUNT(`proofs`.`id`) AS `reserved`

            FROM 
                `days`
                LEFT OUTER JOIN `proofs` ON `days`.`dt` BETWEEN DATE(`proofs`.`created_at`) AND DATE(`proofs`.`external_deadline_at`)
                LEFT OUTER JOIN `proof_reservation_limits` AS `limits` ON `days`.`dt` = `limits`.`date`
            GROUP BY
                `days`.`dt`,
                `limits`.`limit`
        SQL));

        return collect($rows)->mapWithKeys(fn (object $row) => [
            $row->date => [
                'limit' => $row->limit,
                'reserved' => $row->reserved,
                'reservedColor' => $this->getColor($row->reserved, $row->limit),
            ]
        ]);
    }
}
