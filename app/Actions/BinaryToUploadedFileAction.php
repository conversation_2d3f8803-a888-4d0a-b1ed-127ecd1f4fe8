<?php

namespace App\Actions;

use App\Helpers\ProofFileManager;
use Illuminate\Http\UploadedFile;

class BinaryToUploadedFileAction
{
    public function execute(string $fileBaseName, string $binary): UploadedFile
    {
        $path = sys_get_temp_dir() . '/' . $fileBaseName;

        file_put_contents($path, $binary);

        return new UploadedFile(
            path: $path,
            originalName: $fileBaseName,
            mimeType: ProofFileManager::getMIME(
                pathinfo($fileBaseName, PATHINFO_EXTENSION)
            )
        );
    }
}
