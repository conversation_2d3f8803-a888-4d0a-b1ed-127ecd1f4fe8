<?php

namespace App\Console\Commands;

use App\Helpers\ProofCloudSearchManager;
use App\Models\ProofCloudSearch;
use Illuminate\Console\Command;

class ProofCloudSearchDeletes extends Command
{
    private $found = [];
    private $current;
    private $isContinue = false;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cloudsearch:deletes 
                            {ids* : proof_cloud_searchesテーブルのid}
                            ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = <<<DESC
    AWS CloudSearchの検索するドキュメント(PDF)を削除する。

    example 1)
    php artisan cloudsearch:deletes 1 2 3 4
    DESC;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->init();

        if (!$this->isContinue) {
            return 0;
        }

        $this->info('AWS CloudSearch Document Delete Start...');

        try {
            collect($this->found)->each(function ($model, $index) {
                $this->current = $model;

                ProofCloudSearchManager::deleteDocument($model);

                $this->progressMessage($model, $index + 1, count($this->found));
            });

            $this->newLine();
            $this->info('AWS CloudSearch Document Delete Finish.');
            
            return 0;
        } catch (\Throwable $th) {
            $this->error($this->current->id);
            $this->error($th->getmessage());
            
            throw $th;
        }
    }

    private function init()
    {
        $ids = $this->argument("ids");
        $hasAll = is_numeric(array_search('all', $ids));
        
        $found = $hasAll ? ProofCloudSearch::all() : ProofCloudSearch::find($ids);

        $this->info('AWS CloudSearch Document Deleting Ready');
        $this->line(
            sprintf(
                "Found <fg=green>%s items</> out of %s in proof_cloud_searches(RDB)",
                count($found),
                count($ids)
            )
        );
        
        if (count($found) !== 0) {
            $this->isContinue = $this->confirm("Do you want to continue?");
            $this->found = $found;
        }
    }

    private function progressMessage(ProofCloudSearch $model, int $currentCount, int $totalCount)
    {
        $message = sprintf(
            "(%s%%) (%s/%s) - <fg=green>Success</> %s",
            round(($currentCount * 100) / $totalCount, 2),
            $currentCount,
            $totalCount,
            $model->path,
        );
        
        $this->line($message);
    }
}
