<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Proof\Category\UpsertCategoryAction;
use App\Actions\Proof\Category\UpsertIntellectualPropertyAction;
use App\Actions\User\AicUpdateUserAction as UserUpdateUserAction;
use App\Data\UpsertCategoryData;
use App\Data\UpsertIntellectualPropertyData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserController\UpdateRequest as UserUpdateRequest;
use App\Models\Category;
use App\Models\IntellectualProperty;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class CategoryController extends Controller
{
    public function index()
    {
        $users = User::orWhere('user_type_id', User::USER_TYPE_ADMIN)
            ->orWhere('user_type_id', User::USER_TYPE_MEMBER)
            ->with([
                'ips',
                'ips.parent',
                'categories',
                'completion_mark_sorts'
            ])
            ->orderByDesc('id')
            ->orderByDesc('created_at')->get();

        $categories = Category::query()->orderByDesc("created_at")->paginate(
            perPage: self::PER_PAGE,
        )
        ->appends(request()->except(['page', '_token']));

        $intellectualProperties = IntellectualProperty::query()
            ->with('children')
            ->whereNull('parent_id')
            ->orderByDesc("created_at")
            ->paginate(
                perPage: self::PER_PAGE,
                pageName: 'ip',
            );

        return Inertia::render('Admin/Proof/Category/AicIndex', [
            'categories' => fn() => $categories,
            'intellectualProperties' => fn() => $intellectualProperties,
            'users' => fn() => $users,
        ]);
    }

    public function ipIndex()
    {
        $users = User::orWhere('user_type_id', User::USER_TYPE_ADMIN)
            ->orWhere('user_type_id', User::USER_TYPE_MEMBER)
            ->with([
                'ips',
                'ips.parent',
                'categories',
                'completion_mark_sorts'
            ])
            ->orderByDesc('id')
            ->orderByDesc('created_at')->get();

        $categories = Category::query()->orderByDesc("created_at")->paginate(
            perPage: self::PER_PAGE,
            pageName: 'category',
        );

        Log::info('page', ['page' => request()->except(['page', '_token'])]);
        $intellectualProperties = IntellectualProperty::query()
            ->with('children')
            ->whereNull('parent_id')
            ->orderByDesc("created_at")
            ->paginate(
                perPage: self::PER_PAGE,
            )
            ->appends(request()->except(['page', '_token']));

        return Inertia::render('Admin/Proof/Category/AicIpIndex', [
            'categories' => fn() => $categories,
            'intellectualProperties' => fn() => $intellectualProperties,
            'users' => fn() => $users,
        ]);
    }

    public function aicIndex(Request $request)
    {
        $tabs = $request->all()['tabs'] ?? null;
        $users = User::orWhere('user_type_id', User::USER_TYPE_ADMIN)
            ->orWhere('user_type_id', User::USER_TYPE_MEMBER)
            ->withTrashed()
            ->with([
                'ips',
                'ips.parent',
                'categories',
                'completion_mark_sorts'
            ])
            ->orderByDesc('id')
            ->orderByDesc('created_at')->get();

        $categories = Category::query()->orderByDesc("created_at")->paginate(
            perPage: self::PER_PAGE,
            pageName: 'category',
        );

        $intellectualProperties = IntellectualProperty::query()
            ->with('children')
            ->whereNull('parent_id')
            ->orderByDesc("created_at")
            ->paginate(
                perPage: self::PER_PAGE,
                pageName: 'ip',
            );

        return Inertia::render('Admin/Proof/Category/Index', [
            'categories' => fn() => $categories,
            'intellectualProperties' => fn() => $intellectualProperties,
            'users' => fn() => $users,
            'tabs' => $tabs,
        ]);
    }

    public function upsertCategory(
        UpsertCategoryData $data,
        UpsertCategoryAction $action,
    ) {
        try {
            DB::beginTransaction();

            $action->execute($data);

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }

        return redirect()->back(self::HTTP_STATUS_SEE_OTHER)->with('snackbar', [
            'color' => 'primary',
            'icon' => 'mdi-check-circle',
            'title' => '成功',
            'message' => $data->name . 'を保存しました。'
        ]);
    }

    public function deleteCategory(Category $category)
    {
        try {
            DB::beginTransaction();

            $category->delete();

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }

        return redirect()->back(self::HTTP_STATUS_SEE_OTHER)->with('snackbar', [
            'color' => 'primary',
            'icon' => 'mdi-check-circle',
            'title' => '成功',
            'message' => $category->name . 'を削除しました。'
        ]);
    }

    public function upsertIntellectualProperty(
        UpsertIntellectualPropertyData $data,
        UpsertIntellectualPropertyAction $action,
    ) {
        try {
            DB::beginTransaction();

            $action->execute($data);

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }

        return redirect()->back(self::HTTP_STATUS_SEE_OTHER)->with('snackbar', [
            'color' => 'primary',
            'icon' => 'mdi-check-circle',
            'title' => '成功',
            'message' => $data->name . 'を保存しました。'
        ]);
    }

    public function deleteIntellectualProperty(IntellectualProperty $intellectualProperty)
    {
        try {
            DB::beginTransaction();

            $intellectualProperty->delete();

            DB::commit();
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }

        return redirect()->back(self::HTTP_STATUS_SEE_OTHER)->with('snackbar', [
            'color' => 'primary',
            'icon' => 'mdi-check-circle',
            'title' => '成功',
            'message' => $intellectualProperty->name . 'を削除しました。'
        ]);
    }

    public function updateCategoryUser(
        UserUpdateRequest $request,
        User $user,
        UserUpdateUserAction $action
    ) {
        try {
            DB::beginTransaction();

            $action->execute($user, $request->data());

            DB::commit();

            return redirect()->back(self::HTTP_STATUS_SEE_OTHER)->with('snackbar', [
                'color' => 'primary',
                'icon' => 'mdi-check-circle',
                'title' => '成功',
                'message' => $user['name'] . 'の情報を更新しました。'
            ]);
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }
    }
    public function updateUser(
        UserUpdateRequest $request,
        User $user,
        UserUpdateUserAction $action
    ) {
        try {
            DB::beginTransaction();

            $action->execute($user, $request->data());

            DB::commit();

            return redirect()->back(self::HTTP_STATUS_SEE_OTHER)->with('snackbar', [
                'color' => 'primary',
                'icon' => 'mdi-check-circle',
                'title' => '成功',
                'message' => $user['name'] . 'の情報を更新しました。'
            ]);
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }
    }
}
