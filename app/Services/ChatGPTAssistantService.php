<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class ChatGPTAssistantService
{
    private string $apiKey;
    private string $baseUrl;
    private string $model;
    private array $functions;

    // Recursion limits
    private const MAX_FUNCTION_CALLS = 5;
    private const MAX_RECURSION_DEPTH = 3;

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key');
        $this->baseUrl = config('services.openai.base_url', 'https://api.openai.com/v1');
        $this->model = config('services.openai.model', 'gpt-4-1106-preview');
        $this->functions = $this->getFunctionDefinitions();
    }

    /**
     * Process user query with ChatGPT and handle function calls
     */
    public function processQuery(string $query, string $currentPage = 'ホーム', array $context = [], int $recursionDepth = 0): array
    {
        try {
            // Check recursion depth limit
            if ($recursionDepth >= self::MAX_RECURSION_DEPTH) {
                Log::warning('Function call recursion depth limit reached', ['depth' => $recursionDepth]);
                return [
                    'success' => true,
                    'response' => 'すみません、処理が複雑になりすぎました。より簡単な質問でお試しください。',
                    'function_calls' => []
                ];
            }

            // Sanitize inputs to prevent prompt injection
            $query = InputSanitizer::sanitizeQuery($query);
            $context = InputSanitizer::sanitizeContext($context);

            // Add user role mapping to context
            $context = $this->addUserRoleToContext($context);

            $messages = $this->buildMessages($query, $currentPage, $context);

            $response = $this->callChatGPT($messages, $this->functions);

            if (isset($response['choices'][0]['message']['tool_calls'])) {
                return $this->handleFunctionCalls($response, $messages, $context, $recursionDepth);
            }

            return [
                'success' => true,
                'response' => $response['choices'][0]['message']['content'] ?? 'すみません、回答を生成できませんでした。',
                'function_calls' => []
            ];

        } catch (Exception $e) {
            Log::error('ChatGPT Assistant Error: ' . $e->getMessage(), [
                'user_id' => auth()->id() ?? 'anonymous',
                'query_length' => strlen($query),
                'context_keys' => array_keys($context)
            ]);

            return [
                'success' => false,
                'response' => 'すみません、一時的にサービスが利用できません。しばらくしてからもう一度お試しください。',
                'error' => ErrorSanitizer::sanitizeError($e)
            ];
        }
    }

    /**
     * Add user role mapping to context
     */
    private function addUserRoleToContext(array $context): array
    {
        $userContext = UserRoleMappingService::getUserContextForAssistant();
        return array_merge($context, $userContext);
    }

    /**
     * Build messages array for ChatGPT
     */
    private function buildMessages(string $query, string $currentPage, array $context): array
    {
        $systemPrompt = $this->getSystemPrompt($currentPage);

        $messages = [
            [
                'role' => 'system',
                'content' => $systemPrompt
            ],
            [
                'role' => 'user',
                'content' => $query
            ]
        ];

        // Add context if provided (already sanitized)
        if (!empty($context)) {
            // Limit context size to prevent token overflow
            $contextString = json_encode($context, JSON_UNESCAPED_UNICODE);
            if (strlen($contextString) > 2000) {
                $context = ['note' => 'Context truncated due to size'];
                $contextString = json_encode($context, JSON_UNESCAPED_UNICODE);
            }

            $contextMessage = "現在のコンテキスト: " . $contextString;
            $messages[] = [
                'role' => 'system',
                'content' => $contextMessage
            ];
        }

        return $messages;
    }

    /**
     * Get system prompt based on current page
     */
    private function getSystemPrompt(string $currentPage): string
    {
        $basePrompt = "あなたはAIC（AI Check）システムのアシスタントです。ユーザーが現在「{$currentPage}」ページにいます。

あなたの役割:
1. ユーザーの質問に日本語で丁寧に回答する
2. システムの機能や使い方を説明する
3. 必要に応じて適切な関数を呼び出してシステム操作を支援する
4. 倫理確認システムとカードチェック機能について詳しく案内する

利用可能な機能:
- 倫理確認システム: 製品や公表文書の倫理的観点からのチェック
- カードチェック: カード仕様と版下の整合性チェック
- 申請フォーム: 各種申請の提出
- 申請履歴: 過去の申請状況確認

回答ガイドライン:
- 回答は簡潔で分かりやすく、具体的な手順を含めてください
- 利用可能な情報に基づいて完全な回答を提供してください
- フォローアップの質問や追加情報の要求は行わないでください
- 確定的で実用的な回答を心がけてください";

        $pageSpecificPrompts = [
            'ホーム' => "\n\n現在ホームページにいます。倫理確認システムとカードチェックへの案内を中心に対応してください。",
            '倫理確認システム' => "\n\n現在倫理確認システムページにいます。文書のアップロードや倫理チェックの手順について詳しく説明してください。",
            'カードチェック' => "\n\n現在カードチェックページにいます。カード仕様チェックの方法や結果の見方について説明してください。",
            '申請フォーム' => "\n\n現在申請フォームページにいます。ファイルのアップロード方法や必須項目について案内してください。",
            '申請履歴' => "\n\n現在申請履歴ページにいます。申請状況の確認方法や修正・再提出について説明してください。"
        ];

        return $basePrompt . ($pageSpecificPrompts[$currentPage] ?? '');
    }

    /**
     * Call ChatGPT API with function calling support
     */
    private function callChatGPT(array $messages, array $functions = []): array
    {
        $data = [
            'messages' => $messages,
            'max_tokens' => 1000,
            'temperature' => 0.7,
        ];

        // Check if using Azure OpenAI
        $isAzure = strpos($this->baseUrl, 'openai.azure.com') !== false;

        if (!$isAzure) {
            // Standard OpenAI API requires model parameter
            $data['model'] = $this->model;
        }

        if (!empty($functions)) {
            $data['tools'] = array_map(function($func) {
                return ['type' => 'function', 'function' => $func];
            }, $functions);
            $data['tool_choice'] = 'auto';
        }

        // Set appropriate headers based on API type
        $headers = ['Content-Type' => 'application/json'];

        if ($isAzure) {
            $headers['api-key'] = $this->apiKey;
            $url = $this->baseUrl;
        } else {
            // Standard OpenAI uses Authorization Bearer header
            $headers['Authorization'] = 'Bearer ' . $this->apiKey;
            $url = $this->baseUrl . '/chat/completions';
        }

        $response = Http::withHeaders($headers)->timeout(30)->post($url, $data);

        if (!$response->successful()) {
            throw new Exception('ChatGPT API request failed: ' . $response->body());
        }

        return $response->json();
    }

    /**
     * Handle function calls from ChatGPT
     */
    private function handleFunctionCalls(array $response, array $messages, array $context = [], int $recursionDepth = 0): array
    {
        $toolCalls = $response['choices'][0]['message']['tool_calls'];
        $functionResults = [];

        // Check function call limit
        if (count($toolCalls) > self::MAX_FUNCTION_CALLS) {
            Log::warning('Function call limit exceeded', ['count' => count($toolCalls)]);
            return [
                'success' => true,
                'response' => 'すみません、一度に実行できる機能の数を超えました。より簡単な質問でお試しください。',
                'function_calls' => []
            ];
        }

        // Add assistant message with tool calls to conversation
        $messages[] = $response['choices'][0]['message'];

        foreach ($toolCalls as $toolCall) {
            $functionName = $toolCall['function']['name'];
            $arguments = json_decode($toolCall['function']['arguments'], true);

            // Inject user role for manual-related functions
            $arguments = $this->injectUserRoleForManualFunctions($functionName, $arguments, $context);

            $result = $this->executeFunctionCall($functionName, $arguments);
            $functionResults[] = [
                'name' => $functionName,
                'arguments' => $arguments,
                'result' => $result
            ];

            // Add function result to conversation
            $messages[] = [
                'role' => 'tool',
                'tool_call_id' => $toolCall['id'],
                'content' => json_encode($result, JSON_UNESCAPED_UNICODE)
            ];
        }

        // Get final response from ChatGPT with function results
        // Don't allow further function calls to prevent infinite recursion
        $finalResponse = $this->callChatGPT($messages, []);

        // Check if the final response contains more tool calls (shouldn't happen with empty functions array)
        if (isset($finalResponse['choices'][0]['message']['tool_calls'])) {
            Log::warning('Unexpected tool calls in final response, ignoring to prevent recursion');
        }

        return [
            'success' => true,
            'response' => $finalResponse['choices'][0]['message']['content'] ?? 'すみません、回答を生成できませんでした。',
            'function_calls' => $functionResults
        ];
    }

    /**
     * Inject user role for manual-related functions
     */
    private function injectUserRoleForManualFunctions(string $functionName, array $arguments, array $context): array
    {
        // Functions that should use the authenticated user's manual role
        $manualFunctions = ['getEthicsManual'];

        if (in_array($functionName, $manualFunctions)) {
            // Check if user has manual access
            $hasAccess = isset($context['has_manual_access']) ? $context['has_manual_access'] : false;

            if (!$hasAccess) {
                // User doesn't have access, set role to null to trigger access denied
                $arguments['user_role'] = null;

                Log::warning('Manual access denied for function', [
                    'function' => $functionName,
                    'user_type' => $context['user_type'] ?? 'unknown',
                    'has_manual_access' => false
                ]);
            } elseif (isset($context['manual_role'])) {
                // Only override if user_role is not explicitly set or is the default
                if (!isset($arguments['user_role']) || $arguments['user_role'] === 'admin') {
                    $arguments['user_role'] = $context['manual_role'];

                    Log::info('Injected user role for function', [
                        'function' => $functionName,
                        'injected_role' => $context['manual_role'],
                        'user_type' => $context['user_type'] ?? 'unknown'
                    ]);
                }
            }
        }

        return $arguments;
    }

    /**
     * Execute a function call
     */
    private function executeFunctionCall(string $functionName, array $arguments): array
    {
        try {
            $handlerClass = "App\\Services\\FunctionHandlers\\" . ucfirst($functionName) . "Handler";

            if (!class_exists($handlerClass)) {
                Log::error("Function handler not found: {$functionName}");
                return [
                    'success' => false,
                    'error' => 'Function not available'
                ];
            }

            $handler = new $handlerClass();
            return $handler->execute($arguments);

        } catch (Exception $e) {
            Log::error("Function call error: {$functionName} - " . $e->getMessage(), [
                'user_id' => auth()->id() ?? 'anonymous',
                'function_name' => $functionName,
                'arguments_keys' => array_keys($arguments)
            ]);

            return [
                'success' => false,
                'error' => ErrorSanitizer::sanitizeError($e)
            ];
        }
    }

    /**
     * Get function definitions for ChatGPT
     */
    private function getFunctionDefinitions(): array
    {
        return [
            [
                'name' => 'getCurrentPageInfo',
                'description' => '現在のページの情報と利用可能な機能を取得する',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'page' => [
                            'type' => 'string',
                            'description' => 'ページ名（ホーム、倫理確認システム、カードチェック、申請フォーム、申請履歴）'
                        ]
                    ],
                    'required' => ['page']
                ]
            ],
            [
                'name' => 'getSystemStatus',
                'description' => 'システムの稼働状況を確認する',
                'parameters' => [
                    'type' => 'object',
                    'properties' => (object)[],
                    'required' => []
                ]
            ],
            [
                'name' => 'navigateToPage',
                'description' => '指定されたページに移動する',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'page' => [
                            'type' => 'string',
                            'description' => '移動先のページ名',
                            'enum' => ['ホーム', '倫理確認システム', 'カードチェック', '申請フォーム', '申請履歴']
                        ]
                    ],
                    'required' => ['page']
                ]
            ],
            [
                'name' => 'getApplicationHistory',
                'description' => 'ユーザーの申請履歴を取得する',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'status' => [
                            'type' => 'string',
                            'description' => 'フィルターするステータス（承認待ち、承認済み、差し戻し）',
                            'enum' => ['all', '承認待ち', '承認済み', '差し戻し']
                        ],
                        'limit' => [
                            'type' => 'integer',
                            'description' => '取得する件数',
                            'default' => 10
                        ]
                    ],
                    'required' => []
                ]
            ],
            [
                'name' => 'getFileUploadGuidelines',
                'description' => 'ファイルアップロードのガイドラインを取得する',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'fileType' => [
                            'type' => 'string',
                            'description' => 'ファイルタイプ（PDF、動画、元素材）',
                            'enum' => ['PDF', '動画', '元素材', 'all']
                        ]
                    ],
                    'required' => []
                ]
            ],
            [
                'name' => 'checkEthicsGuidelines',
                'description' => '倫理確認のガイドラインと基準を取得する',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'category' => [
                            'type' => 'string',
                            'description' => '倫理カテゴリ（文書、製品、広告など）'
                        ]
                    ],
                    'required' => []
                ]
            ],
            [
                'name' => 'getEthicsManual',
                'description' => 'AIC倫理確認システムの詳細マニュアル情報を取得する（メンバー向け）',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'section' => [
                            'type' => 'string',
                            'description' => 'マニュアルのセクション',
                            'enum' => ['overview', 'getting_started', 'application_process', 'document_guidelines', 'evaluation_criteria', 'common_issues', 'troubleshooting', 'faq', 'contact']
                        ],
                        'user_role' => [
                            'type' => 'string',
                            'description' => 'ユーザーの役割',
                            'enum' => ['member', 'supporter', 'admin'],
                            'default' => 'member'
                        ]
                    ],
                    'required' => []
                ]
            ],
            [
                'name' => 'getCardCheckInstructions',
                'description' => 'カードチェックの手順と要件を取得する',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'checkType' => [
                            'type' => 'string',
                            'description' => 'チェックタイプ（仕様確認、エラー修正、結果確認）'
                        ]
                    ],
                    'required' => []
                ]
            ]
        ];
    }
}