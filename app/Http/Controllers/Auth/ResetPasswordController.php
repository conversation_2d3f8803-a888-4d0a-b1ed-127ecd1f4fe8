<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\UserRegisteredMail;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use App\Rules\Password;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Inertia\Inertia;
use Illuminate\Support\Facades\Hash;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;

    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    public function showResetForm(User $user)
    {
        if (!request()->hasValidSignature()) {
            return abort(403, "無効なリンクですので管理者にお問合せしてください");
        }
        
        return Inertia::render('Auth/PasswordReset', [
            'user' => $user
        ]);
    }

    public function reset(User $user)
    {
        try {
            request()->validate(
                $this->rules($user),
                $this->validationErrorMessages()
            );

            $user->password = bcrypt(request()->password);
            $user->email_verified_at = now();
            $user->save();

            return redirect()->back()
                             ->with('snackbar', [
                                'color' => 'primary',
                                'icon' => 'mdi-check-circle',
                                'title' => '成功',
                                'message' => 'パスワードを変更しました。'
                             ]);
        } catch (\Throwable $th) {
            return redirect()->back()
                             ->with([
                                'snackbar' =>  [
                                    'color' => 'error',
                                    'icon' => 'mdi-alert-circle',
                                    'title' => '失敗',
                                    'message' => 'エラーが発生しました。'
                                ],
                                'errors' => $th->errors(),
                            ]);
        }
    }

    public function sendEmail(User $receiver, $isInitPassword = false)
    {
        return Mail::to($receiver)
                ->send(new UserRegisteredMail($receiver, $isInitPassword));
    }

    /**
     * Get the password reset validation rules.
     *
     * @return array
     */
    protected function rules(User $user)
    {
        return [
            'email' => [
                'required',
                'email',
                'exists:users,email'
            ],
            'password' => [
                'required',
                'confirmed',
                'min:8',
                new Password($user)
            ],
        ];
    }
    
    protected function validationErrorMessages()
    {
        return [
            'email.exists' => '存在しないメールアドレスです。',
            'email.required' => 'メールアドレスは必須です。',
            'email.email' => 'メールアドレスフォーマットではありません。',
            'password.required' => 'パスワードは必須です。',
            'password.confirm' => 'パスワードが一致していません。',
            'password.min' => '8文字以上にしてください。',
        ];
    }
}
