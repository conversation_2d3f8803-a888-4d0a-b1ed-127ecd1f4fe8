<template>
  <v-overlay absolute v-if="reviewFiles.length">
    <UploadReviewDetailError :dialog.sync="detailDialog" :file="file" />

    <!-- Header -->
    <v-card flat light class="mx-auto">
      <v-card-title color="primary">
        <v-icon>mdi-file-outline</v-icon>
        ファイルチェック ({{ reviewFiles.length }})
      </v-card-title>

      <v-divider></v-divider>

      <!-- Body -->
      <v-card-text class="pa-0 files-list-wrapper">
        <v-list three-line>
          <v-list-item
            v-for="(file, index) in reviewFiles"
            :key="index"
            v-on="onlyRejectFileClickEvent(file)"
          >
            <v-list-item-avatar>
              <v-icon class="mdi-36px" color="grey lighten-1">
                {{ icons[file.extension] || 'mdi-file' }}
              </v-icon>
            </v-list-item-avatar>
            <v-list-item-content>
              <!-- ファイル名 -->
              <v-list-item-title>
                <v-tooltip top>
                  <template v-slot:activator="{ on, attrs }">
                    <span v-bind="attrs" v-on="on">{{ file.name }}</span>
                  </template>

                  <span>{{ file.name }}</span>
                </v-tooltip>
              </v-list-item-title>

              <!-- ファイル情報 -->
              <v-list-item-subtitle>
                {{ formatBytes(file.size) }} -
                {{ file.last_modified | moment('YYYY-MM-DD HH:mm:ss') }}
              </v-list-item-subtitle>

              <!-- エラーメッセージ -->
              <v-list-item-subtitle
                v-if="isRejectFile(file)"
                class="error-item"
              >
                {{ file.error }}
              </v-list-item-subtitle>
            </v-list-item-content>

            <v-list-item-action>
              <v-icon v-if="isRejectFile(file)" color="error">
                mdi-close
              </v-icon>
              <v-icon v-else color="success">
                mdi-check
              </v-icon>
            </v-list-item-action>
          </v-list-item>
        </v-list>
      </v-card-text>

      <v-divider></v-divider>
      <v-toolbar dense flat>
        <div class="grow"></div>
        <v-btn @click="close" class="mx-1">閉じる</v-btn>
      </v-toolbar>
    </v-card>
  </v-overlay>
</template>

<script>
import UploadReviewDetailError from './UploadReviewDetailError';
import { formatBytes } from './util';

export default {
  props: {
    icons: Object,
    handler: Object,
    maxUploadFilesCount: { type: Number, default: 0 },
    maxUploadFileSize: { type: Number, default: 0 },
  },
  components: {
    UploadReviewDetailError,
  },
  computed: {
    reviewFiles: {
      get() {
        return this.handler.uploadedFiles;
      },
      set() {
        this.handler.uploadedFiles = [];
      },
    },
  },

  data() {
    return {
      detailDialog: false,
      file: {},
    };
  },
  methods: {
    formatBytes,

    close() {
      this.reviewFiles = [];
    },
    isRejectFile(file) {
      return Boolean(file.error);
    },

    onlyRejectFileClickEvent(file) {
      return this.isRejectFile(file)
        ? {
            click: () => this.showDetailError(file),
          }
        : {};
    },

    showDetailError(file) {
      this.detailDialog = true;
      this.file = file;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .v-overlay__content {
  width: 90%;
  max-width: 900px;
  .files-list-wrapper {
    max-height: 250px;
    overflow-y: auto;
  }
}

.error-item {
  color: red;
}
</style>
