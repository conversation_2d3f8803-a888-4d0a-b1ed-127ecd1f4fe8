<!-- <template>
    <div class="comments-sidebar">
        <div class="sidebar-header">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="検索..." />
                <i class="fas fa-search search-icon"></i>
            </div>
            <h3>コメント ({{ comments.length }})</h3>
            <div class="sort-container">
                <span>並び替え:</span>
                <select class="sort-select" v-model="sortMethod" @change="sortComments">
                    <option value="page">ページ</option>
                    <option value="time">日付</option>
                    <option value="author">投稿者</option>
                </select>
            </div>
            <div class="page-info" v-if="currentPageInfo">
                <span>ページ {{ currentPageInfo }}</span>
            </div>
        </div>
        <div class="comments-list">
            <div v-if="comments.length === 0" class="empty-state">
                <p>コメントがありません</p>
            </div>
            <div v-for="comment in getMainComments()" :key="comment.id" class="comment-item"
                :class="{ active: selectedCommentId === comment.id }" @click="$emit('select-comment', comment.id)">
                <div class="comment-marker" :style="{ backgroundColor: getAnnotationColor(comment.annotationId) }"></div>
                <div class="comment-content">
                    <div class="comment-header">
                        <div class="comment-info">
                            <div class="comment-author">{{ comment.author }}</div>
                            <div class="comment-time">{{ comment.timestamp }}</div>
                            <div v-if="comment.page" class="comment-page">p.{{ comment.page }}</div>
                            <div v-if="comment.annotationId" class="comment-number" 
                                 :style="{ backgroundColor: getAnnotationColor(comment.annotationId) }">
                                {{ getAnnotationNumber(comment.annotationId) }}
                            </div>
                        </div>
                        <div class="comment-actions">
                            <button class="comment-sub-button" @click.stop="editComment(comment)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="comment-sub-button" @click.stop="$emit('delete-comment', comment.id)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div v-if="comment.editing" class="comment-edit">
                        <textarea v-model="comment.editText" @input="updateCommentText(comment)"
                            @keydown="handleTextareaKeydown($event, comment)"></textarea>
                        <div class="edit-actions">
                            <button class="cancel-btn" @click.stop="cancelEdit(comment)">キャンセル</button>
                            <button class="save-btn" @click.stop="saveEdit(comment)">保存</button>
                        </div>
                    </div>
                    <div v-else class="comment-text">
                        {{ comment.text }}
                    </div>
                    <div v-if="getReplies(comment.id).length > 0" class="replies-container">
                        <div v-for="reply in getReplies(comment.id)" :key="reply.id" class="reply-item">
                            <div class="reply-content">
                                <div class="reply-header">
                                    <div class="comment-author">{{ reply.author }}</div>
                                    <div class="comment-time">{{ reply.timestamp }}</div>
                                    <div class="more-options">
                                        <button class="comment-sub-button" @click.stop="editComment(reply)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="comment-sub-button"
                                            @click.stop="$emit('delete-reply', reply.id)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div v-if="reply.editing" class="comment-edit">
                                    <textarea v-model="reply.editText" @input="updateCommentText(reply)"
                                        @keydown="handleTextareaKeydown($event, reply)"></textarea>
                                    <div class="edit-actions">
                                        <button class="cancel-btn" @click.stop="cancelEdit(reply)">キャンセル</button>
                                        <button class="save-btn" @click.stop="saveEdit(reply)">保存</button>
                                    </div>
                                </div>
                                <div v-else class="comment-text">
                                    {{ reply.text }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="comment.text && comment.text.trim()" class="reply-form-container">
                        <div class="reply-form">
                            <input type="text" class="reply-input" placeholder="コメントする..."
                                v-model="replyText[comment.id]" @keyup.enter="addReply(comment)"
                                :id="`reply-input-${comment.id}`" />
                            <button class="send-btn" @click.stop="addReply(comment)">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "CommentsSidebar",
    props: {
        comments: {
            type: Array,
            default: () => []
        },
        annotations: {
            type: Array,
            default: () => []
        },
        selectedCommentId: {
            type: [Number, String],
            default: null
        }
    },
    data() {
        return {
            replyText: {},
            sortMethod: 'page',
            sortedComments: [],
            annotationColors: [
                { value: 'red', label: '赤' },
                { value: 'blue', label: '青' },
                { value: 'green', label: '緑' },
                { value: 'orange', label: 'オレンジ' },
                { value: 'purple', label: '紫' }
            ]
        };
    },
    computed: {
        currentPageInfo() {
            if (this.annotations.length === 0) return '';
            const commentPages = new Set();
            this.comments.forEach(comment => {
                if (comment.page && comment.id === comment.parentId) {
                    commentPages.add(comment.page);
                }
            });
            const pageArray = Array.from(commentPages).sort((a, b) => a - b);
            if (pageArray.length === 0) return '';
            if (pageArray.length === 1) return pageArray[0];
            return `${Math.min(...pageArray)}-${Math.max(...pageArray)}`;
        }
    },
    methods: {
        sortComments() {
            this.sortedComments = [...this.comments];
        },
        getMainComments() {
            console.log("getting main comments");
            const mainComments = this.comments.filter((comment) => comment.id === comment.parentId);
            if (this.sortMethod === 'page') {
                return mainComments.sort((a, b) => {
                    const aPage = a.page || 1;
                    const bPage = b.page || 1;
                    
                    if (aPage !== bPage) {
                        return aPage - bPage;
                    }
                    const aAnnotation = this.annotations.find(ann => ann.id === a.annotationId);
                    const bAnnotation = this.annotations.find(ann => ann.id === b.annotationId);
                    const aNumber = aAnnotation ? aAnnotation.number : 0;
                    const bNumber = bAnnotation ? bAnnotation.number : 0;
                    return aNumber - bNumber;
                });
            } else if (this.sortMethod === 'time') {
                return mainComments.sort((a, b) => {
                    const aTimeParts = a.timestamp.split(', ')[1].split(':');
                    const bTimeParts = b.timestamp.split(', ')[1].split(':');
                    const aTimeValue = parseInt(aTimeParts[0]) * 60 + parseInt(aTimeParts[1]);
                    const bTimeValue = parseInt(bTimeParts[0]) * 60 + parseInt(bTimeParts[1]);
                    return bTimeValue - aTimeValue; 
                });
            } else if (this.sortMethod === 'author') {
                return mainComments.sort((a, b) => {
                    return a.author.localeCompare(b.author);
                });
            }
            return mainComments;
        },
        getReplies(commentId) {
            console.log("checking replies...");
            return this.comments.filter((comment) =>comment.parentId !== comment.id && comment.parentId === commentId);
        },
        getAnnotationNumber(annotationId) {
            const annotation = this.annotations.find((a) => a.id === annotationId);
            return annotation ? annotation.number : "";
        },
        getAnnotationColor(annotationId) {
            if (!annotationId) return 'red';
            const annotation = this.annotations.find(a => a.id === annotationId);
            return annotation ? annotation.color || 'red' : 'red';
        },
        updateAnnotationColor(annotationId, color) {
            this.$emit('update-annotation-color', annotationId, color);
        },
        editComment(comment) {
            comment.editing = true;
            comment.editText = comment.text;
            comment.showOptions = false;
            this.$emit("update-comment", comment);
        },
        updateCommentText(comment) {
            const updatedComment = { ...comment, text: comment.editText };
            this.$emit("update-comment", updatedComment);
        },
        saveEdit(comment) {
            comment.text = comment.editText;
            comment.editing = false;
            this.$emit("update-comment", comment);
        },
        cancelEdit(comment) {
            comment.editing = false;
            this.$emit("update-comment", comment);
        },
        handleTextareaKeydown(event, comment) {
            if (event.key === "Enter" && !event.shiftKey) {
                event.stopPropagation();
                event.preventDefault();
                this.saveEdit(comment);
            }
        },
        toggleReply(comment) {
            if (!this.replyText[comment.id]) {
                this.replyText[comment.id] = "";
            }
            this.$nextTick(() => {
                const inputField = document.querySelector(`#reply-input-${comment.id}`);
                if (inputField) inputField.focus();
            });
        },
        addReply(comment) {
            const replyContent = this.replyText[comment.id];
            console.log("CALLING ADD REPLY AEAEA");
            if (replyContent && replyContent.trim()) {
                this.$emit("add-reply", {
                    text: replyContent,
                    annotationId: comment.annotationId,
                    parentId: comment.id
                });
                this.replyText[comment.id] = "";
            }
        }
    },
    mounted() {
        this.sortComments();
    },
    watch: {
        comments: {
            handler() {
                this.sortComments();
            },
            deep: true
        }
    }
};
</script>

<style lang="scss" scoped>
.comments-sidebar {
    width: 300px;
    border-left: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.sidebar-header {
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;

    h3 {
        margin: 10px 0;
        font-size: 16px;
    }
}

.search-container {
    position: relative;
    margin-bottom: 10px;

    .search-input {
        width: 100%;
        padding: 8px 30px 8px 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    .search-icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
    }
}

.sort-container {
    display: flex;
    align-items: center;
    margin: 10px 0;

    span {
        margin-right: 8px;
        font-size: 14px;
    }

    .sort-select {
        padding: 4px 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
}

.page-info {
    margin: 10px 0;
    font-size: 14px;
    color: #666;
}

.comments-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;
    color: #666;

    p {
        margin: 5px 0;
    }
}

.comment-item {
    display: flex;
    position: relative;
    padding-top: 20px;
    margin-bottom: 15px;
    border-radius: 4px;
    background-color: #f9f9f9;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-left: 4px solid transparent;

    &:hover {
        background-color: #f0f0f0;
    }

    &.active {
        background-color: #e6f2ff;
    }
}

.comment-marker {
    width: 4px;
    border-radius: 4px 0 0 4px;
}

.comment-content {
    flex: 1;
    padding: 10px;
}

.comment-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 5px;
}

.comment-info {
    .comment-author {
        font-weight: bold;
        font-size: 14px;
    }

    .comment-time {
        font-size: 12px;
        color: #666;
    }

    .comment-page {
        font-size: 11px;
        color: #555;
        background-color: #e9e9e9;
        padding: 1px 4px;
        border-radius: 3px;
        margin-top: 2px;
        display: inline-block;
    }

    .comment-number {
        position: absolute;
        top: 5px;
        left: 5px;
        font-size: 12px;
        font-weight: bold;
        color: #ffffff;
        background: #a50000;
        padding: 2px 4px;
        border-radius: 3px;
        z-index: 5;
    }
}

.comment-actions {
    display: flex;

    .comment-sub-button,
    .more-btn {
        background: none;
        border: none;
        color: #666;
        cursor: pointer;

        &:hover {
            color: #0066cc;
        }
    }
}

.more-options {
    position: relative;
    display: flex;

    .options-menu {
        position: absolute;
        right: 0;
        top: 20px;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        z-index: 100;

        button {
            display: block;
            width: 100%;
            text-align: left;
            background: none;
            border: none;
            padding: 8px 15px;
            cursor: pointer;

            &:hover {
                background-color: #f0f0f0;
            }
        }
    }
}

.comment-text {
    font-size: 14px;
    margin-bottom: 10px;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.comment-edit {
    margin-bottom: 10px;

    textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        resize: vertical;
        min-height: 60px;
        margin-bottom: 5px;
        white-space: pre-wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
        font-family: inherit;
        font-size: 14px;
    }

    .edit-actions {
        display: flex;
        justify-content: flex-end;

        button {
            margin-left: 5px;
            padding: 4px 10px;
            border-radius: 4px;
            cursor: pointer;
        }

        .cancel-btn {
            background: none;
            border: 1px solid #ccc;
        }

        .save-btn {
            background-color: #0066cc;
            border: 1px solid #0066cc;
            color: white;
        }
    }
}

.replies-container {
    margin-left: 10px;
    margin-bottom: 10px;
    border-left: 1px solid #e0e0e0;
}

.reply-item {
    padding: 8px 0 8px 10px;

    .reply-content {
        background-color: #ffffff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 8px;
    }

    .reply-header {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        .comment-author {
            font-weight: bold;
            font-size: 13px;
            margin-right: 8px;
        }

        .comment-time {
            font-size: 11px;
            color: #666;
            flex-grow: 1;
        }

        .more-options {
            display: flex;

            .comment-sub-button{
                background: none;
                border: none;
                color: #666;
                cursor: pointer;

                &:hover {
                    color: #0066cc;
                }	 
            }
        }
    }

    .comment-text {
        font-size: 13px;
        margin-bottom: 5px;
    }
}

.reply-form-container {
    margin-top: 10px;
    border-top: 1px solid #f0f0f0;
    padding-top: 8px;
}

.reply-form {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 2px 10px 2px 15px;

    .reply-input {
        flex: 1;
        border: none;
        background: transparent;
        padding: 8px 0;
        outline: none;
        font-size: 13px;
    }

    .send-btn {
        background: none;
        border: none;
        color: #0066cc;
        cursor: pointer;
        padding: 5px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            color: #004499;
        }

        i {
            font-size: 14px;
        }
    }
}
</style> -->