<?php

namespace App\Actions\Proof;

use App\Enums\ProofNotificationTypeEnum;
use App\Mail\ProofUpdateInternalDeadlineAtToAdminUserMail;
use App\Mail\ProofUpdateInternalDeadlineAtToNotRequiredUserMail;
use App\Mail\ProofUpdateInternalDeadlineAtToRequiredUserMail;
use App\Models\Proof;
use App\Models\User;
use App\Notifications\ProofNotification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class ChangeProofInternalDeadlineAt
{
    public function execute(Proof $proof, Carbon $afterInternalDeadlineAt): void
    {
        if ($this->isNotChanable($proof, $afterInternalDeadlineAt)) {
            return;
        }

        $this->getAdmin()->each(
            fn(User $user) => Mail::to($user)->send(new ProofUpdateInternalDeadlineAtToAdminUserMail(
                proof: $proof,
                receiver: $user,
                before: $proof->internal_deadline_at?->format('Y年n月j日 H時i分') ?? '',
                after: $afterInternalDeadlineAt->format('Y年n月j日 H時i分'),
            ))
        );

        $spUsers = $this->getRequiredUsersInProof($proof);
        $spUsers = $spUsers->filter(function (User $user) {
            return $user->type->id == User::USER_TYPE_MEMBER;
        });
        $spUsers->each(
            fn(User $user) => Mail::to($user)->send(new ProofUpdateInternalDeadlineAtToRequiredUserMail(
                proof: $proof,
                receiver: $user,
                before: $proof->internal_deadline_at?->format('Y年n月j日 H時i分') ?? '',
                after: $afterInternalDeadlineAt->format('Y年n月j日 H時i分'),
            ))
        );

        $users = $this->getNotRequiredUsersInProof($proof);
        $users->each(
            fn(User $user) => Mail::to($user)->send(new ProofUpdateInternalDeadlineAtToNotRequiredUserMail(
                proof: $proof,
                before: $proof->internal_deadline_at?->format('Y年n月j日 H時i分') ?? '',
                after: $afterInternalDeadlineAt->format('Y年n月j日 H時i分'),
                receiver: $user,
            ))
        );
        // Mail::to($users)->send(new ProofUpdateInternalDeadlineAtToNotRequiredUserMail(
        //     proof: $proof,
        //     before: $proof->internal_deadline_at?->format('Y年n月j日 H時i分') ?? '',
        //     after: $afterInternalDeadlineAt->format('Y年n月j日 H時i分'),
        // ));

        Notification::send(
            $proof->members,
            new ProofNotification($proof, ProofNotificationTypeEnum::Update)
        );

        $proof->internal_deadline_at = $afterInternalDeadlineAt;
        $proof->save();
    }

    private function getRequiredUsersInProof(Proof $proof)
    {
        return User::query()->requiredInProof($proof)->get();
    }

    private function getNotRequiredUsersInProof(Proof $proof)
    {
        return User::query()->notRequiredInProof($proof)->get();
    }

    private function getAdmin()
    {
        return User::query()->admin()->get();
    }

    /**
     * $afterInternalDeadlineAtと$proof->internal_deadline_atが同じであり、
     * ProofStatus::STATUS_ASSIGNMENTまたはProofStatus::STATUS_PROGRESSINGでない場合、変更不可
     *
     * @param Proof $proof
     * @param Carbon $afterInternalDeadlineAt
     *
     * @return bool
     */
    private function isNotChanable(Proof $proof, Carbon $afterInternalDeadlineAt): bool
    {
        if (is_null($proof->internal_deadline_at)) {
            return false;
        }

        return $proof->internal_deadline_at->eq($afterInternalDeadlineAt);
    }
}
