<?php

namespace App\Actions\Annotations;

use App\Helpers\XmlHelper as XML;
use SimpleXMLElement;

class ModifyAnnotationsExtractAndMergeAction
{
    public function execute(SimpleXMLElement $xml, array $annotations): array
    {
        return collect($annotations)
                ->map(
                    fn (string $annotation) =>
                    $this->replaceAnnotation(
                        $xml->modify->children(),
                        $annotation
                    )
                )
                ->toArray();
    }

    private function replaceAnnotation(SimpleXMLElement $children, string $annotation): string
    {
        if ($found = $this->findAnnotation($children, $annotation)) {
            return $found->asXML();
        }

        return $annotation;
    }

    private function findAnnotation(SimpleXMLElement $children, string $annotation)
    {
        return XML::find($children, function (SimpleXMLElement $child) use ($annotation) {
            return XML::getName($child) == XML::getName($annotation);
        });
    }
}
